package cn.savas.hub.module.infra.api.file;

/**
 * 文件 API 接口
 *
 * <AUTHOR>
 */
public interface FileApi {

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(byte[] content) {
        return createFile(null, null, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(String path, byte[] content) {
        return createFile(null, path, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param name 文件名称
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    String createFile(String name, String path, byte[] content);

    /**
     * 获取文件内容
     * @param path
     * @return
     * @throws Exception
     */
    byte[] getFileContent(String path) throws Exception;

    /**
     * 删除文件-根据路径
     */
    void deleteFile(String path) throws Exception;

    /**
     * 文件保存到本地,并返回文件的访问缓存路径
     * @param url
     * @return
     * @throws Exception
     */
    String saveAndGetCacheUri(String url) throws Exception;

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param path 文件路径
     * @return 文件路径
     */
    String createFile(String path);

    /**
     * 获取本地文件
     * @param path
     * @return
     */
    byte[] getLocalFile(String path);

    /**
     * 获取本地文件路径-根据S3路径
     * @param path
     * @return
     * @throws Exception
     */
    String getFileLocalByPath(String path);
}
