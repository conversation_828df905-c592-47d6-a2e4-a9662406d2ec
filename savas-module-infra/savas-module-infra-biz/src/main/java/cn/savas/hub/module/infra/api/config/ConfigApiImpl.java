package cn.savas.hub.module.infra.api.config;

import cn.savas.hub.module.infra.dal.dataobject.config.ConfigDO;
import cn.savas.hub.module.infra.service.config.ConfigService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 参数配置 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConfigApiImpl implements ConfigApi {

    @Resource
    private ConfigService configService;

    @Override
    public String getConfigValueByKey(String key) {
        ConfigDO config = configService.getConfigByKey(key);
        return config != null ? config.getValue() : null;
    }

    @Override
    public Boolean getConfigVisibleByKey(String key) {
        ConfigDO config = configService.getConfigByKey(key);
        return config != null ? config.getVisible() : null;
    }

}
