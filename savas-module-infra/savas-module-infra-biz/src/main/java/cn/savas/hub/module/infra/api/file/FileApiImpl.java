package cn.savas.hub.module.infra.api.file;

import cn.hutool.core.io.FileUtil;
import cn.savas.hub.module.infra.service.file.FileService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.File;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.infra.enums.ErrorCodeConstants.FILE_NOT_EXISTS;

/**
 * 文件 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FileApiImpl implements FileApi {

    @Resource
    private FileService fileService;

    @Override
    public String createFile(String name, String path, byte[] content) {
        return fileService.createFile(name, path, content);
    }

    @Override
    public byte[] getFileContent(String path) throws Exception {
        return fileService.getFileContent(path);
    }

    @Override
    public void deleteFile(String path) throws Exception {
        fileService.deleteFile(path);

    }

    @Override
    public String saveAndGetCacheUri(String url) throws Exception {
        byte[] data = fileService.getFileContent(url);
        String rootPath = System.getProperty("java.io.tmpdir");
        String filePath = rootPath + "/" + url;
        File file = FileUtil.writeBytes(data, filePath);
        return file.getAbsoluteFile().getPath();
    }

    @Override
    public String createFile(String path) {
        byte[] localFileByte = fileService.getLocalFile(path);
        return createFile(null, null, localFileByte);
    }

    @Override
    public byte[] getLocalFile(String path) {
        return fileService.getLocalFile(path);
    }

    @Override
    public String getFileLocalByPath(String path) {
        try {
            byte[] fileContent = fileService.getFileContent(path);
            String rootPath = System.getProperty("java.io.tmpdir");
            String filePath = rootPath + "/" + path;
            File file = FileUtil.writeBytes(fileContent, filePath);
            return file.getAbsoluteFile().getPath();
        } catch (Exception e) {
            throw exception(FILE_NOT_EXISTS);
        }
    }

}
