package cn.savas.hub.module.collect.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/20 16:54
 */
@Data
public class PrjProjectCostDTO {
    /**
     * 费用ID
     */
    private Long feeId;
    /**
     * 父级费用ID
     */
    private Long feePid;
    /**
     * 主模型ID
     */
    private Long feeHostmodel;
    /**
     * 费用名称
     */
    private String feeName;
    /**
     * 费用代码
     */
    private String feeCode;
    /**
     * 费用样式
     */
    private Integer feeStyle;
    /**
     * 参数
     */
    private byte[] feeParameters;
    /**
     * 状态
     */
    private Long feeState;
    /**
     * 费用序列
     */
    private String feeSequence;

    private Integer feeSortid;
    /**
     * 表达式依据
     */
    private String feeExpressionbasis;
    /**
     * 费用表达式
     */
    private String feeExpression;
    /**
     * 税率
     */
    private BigDecimal feeTaxrate;
    /**
     * 比率
     */
    private BigDecimal feeRate;
    /**
     * 费用描述
     */
    private String feeDescription;

    private Long projectId;
}
