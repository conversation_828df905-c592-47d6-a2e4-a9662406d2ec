package cn.savas.hub.module.collect.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/22 15:35
 */
@Data
public class PrjNormDTO {
    private Long normId;
    private Long normPid;
    private Long normHostmodel;
    private String normCode;
    private String normName;
    private String normExpense;
    private String normUnit;
    private String normNaturalunit;
    private String normExpression;
    private BigDecimal normAmount;
    private Integer normSystemid;
    private Long normChapterid;
    private BigDecimal normWeight;
    private String normSpecialcode;
    private Integer normNormruletag;
    private String normCompositions;
    private String normQuantitystring;
    private String normPricestring;
    private BigDecimal normRate;
    private String normAdjuststring;
    private String normSystemcode;
    private String normSystemunit;
    private Long normMainid;
    private Integer normMark;
    private String normStandard;
    private String normStatistics;
    private String normCbscode;
    private String normContent;
    private String normEffects;
    private Integer normSortid;
    private Long normState;
    private byte[] normParameters;
    private String NormEffectchapterscope;
    private String NormEffectnormscope;
    private String NormExcludechapter;
    private String NormExcludeburl;
    private String NormBurl;
    private Long NormExcludetag;
    private String NormInformation;
    private Long NormCalculatetag;
    private BigDecimal NormUnitsquotiety;
    private String NormUnitengcode;
    private Integer NormColor;
    private Integer normStatus;
    private String NormMaterial;
    private Integer NormEffectkind;
    private String NormEffectnorm;
    private Integer NormStyle;
    private Long NormOriginalamount;
    private String NormOriginalexpression;
    private String NormPlacecode;
    private BigDecimal NormDegisnrate;
    private BigDecimal NormSuperviserate;
    private BigDecimal NormTestrate;
    private Long NormDesignconditionhash;
    private BigDecimal NormTariffrate;
    private String NormProfessionalcode;
    private BigDecimal NormPipeditchcoefficient;
}
