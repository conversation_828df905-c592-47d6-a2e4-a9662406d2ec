package cn.savas.hub.module.collect.event;

import lombok.Data;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2025/5/9 14:47
 */
@SuppressWarnings("ALL")
@Data
public class SumFilePackageCallbackEvent extends ApplicationEvent {
    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 新项目db文件绝对路径
     */
    private String dbPath;


    public SumFilePackageCallbackEvent(Object source) {
        super(source);
    }

}
