package cn.savas.hub.module.collect.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/12 12:14
 */
@Data
public class OtherFeeDTO {
    /**
     * 费用ID
     */
    private Long feeId;

    /**
     * 父级费用ID
     */
    private Long feePid;

    /**
     * 主模型ID
     */
    private Long feeHostmodel;

    /**
     * 费用序列
     */
    private String feeSequence;

    /**
     * 费用名称
     */
    private String feeName;

    /**
     * 费用代码
     */
    private String feeCode;

    /**
     * 费用标签
     */
    private String feeCosttag;

    /**
     * 费用值
     */
    private BigDecimal feeValue;

    /**
     * 外部费用值
     */
    private BigDecimal feeForeignvalue;

    /**
     * 费用表达式
     */
    private String feeExpression;

    /**
     * 费用表达式代码
     */
    private String feeExpressioncode;

    /**
     * 中文表达式
     */
    private String feeCnexpression;

    /**
     * 费用说明
     */
    private String feeExpense;

    /**
     * 表达式依据
     */
    private String feeExpressionbasis;

    /**
     * 费用样式
     */
    private Integer feeStyle;

    /**
     * 费用顺序
     */
    private Integer feeOrder;

    /**
     * 费用描述
     */
    private String feeDescription;

    /**
     * 排序ID
     */
    private Integer feeSortid;

    /**
     * 税率
     */
    private BigDecimal feeTaxrate;

    /**
     * 税率代码
     */

    private String feeTaxratecode;

    /**
     * 比率
     */
    private BigDecimal feeRate;

    /**
     * 比率代码
     */
    private String feeRatecode;

    /**
     * 统计
     */
    private String feeStatistics;

    /**
     * 原始ID
     */
    private Long feeOrginalid;

    /**
     * 上下文
     */
    private Integer feeContext;

    /**
     * 状态
     */
    private Long feeState;

    /**
     * 计算依据
     */
    private String feeCalculatorbasis;

    /**
     * 参数
     */
    private byte[] feeParameters;
}
