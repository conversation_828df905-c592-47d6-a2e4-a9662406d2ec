package cn.savas.hub.module.collect.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/9 10:56
 */
@Data
public class PrjProjectEngDTO {
    /**
     * 工程ID
     */
    private Long engId;

    /**
     * 项目ID
     */
    private Long engProjectid;

    /**
     * 原始ID
     */
    private Long engOriginalid;

    /**
     * 工程代码
     */
    private String engCode;

    /**
     * 工程名称
     */
    private String engName;

    /**
     * 阶段
     */
    private String engPhase;

    /**
     * 版本
     */
    private String engEdition;

    /**
     * 编译版本
     */
    private Integer engCompileversion;

    /**
     * 同步版本
     */
    private Integer engSyncversion;

    /**
     * 状态
     */
    private Long engState;

    /**
     * 样式
     */
    private Integer engStyle;

    /**
     * 用户ID
     */
    private Long engUserid;

    /**
     * 用户名
     */
    private String engUsername;

    /**
     * 日期
     */
    private BigDecimal engDate;

    /**
     * 主管ID
     */
    private Long engDirectorid;

    /**
     * 主管姓名
     */
    private String engDirectorname;

    /**
     * 规则
     */
    private String engRule;
    /**
     * 参数
     */
    private byte[] engParameters;
}
