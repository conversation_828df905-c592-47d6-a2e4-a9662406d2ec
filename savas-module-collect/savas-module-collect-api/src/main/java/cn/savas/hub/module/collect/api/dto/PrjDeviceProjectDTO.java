package cn.savas.hub.module.collect.api.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:31
 */
@Data
public class PrjDeviceProjectDTO {
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 设计阶段
     */
    private String designStage;
    /**
     * 价格水平
     */
    private String priceLevel;

    /**
     * 美元汇率
     */
    private Double currencyRate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
