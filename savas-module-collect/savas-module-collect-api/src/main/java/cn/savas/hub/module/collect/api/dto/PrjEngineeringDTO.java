package cn.savas.hub.module.collect.api.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/20 17:39
 */
@Data
public class PrjEngineeringDTO {
    /**
     * 工程ID
     */
    private Long engId;

    /**
     * 父工程ID
     */
    private Long engPid;

    /**
     * 原始工程ID
     */
    private Long engOrginalid;

    /**
     * 工程分类
     */
    private Long engClass;

    /**
     * 工程代码
     */
    private String engCode;

    /**
     * 工程名称
     */
    private String engName;

    /**
     * 特殊代码
     */
    private String engSpecialcode;

    /**
     * 用户ID
     */
    private Integer engUserid;

    /**
     * 用户名称
     */
    private String engUsername;

    /**
     * 工程日期
     */
    private Float engDate;

    /**
     * 负责人ID
     */
    private Long engDirectorid;

    /**
     * 负责人名称
     */
    private String engDirectorname;

    /**
     * 排序ID
     */
    private Integer engSortid;

    /**
     * 编译版本
     */
    private Integer engCompileversion;

    /**
     * 同步版本
     */
    private Integer engSyncversion;

    /**
     * 工程状态
     */
    private Long engState;

    /**
     * 表达式
     */
    private String engExpression;

    /**
     * 参数 (Blob类型)
     */
    private byte[] engParameters;
}
