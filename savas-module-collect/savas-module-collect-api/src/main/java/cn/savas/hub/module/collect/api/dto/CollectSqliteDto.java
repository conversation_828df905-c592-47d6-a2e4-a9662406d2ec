package cn.savas.hub.module.collect.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class CollectSqliteDto {

    /**
     * 公共库 绝对地址(全路径地址)
     */
    @NotEmpty(message = "公共库文件")
    private String mainFilePath;

    /**
     * 公共库 db
     */
    private String mainDB;

    /**
     * 新库地址(全路径地址)
     */
    @NotEmpty(message = "新库文件")
    private String slaveFilePath;

    /**
     * 新文件 db
     */
    private String slaveDB;


    @Size(min = 1, message = "wbsIds 不能为空")
    private List<Long> wbsIds;

    /**
     * 删除新库文件
     * 默认true
     */
    private Boolean delSlaveFile = false;

    /**
     * 重新计算
     * 默认true
     */
    private Boolean summeryCost = true;

    /**
     * 包主键
     */
    private Long projectId;


}
