package cn.savas.hub.module.collect.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 其他工程费DTO
 * <AUTHOR>
 * @date 2025/5/12 10:51
 */
@Data
public class OtherEngFeeDTO {
    /**
     * 费用ID
     */
    private Long feeId;

    /**
     * 父费用ID
     */
    private Long feePid;

    /**
     * 主模型ID
     */
    private Long feeHostmodel;

    /**
     * 序列
     */
    private String feeSequence;

    /**
     * 费用名称
     */
    private String feeName;

    /**
     * 费用代码
     */
    private String feeCode;

    /**
     * 费用标签
     */
    private String feeCosttag;

    /**
     * 费用值
     */
    private BigDecimal feeValue;

    /**
     * 表达式
     */
    private String feeExpression;

    /**
     * 表达式代码
     */
    private String feeExpressioncode;

    /**
     * 中文表达式
     */
    private String feeCnexpression;

    /**
     * 费用说明
     */
    private String feeExpense;

    /**
     * 样式
     */
    private Integer feeStyle;

    /**
     * 排序
     */
    private Integer feeOrder;

    /**
     * 描述
     */
    private String feeDescription;

    /**
     * 排序ID
     */
    private Integer feeSortid;

    /**
     * 税率
     */
    private BigDecimal feeTaxrate;

    /**
     * 税率代码
     */
    private String feeTaxratecode;

    /**
     * 统计信息
     */
    private String feeStatistics;

    /**
     * 费率
     */
    private BigDecimal feeRate;

    /**
     * 费率代码
     */
    private String feeRatecode;

    /**
     * 原始ID
     */
    private Long feeOrginalid;

    /**
     * 上下文
     */
    private Integer feeContext;

    /**
     * 状态
     */
    private Long feeState;

    /**
     * 参数 (Blob类型)
     */
    private byte[] feeParameters;
}
