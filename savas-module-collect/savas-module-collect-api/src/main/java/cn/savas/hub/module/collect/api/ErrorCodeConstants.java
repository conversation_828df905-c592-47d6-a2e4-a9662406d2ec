package cn.savas.hub.module.collect.api;

import cn.savas.hub.framework.common.exception.ErrorCode;

/**
 * Bpm 错误码枚举类
 * <p>
 * bpm 系统，使用 1-009-000-000 段
 */
public interface ErrorCodeConstants {


    // ========== 数据合并模块 1_010_002_000 ==========
    ErrorCode SLAVE_NOT_EXISTS = new ErrorCode(1_010_002_001, "从库文件不存在");


    // ========== 数据合并模块 1_110_002_000 ==========
    ErrorCode OTHER_CODE_ROW_NOT_EXISTS = new ErrorCode(1_110_002_000, "其他费需要计算的行不存在");
    ErrorCode PACKAGE_ERROR = new ErrorCode(1_110_002_001, "打包失败");
    ErrorCode NO_SUCH_STRATEGY = new ErrorCode(1_110_002_002, "未找到适合产品的策略");
    ErrorCode UNKNOWN_ALIAS = new ErrorCode(1_110_002_003, "未知的alias: {0}");
}
