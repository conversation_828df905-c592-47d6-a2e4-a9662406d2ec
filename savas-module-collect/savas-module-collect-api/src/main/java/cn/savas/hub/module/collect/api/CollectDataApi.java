package cn.savas.hub.module.collect.api;

import cn.savas.hub.module.collect.api.dto.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/18 18:39
 */
public interface CollectDataApi {
    /**
     * 获取wbs工程树结构
     */
    List<WbsTreeDTO> getWbsTree(Long projectId);

    /**
     * 获取工程概况
     */
    List<BudgetProjectInfoDTO> getProjectInfo(Long wbsId);

    /**
     * 获取其他费数据
     * @param type 1:工程费 2:其他费 3:项目总概算
     */
    List<PrjProjectCostDTO> getProjectCostByEngId(Long engineeringId);

    /**
     * 获取业务wbs工程数据
     */
    List<PrjEngineeringDTO> getEngineering();

    /**
     * 获取业务wbs工程数据
     */
    List<PrjEngineeringDTO> getEngineeringById(Collection<Long> engIds);

    /**
     * 获取分部数据
     */
    List<PrjSectionBillDTO> getBranch(Long engineeringId);

    /**
     * 子目查询
     */
    List<PrjNormDTO> getNorm(Long engineeringId);

    /**
     * 子目查询多条件
     */
    List<PrjNormDTO> getNormByCondition(PrjNormQueryDTO queryDTO);

    /**
     * 获取项目数据
     */
    PrjProjectEngDTO getProjectEng(Long projectId);

    /**
     * 合并项目文件
     */
    void mergeProjectFile(ClientMergeProjectFileDTO reqVO) throws Exception;

    /**
     * 计算项目数据
     */
    void calculateProjectData(CalculateReqDTO reqDTO) throws Exception;

    /**
     * 删除指的engId相关数据
     */
    void deleteDataByEngId(List<Long> engIds, String product);

    /**
     * 更新工程负责人
     */
    void updateDirectorId(Long projectId, List<PrjEngineeringDTO> items);

    /**
     * 获取分部数据
     */
    List<PrjSectionBillDTO> getDeviceSectionBillByEngId(Long projectId, Collection<Long> engIdList);

    /**
     * 获取工程费用数据
     */
    List<PrjProjectCostDTO> getDeviceProjectCostByEngId(Long projectId, Collection<Long> engIdList);

    PrjDeviceProjectDTO getDeviceProject(Long prjId);

    /**
     * 获取其他工程费
     */
    List<OtherEngFeeDTO> getProjectOtherEngFee(Long engId);

    /**
     * 获取其他费用
     */
    List<OtherFeeDTO> getProjectOtherFee(Long engId);

    /**
     * 获取分部数据
     * @param hostModelIdSet
     * @return
     */
    List<PrjSectionBillDTO> getBranchByEngId(Collection<Long> hostModelIdSet);

    /**
     * 获取分部数据
     * @return
     */
    List<PrjSectionBillDTO> getBranch();

    /**
     * 获取工程检验检测费
     */
    List<PrjTestEngineeringDTO> getTestEngineering(Long projectId);

    /**
     * 同步项目参数配置
     */
    void syncProjectParam(List<ClientPrjSettingParamDTO> reqDTO);

    /**
     * 同步项目费率配置
     */
    void syncProjectRate(List<ClientPrjSettingRateDTO> reqDTO);

    /**
     * 同步降效费用配置
     */
    void syncProjectEffectSet(String product, List<ClientPrjSettingEffectSetDTO> reqDTO);

    /**
     * 同步指标关联的降效费用配置
     */
    void syncProjectEffectMulti(String product, List<ClientPrjSettingEffectMultiDTO> reqDTO);

    /**
     * 同步项目主材系数配置
     */
    void syncProjectMainMaterial(List<ClientPrjSettingMaterialDTO> reqDTO);

    /**
     * 更改项目数据
     * @param prjProjectEngDTO
     */
    void updateProjectData(PrjProjectEngDTO prjProjectEngDTO);

}
