<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.savas.hub</groupId>
        <artifactId>savas-hub</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <modules>
        <module>savas-module-collect-api</module>
        <module>savas-module-collect-biz</module>
    </modules>
    <artifactId>savas-module-collect</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>
        collect 汇总计算 模块，主要提供两块能力：
        1. 基于sqlite 数据 汇总 迁移
        2. 基于sqlite 数据重新计算
    </description>

</project>
