package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.ByteArrayTypeHandler;

/**
 * <AUTHOR>
 * @date 2025/6/18 14:45
 */
@Data
@TableName(value ="prj_testingengineering",autoResultMap = true)
public class PrjTestingengineering {
    @TableId
    private Long engId;
    private Long engPid;
    private Long engOrginalid;
    private String engCode;
    private String engName;
    private String engSpecialcode;
    private Integer engUserid;
    private String engUsername;
    private Long engDate;
    private Long engDirectorid;
    private String engDirectorname;
    private Integer engSortid;
    private Integer engCompileversion;
    private Integer engSyncversion;
    private Long engState;
    @TableField(typeHandler = ByteArrayTypeHandler.class)
    private byte[] engParameters;
    private String engExpression;
}
