package cn.savas.hub.module.collect.framework.aspect;

import cn.savas.hub.module.collect.framework.annotations.ChangeSqliteDB;
import cn.savas.hub.module.collect.framework.db.SqliteSourceManager;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.TypedValue;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.Method;


@Aspect
@Component
public class ChangeLiteDBAspect {

    private static final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    private static final String jdbcDriver = "org.sqlite.JDBC";
    private static final String dbHeader = "jdbc:sqlite:";

    @Resource
    private SqliteSourceManager sqliteSourceManager;


    @Around("@annotation(changeSqliteDB)")
    public Object around(ProceedingJoinPoint point, ChangeSqliteDB changeSqliteDB) throws Throwable {
        String poolName = IdWorker.get32UUID();
        try {
            //解析出注解里的动态参数
            String file = getSqliteAbsolutePath(point, changeSqliteDB);
            if (!new File(file).exists()) {
                throw new RuntimeException("sqlite 文件不存在!");
            }
            //切换数据源
            sqliteSourceManager.changeDatasource(poolName, dbHeader + file, jdbcDriver);
            return point.proceed();
        } finally {
            // 方法执行完毕，清除连接
            sqliteSourceManager.removeDatasource(poolName);
        }
    }

    private String getSqliteAbsolutePath(ProceedingJoinPoint point, ChangeSqliteDB changeSqliteDB) {
        StringBuilder sb = new StringBuilder();
        // spel解析器
        ExpressionParser parser = new SpelExpressionParser();
        // spel表达式对象
        Expression expression = parser.parseExpression(changeSqliteDB.value());
        // 上下文对象
        EvaluationContext context = new MethodBasedEvaluationContext(TypedValue.NULL, resolveMethod(point),
                point.getArgs(), parameterNameDiscoverer);
        // 解析出的动态参数
        Object value = expression.getValue(context);
        sb.append(ObjectUtils.nullSafeToString(value));
        return sb.toString();

    }

    /**
     * 获取注解所在的method
     *
     * @param joinPoint
     * @return
     */
    private Method resolveMethod(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Class<?> targetClass = joinPoint.getTarget().getClass();
        return getDeclaredMethodFor(targetClass, signature.getName(),
                signature.getMethod().getParameterTypes());
    }

    private Method getDeclaredMethodFor(Class<?> clazz, String name, Class<?>... parameterTypes) {
        try {
            return clazz.getDeclaredMethod(name, parameterTypes);
        } catch (NoSuchMethodException e) {
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null) {
                return getDeclaredMethodFor(superClass, name, parameterTypes);
            }
        }
        throw new IllegalStateException("Cannot resolve target method: " + name);
    }

}
