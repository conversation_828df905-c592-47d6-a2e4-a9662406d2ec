package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/17 16:38
 */
@Data
@TableName(value ="prj_EffectFeeCoefficientMulti")
public class PrjEffectFeeCoefficientMulti {
    @TableId
    private Long fcmId;
    private Long fcmPid;
    private Long fcmHostmodel;
    private String fcmSequence;
    private String fcmCode;
    private String fcmName;
    private Double fcmValue;
    private String fcmDescription;
    private String fcmSpecialcode;
    private String fcmBook;
    private String fcmChapter;
    private String fcmBurl;
    private String fcmExcludebook;
    private String fcmExcludechapter;
    private String fcmExcludeburl;
    private Long fcmState;
    private Integer fcmSortid;
    private Integer fcmStyle;
}
