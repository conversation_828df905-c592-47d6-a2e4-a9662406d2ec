package cn.savas.hub.module.collect.dal.dataobject;

import cn.savas.hub.framework.common.client.util.V9BinaryToMapUtil;
import cn.savas.hub.framework.common.client.util.V9MapToBinaryUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.ByteArrayTypeHandler;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @TableName prj_engineering
 */
@TableName(value = "prj_engineering", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class FeeEngineering extends BaseFeeEntity {
    /**
     * 工程ID
     */
    @TableId(value = "eng_id")
    private Long engId;

    /**
     * 父工程ID
     */
    @TableField(value = "eng_pid")
    private Long engPid;

    /**
     * 原始工程ID
     */
    @TableField(value = "eng_orginalid")
    private Long engOrginalid;

    /**
     * 工程分类
     */
    @TableField(value = "eng_class")
    private Integer engClass;

    /**
     * 工程代码
     */
    @TableField(value = "eng_code")
    private String engCode;

    /**
     * 工程名称
     */
    @TableField(value = "eng_name")
    private String engName;

    /**
     * 特殊代码
     */
    @TableField(value = "eng_specialcode")
    private String engSpecialcode;

    /**
     * 用户ID
     */
    @TableField(value = "eng_userid")
    private Integer engUserid;

    /**
     * 用户名称
     */
    @TableField(value = "eng_username")
    private String engUsername;

    /**
     * 工程日期
     */
    @TableField(value = "eng_date")
    private Float engDate;

    /**
     * 负责人ID
     */
    @TableField(value = "eng_directorid")
    private Long engDirectorid;

    /**
     * 负责人名称
     */
    @TableField(value = "eng_directorname")
    private String engDirectorname;

    /**
     * 排序ID
     */
    @TableField(value = "eng_sortid")
    private Integer engSortid;

    /**
     * 编译版本
     */
    @TableField(value = "eng_compileversion")
    private Integer engCompileversion;

    /**
     * 同步版本
     */
    @TableField(value = "eng_syncversion")
    private Integer engSyncversion;

    /**
     * 工程状态
     */
    @TableField(value = "eng_state")
    private Long engState;

    /**
     * 表达式
     */
    @TableField(value = "eng_expression")
    private String engExpression;

    /**
     * 参数 (Blob类型)
     */
    @TableField(value = "eng_parameters", typeHandler = ByteArrayTypeHandler.class)
    private byte[] engParameters;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


    @Override
    public Long getId() {
        return this.engId;
    }

    @Override
    public Long getPid() {
        return this.engPid;
    }

    @Override
    public Map<String, BigDecimal> getFeeMap() {
        return V9BinaryToMapUtil.unpackData(this.engParameters);
    }

    @Override
    public Integer getEngClass() {
        return engClass;
    }

    @Override
    public void setBytes(Map<String, BigDecimal> feeMap) {
        this.engParameters= V9MapToBinaryUtil.convertToBinary(feeMap);
    }

    @Override
    public String getName() {
        return this.engName;
    }

    @Override
    public String getCode() {
        return engCode;
    }

}
