package cn.savas.hub.module.collect.test;

import cn.savas.hub.framework.common.util.io.XStreamUtil;
import cn.savas.hub.module.collect.dal.handler.dto.InterpolationFeeRuleVO;

public class TestXStream {

    public static void main(String[] args) {
        String xml=" <?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<Rules BasicExpression=\"FEE@F004\"  Multiple=\"10000\" BaseCaption=\"工程费(万元)\">\n" +
                "  <Rule>\n" +
                "    <Item Style=\"1\" X1=\"0\" Y1=\"0\" X2=\"5000\" Y2=\"25\" />\n" +
                "    <Item Style=\"1\" X1=\"5000\" Y1=\"25\" X2=\"20000\" Y2=\"58\" />\n" +
                "    <Item Style=\"1\" X1=\"20000\" Y1=\"58\" X2=\"100000\" Y2=\"88\" />\n" +
                "    <Item Style=\"1\" X1=\"100000\" Y1=\"88\" X2=\"200000\" Y2=\"132\" />\n" +
                "    <Item Style=\"1\" X1=\"200000\" Y1=\"132\" X2=\"500000\" Y2=\"233\" />\n" +
                "    <Item Style=\"1\" X1=\"500000\" Y1=\"233\" X2=\"1000000\" Y2=\"292\" />\n" +
                "    <Item Style=\"6\" Value=\"1000000\" X1=\"1000000\" Y1=\"0.015\" X2=\"∞\" Y2=\"0.015\" />\n" +
                "  </Rule>\n" +
                "   <factor>\n" +
                "    <component Style=\"1\" Caption = \"其他系数\" Name = \"QTXS\" Value=\"1\" /> \n" +
                "  </factor>  \n" +
                "</Rules>  ";

        InterpolationFeeRuleVO vo = XStreamUtil.XML2Bean(xml.trim(), InterpolationFeeRuleVO.class);
        System.out.println(vo);
    }
}
