package cn.savas.hub.module.collect.util;

import cn.hutool.core.convert.Convert;

import java.math.BigDecimal;
import java.util.*;

public class MapDecimalUtil {

    /**
     * 初始化Map
     *
     * @param key
     * @param value
     * @return
     */
    public static Map<String, BigDecimal> of(String key, BigDecimal value) {
        HashMap<String, BigDecimal> map = new HashMap<>();
        map.put(key, value);
        return map;
    }

    /**
     * 合并多个Map，返回一个新的Map，避免对象传递导致问题
     *
     * @param mapList 可变参数，多个Map
     * @return 合并后的新Map
     */
    @SafeVarargs
    public static Map<String, BigDecimal> mergeMap(Map<String, BigDecimal>... mapList) {
        List<Map<String, BigDecimal>> list = Arrays.asList(mapList);
        return mergeMap(list);
    }

    /**
     * 合并多个Map，返回一个新的Map，避免对象传递导致问题
     *
     * @param mapList 可变参数，多个Map
     * @return 合并后的新Map
     */
    public static Map<String, BigDecimal> mergeMap(Map<String, BigDecimal> baseMap, List<Map<String, BigDecimal>> mapList) {
        Map<String, BigDecimal> map1 = mergeMap(mapList);
        return mergeMap(baseMap, map1);
    }

    /**
     * 合并多个Map，返回一个新的Map，避免对象传递导致问题
     *
     * @param mapList 可变参数，多个Map
     * @return 合并后的新Map
     */
    public static Map<String, BigDecimal> mergeMap(List<Map<String, BigDecimal>> mapList) {
        // 创建一个新的Map来存储结果
        Map<String, BigDecimal> result = new HashMap<>();
        // 遍历传入的每个Map
        for (Map<String, BigDecimal> map : mapList) {
            if (map != null && !map.isEmpty()) {
                for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
                    // 使用merge方法将相同键的值相加
                    result.merge(entry.getKey(), entry.getValue(), BigDecimal::add);
                }
            }
        }
        return result;
    }

    /**
     * 合并多个Map，返回一个新的Map，避免对象传递导致问题
     *
     * @param mapList 可变参数，多个Map
     * @return 合并后的新Map
     */
    @SafeVarargs
    public static Map<String, Object> mergeMapToObject(Map<String, BigDecimal>... mapList) {
        // 创建一个新的Map来存储结果
        Map<String, Object> result = new HashMap<>();
        // 遍历传入的每个Map
        for (Map<String, BigDecimal> map : mapList) {
            for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
                result.put(entry.getKey(), Convert.toBigDecimal(entry.getValue(), BigDecimal.ZERO));
            }
        }
        return result;
    }

    /**
     * 获取Map中所有值的和
     *
     * @param baseMap Map对象
     * @return 所有值的和
     */
    public static BigDecimal getMapSum(Map<String, BigDecimal> baseMap) {
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal value : baseMap.values()) {
            result = result.add(Convert.toBigDecimal(value, BigDecimal.ZERO));
        }
        return result;
    }


    /**
     * 对比两个Map，获取不一致的值和不为默认值的值
     * 规则：
     * 1. 键在两个Map中都存在且值不同 → 加入diffMap（值为newValue）
     * 2. 键仅在oldMap中存在且值不为defaultValue → 加入diffMap（值为oldValue）
     * 3. 键仅在newMap中存在且值不为defaultValue → 加入diffMap（值为newValue）
     *
     * @return 所有值的和
     */
    public static Map<String, BigDecimal> diffMapWitNotDefaultValue(Map<String, BigDecimal> oldMap, Map<String, BigDecimal> newMap, BigDecimal defaultValue) {
        Map<String, BigDecimal> diffMap = new HashMap<>();
        // 收集所有键的并集
        Set<String> allKeys = new HashSet<>(oldMap.keySet());
        allKeys.addAll(newMap.keySet());
        for (String key : allKeys) {
            BigDecimal oldValue = oldMap.get(key);
            BigDecimal newValue = newMap.get(key);
            if (oldValue == null) {
                if (newValue.compareTo(defaultValue) != 0) {
                    diffMap.put(key, newValue);
                }
            }
            if (newValue == null) {
                if (oldValue.compareTo(defaultValue) != 0) {
                    diffMap.put(key, defaultValue);
                }
            }
            if (newValue != null && oldValue != null) {
                if (oldValue.compareTo(newValue) != 0) {
                    diffMap.put(key, newValue);
                }
            }
        }
        return diffMap;
    }

    /**
     * 获取差值
     *
     * @return 所有值的和
     */
    public static Map<String, BigDecimal> diffMapFee(Map<String, BigDecimal> oldMap, Map<String, BigDecimal> newMap) {
        Map<String, BigDecimal> diffMap = new HashMap<>();
        // 收集所有键的并集
        Set<String> allKeys = new HashSet<>(oldMap.keySet());
        allKeys.addAll(newMap.keySet());
        for (String key : allKeys) {
            BigDecimal oldValue = oldMap.get(key);
            BigDecimal newValue = newMap.get(key);
            if (oldValue == null) {
                diffMap.put(key, newValue);
            }
            if (newValue == null) {
                diffMap.put(key, oldValue.negate());
            }
            if (newValue != null && oldValue != null) {
                diffMap.put(key, newValue.subtract(oldValue));
            }
        }
        return diffMap;
    }

    /**
     * 获取差值
     *
     * @return 所有值的和
     */
    public static Map<String, BigDecimal> diffMapRate(Map<String, BigDecimal> oldMap, Map<String, BigDecimal> newMap) {
        Map<String, BigDecimal> diffMap = new HashMap<>();
        // 收集所有键的并集
        oldMap.forEach((key, oldValue) -> {
            BigDecimal newValue = newMap.get(key);
            if (oldValue.compareTo(newValue) != 0) {
                diffMap.put(key, newValue);
            }
        });
        return diffMap;
    }

}
