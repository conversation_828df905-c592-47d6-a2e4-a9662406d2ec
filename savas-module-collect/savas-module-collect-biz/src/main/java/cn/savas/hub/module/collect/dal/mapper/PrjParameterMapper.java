package cn.savas.hub.module.collect.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.collect.dal.dataobject.PrjParameter;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/20 09:23
 */
@Mapper
public interface PrjParameterMapper extends BaseMapperX<PrjParameter> {
    List<PrjParameter> selectProjectInfo(Long hostModelId);

    default List<PrjParameter> selectByHostModel(Long hostModelId) {
        return selectList(PrjParameter::getParaHostmodel, hostModelId);
    }
}
