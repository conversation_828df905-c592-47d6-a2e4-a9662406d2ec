package cn.savas.hub.module.collect.util;

import com.google.common.collect.Table;

import java.math.BigDecimal;
import java.util.Map;

public class TableUtil {

    public static void add(String row, Map<String, BigDecimal> map, Table<String, String, BigDecimal> table) {
        for (String col : map.keySet()) {
            table.put(row, col, map.get(col));
        }
    }

    public static BigDecimal getOrDef(Table<String, String, BigDecimal> table, String row, String code) {
        if (table.contains(row, code)) {
            return table.get(row, code);
        }
        return BigDecimal.ZERO;
    }

}
