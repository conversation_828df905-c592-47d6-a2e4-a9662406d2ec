package cn.savas.hub.module.collect.dal.handler;

import cn.savas.hub.framework.common.util.json.JsonUtils;
import cn.savas.hub.module.collect.dal.handler.dto.InterpolationFeeRuleVO;
import cn.savas.hub.framework.common.util.io.XStreamUtil;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;

public class CalcConfigHandlerV2 extends AbstractJsonTypeHandler<Object> {

    public CalcConfigHandlerV2(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        try {
            return XStreamUtil.XML2Bean(json, InterpolationFeeRuleVO.Factor.class);
        } catch (Exception e) {
            InterpolationFeeRuleVO interVO = XStreamUtil.XML2Bean(json, InterpolationFeeRuleVO.class);
            return interVO.getFactor();
        }
    }

    @Override
    public String toJson(Object obj) {
        return JsonUtils.toJsonString(obj);
    }
}
