package cn.savas.hub.module.collect.api;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.client.enmus.ClientParameterEnum;
import cn.savas.hub.framework.common.client.util.ClientDateConvertUtil;
import cn.savas.hub.module.collect.api.dto.*;
import cn.savas.hub.module.collect.convert.CollectDataConvert;
import cn.savas.hub.module.collect.dal.dataobject.*;
import cn.savas.hub.module.collect.dal.mapper.*;
import cn.savas.hub.module.collect.framework.db.SqliteSourceManager;
import cn.savas.hub.module.collect.service.CollectDataService;
import cn.savas.hub.module.collect.strategy.ProductMergeStrategyResolver;
import cn.savas.hub.module.collect.strategy.ProductTestingFeeStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.collect.api.ErrorCodeConstants.UNKNOWN_ALIAS;

/**
 * <AUTHOR>
 * @date 2024/11/18 18:42
 */
@Service
public class CollectDataApiImpl implements CollectDataApi {

    @Resource
    private SqliteSourceManager sqliteSourceManager;
    @Resource
    private PrjEngineeringMapper engineeringMapper;
    @Resource
    private PrjParameterMapper parameterMapper;
    @Resource
    private PrjProjectcostMapper projectCostMapper;
    @Resource
    private PrjSectionBillMapper sectionBillMapper;
    @Resource
    private PrjNormMapper prjNormMapper;
    @Resource
    private PrjProjectengMapper projectEngMapper;
    @Resource
    private CollectDataService collectDataService;
    @Resource
    private PrjEngineeringcostMapper engineeringCostMapper;
    @Resource
    private ProductMergeStrategyResolver strategyResolver;
    @Resource
    private PrjTestingengineeringMapper testingEngineeringMapper;
    @Resource
    private PrjRateStandardMapper rateStandardMapper;
    @Resource
    private PrjMainMaterialCoefficientMapper mainMaterialCoefficientMapper;
    @Override
    public List<WbsTreeDTO> getWbsTree(Long projectId) {
        return CollectDataConvert.INSTANCE.convertList1(engineeringMapper.selectWbsTree());
    }

    @Override
    public List<BudgetProjectInfoDTO> getProjectInfo(Long wbsId) {
        return CollectDataConvert.INSTANCE.convertList2(parameterMapper.selectProjectInfo(wbsId));
    }

    @Override
    public List<PrjProjectCostDTO> getProjectCostByEngId(Long engineeringId) {
        return CollectDataConvert.INSTANCE.convertList3(projectCostMapper.selectProjectCostByEngId(engineeringId));
    }

    @Override
    public List<PrjEngineeringDTO> getEngineering() {
        List<FeeEngineering> list = engineeringMapper.selectList(new LambdaQueryWrapper<FeeEngineering>().orderByAsc(FeeEngineering::getEngSortid));
        return CollectDataConvert.INSTANCE.convertList4(list);
    }

    @Override
    public List<PrjEngineeringDTO> getEngineeringById(Collection<Long> engIds) {
        if (CollectionUtils.isEmpty(engIds)) {
            return new ArrayList<>();
        }
        List<FeeEngineering> list = engineeringMapper.selectList(
                new LambdaQueryWrapper<FeeEngineering>()
                        .in(FeeEngineering::getEngId, engIds)
                        .orderByAsc(FeeEngineering::getEngSortid)
        );
        return CollectDataConvert.INSTANCE.convertList4(list);
    }

    @Override
    public List<PrjSectionBillDTO> getBranch(Long engineeringId) {
        List<PrjSectionBill> bills = sectionBillMapper.selectList(
                new LambdaQueryWrapper<PrjSectionBill>().eq(engineeringId != null, PrjSectionBill::getBillHostmodel, engineeringId)
        );
        return CollectDataConvert.INSTANCE.convertList5(bills);
    }

    @Override
    public List<PrjNormDTO> getNorm(Long engineeringId) {
        List<PrjNorm> prjNorms = prjNormMapper.selectList(
                new LambdaQueryWrapper<PrjNorm>().eq(engineeringId != null, PrjNorm::getNormHostmodel, engineeringId)
        );
        return CollectDataConvert.INSTANCE.convertList6(prjNorms);
    }

    @Override
    public List<PrjNormDTO> getNormByCondition(PrjNormQueryDTO queryDTO) {
        List<PrjNorm> prjNorms = prjNormMapper.selectList(
                new LambdaQueryWrapper<PrjNorm>()
                        .like(queryDTO.getName() != null, PrjNorm::getNormName, queryDTO.getName())
                        .like(queryDTO.getUnit() != null, PrjNorm::getNormUnit, queryDTO.getUnit())
                        .like(queryDTO.getCompositions() != null, PrjNorm::getNormCompositions, queryDTO.getCompositions())
        );
        return CollectDataConvert.INSTANCE.convertList6(prjNorms);
    }

    @Override
    public PrjProjectEngDTO getProjectEng(Long projectId) {
        return CollectDataConvert.INSTANCE.convert7(projectEngMapper.selectOne(
                new LambdaQueryWrapper<FeeProjecteng>().eq(FeeProjecteng::getEngProjectid, projectId)
        ));
    }

    @Override
    public void mergeProjectFile(ClientMergeProjectFileDTO reqVO) throws Exception {
        collectDataService.mergeProjectFile(reqVO);
    }

    @Override
    public void calculateProjectData(CalculateReqDTO reqDTO) throws Exception {
        collectDataService.calculateProjectData(reqDTO);
    }

    @Override
    public void deleteDataByEngId(List<Long> engIds, String product) {
        projectEngMapper.delAttachmentFile(engIds);
        projectEngMapper.delBill(engIds);
        projectEngMapper.delComments(engIds);
        projectEngMapper.delConsumption(engIds);
        projectEngMapper.delConsumptionConversion(engIds);
        projectEngMapper.delCostCalculator(engIds);
        projectEngMapper.delCostRegulation(engIds);
        projectEngMapper.delDesigncondition(engIds);
        projectEngMapper.delEngineering(engIds);
//        projectEngMapper.delEngineeringAmountVariate(engIds);
        projectEngMapper.delEngineeringcost(engIds);
        projectEngMapper.delExpense(engIds);
        projectEngMapper.delExpenseitem(engIds);
        projectEngMapper.delHistorydata(engIds);
//        projectEngMapper.delIndependentCost(engIds);
//        projectEngMapper.delIndependentCostCategory(engIds);
        projectEngMapper.delLecture(engIds);
        projectEngMapper.delMeasure(engIds);
        projectEngMapper.delNorm(engIds);
//        prjProjectengMapper.delNormAmountDetail(engIds);
        projectEngMapper.delNormConversion(engIds);
        projectEngMapper.delOtherbill(engIds);
        projectEngMapper.delOtherbillCategory(engIds);
        projectEngMapper.delParameter(engIds);
        projectEngMapper.delProjectcost(engIds);
        projectEngMapper.delRateStandard(engIds);
        projectEngMapper.delSectionbill(engIds);
        projectEngMapper.delSpecialty(engIds);
        projectEngMapper.delReportCategory(engIds);
        projectEngMapper.delReportTemplate(engIds);
        projectEngMapper.delProjectEng(engIds);
        projectEngMapper.delDictRateStandardOptionCode(engIds);
        projectEngMapper.delDictRateStandardOptionCodeContent(engIds);
        // 删除工程数据-产品业务
        strategyResolver.resolve(product).deleteDataByEngId(engIds);
    }

    @Override
    public void updateDirectorId(Long projectid, List<PrjEngineeringDTO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        for (PrjEngineeringDTO item : items) {
            // 项目层级单独处理
            if (projectid.equals(item.getEngId())) {
                projectEngMapper.updateDirectorId(item.getEngId(), item.getEngDirectorid(), item.getEngDirectorname());
                continue;
            }
            engineeringMapper.updateDirectorId(item.getEngId(), item.getEngDirectorid(), item.getEngDirectorname());
        }
    }

    @Override
    public List<PrjSectionBillDTO> getDeviceSectionBillByEngId(Long projectId, Collection<Long> engIdList) {
        if (CollectionUtils.isEmpty(engIdList)) {
            return new ArrayList<>();
        }
        List<PrjSectionBill> prjSectionBills = sectionBillMapper.selectList(
                new LambdaQueryWrapper<PrjSectionBill>()
                        .select(PrjSectionBill::getBillId,
                                PrjSectionBill::getBillPid,
                                PrjSectionBill::getBillCode,
                                PrjSectionBill::getBillName,
                                PrjSectionBill::getBillHostmodel,
                                PrjSectionBill::getBillParameters,
                                PrjSectionBill::getBillExpression,
                                PrjSectionBill::getBillProfessionalcode,
                                PrjSectionBill::getBillUnit,
                                PrjSectionBill::getBillSortid)
                        .in(PrjSectionBill::getBillHostmodel, engIdList)
        );
        return CollectDataConvert.INSTANCE.convertList5(prjSectionBills);
    }

    @Override
    public List<PrjProjectCostDTO> getDeviceProjectCostByEngId(Long projectId, Collection<Long> engIdList) {
        if (CollectionUtils.isEmpty(engIdList)) {
            return new ArrayList<>();
        }
        List<FeeProjectcost> projectcosts = projectCostMapper.selectList(
                new LambdaQueryWrapper<FeeProjectcost>()
                        .select(FeeProjectcost::getFeeName,
                                FeeProjectcost::getFeeCode,
                                FeeProjectcost::getFeeHostmodel,
                                FeeProjectcost::getFeeParameters,
                                FeeProjectcost::getFeeSortid)
                        .in(FeeProjectcost::getFeeHostmodel, engIdList)
        );
        return CollectDataConvert.INSTANCE.convertList3(projectcosts);
    }

    @Override
    public PrjDeviceProjectDTO getDeviceProject(Long prjId) {
        FeeProjecteng projectDO = projectEngMapper.selectById(prjId);
        List<PrjParameter> parameterList = parameterMapper.selectByHostModel(prjId);
        // 构建返回对象
        PrjDeviceProjectDTO projectDTO = new PrjDeviceProjectDTO();
        projectDTO.setProjectId(projectDO.getEngId());
        projectDTO.setProjectName(projectDO.getEngName());
        projectDTO.setCreateTime(ClientDateConvertUtil.convertDate(projectDO.getEngDate().floatValue()));
        for (PrjParameter prjParameter : parameterList) {
            setParameterValue(projectDTO, prjParameter);
        }
        return projectDTO;
    }

    @Override
    public List<OtherEngFeeDTO> getProjectOtherEngFee(Long engId) {
        return CollectDataConvert.INSTANCE.convertList8(engineeringCostMapper.selectByHostModelId(engId));
    }

    @Override
    public List<OtherFeeDTO> getProjectOtherFee(Long engId) {
        return CollectDataConvert.INSTANCE.convertList9(projectCostMapper.selectByHostModelId(engId));
    }

    @Override
    public List<PrjSectionBillDTO> getBranchByEngId(Collection<Long> hostModelIdSet) {
        if (CollectionUtils.isEmpty(hostModelIdSet)) {
            return new ArrayList<>();
        }
        return CollectDataConvert.INSTANCE.convertList5(sectionBillMapper.selectList(
                new LambdaQueryWrapper<PrjSectionBill>()
                        .in(PrjSectionBill::getBillHostmodel, hostModelIdSet)
        ));
    }

    @Override
    public List<PrjSectionBillDTO> getBranch() {
        List<PrjSectionBill> prjSectionBills = sectionBillMapper.selectList(
                new LambdaQueryWrapper<PrjSectionBill>()
                        .orderByAsc(PrjSectionBill::getBillSortid)
        );
        return CollectDataConvert.INSTANCE.convertList5(prjSectionBills);
    }

    @Override
    public List<PrjTestEngineeringDTO> getTestEngineering(Long projectId) {
       return CollectDataConvert.INSTANCE.convertList10(testingEngineeringMapper.selectList());

    }

    @Override
    public void syncProjectParam(List<ClientPrjSettingParamDTO> reqDTO) {
        parameterMapper.insertOrUpdate(CollectDataConvert.INSTANCE.convertList11(reqDTO));
    }

    @Override
    public void syncProjectRate(List<ClientPrjSettingRateDTO> reqDTO) {
        rateStandardMapper.insertOrUpdate(CollectDataConvert.INSTANCE.convertList12(reqDTO));
    }

    @Override
    public void syncProjectEffectSet(String product, List<ClientPrjSettingEffectSetDTO> reqDTO) {
        // 获取执行产品合并策略
        strategyResolver.resolve(product).mergeEffectFeeSet(CollectDataConvert.INSTANCE.convertList14(reqDTO));
    }

    @Override
    public void syncProjectEffectMulti(String product, List<ClientPrjSettingEffectMultiDTO> reqDTO) {
        // 获取执行产品合并策略
        strategyResolver.resolve(product).mergeEffectFeeCoefficientMulti(CollectDataConvert.INSTANCE.convertList15(reqDTO));
    }

    @Override
    public void syncProjectMainMaterial(List<ClientPrjSettingMaterialDTO> reqDTO) {
        mainMaterialCoefficientMapper.delete(new LambdaQueryWrapper<>());
        mainMaterialCoefficientMapper.insertBatch(CollectDataConvert.INSTANCE.convertList13(reqDTO));
    }

    @Override
    public void updateProjectData(PrjProjectEngDTO prjProjectEngDTO) {
        projectEngMapper.update(null, new LambdaUpdateWrapper<FeeProjecteng>()
                .eq(FeeProjecteng::getEngProjectid, prjProjectEngDTO.getEngProjectid())
                .set(prjProjectEngDTO.getEngEdition() != null, FeeProjecteng::getEngEdition, prjProjectEngDTO.getEngEdition())
                .set(prjProjectEngDTO.getEngCompileversion() != null, FeeProjecteng::getEngCompileversion, prjProjectEngDTO.getEngCompileversion())
                .set(prjProjectEngDTO.getEngSyncversion() != null, FeeProjecteng::getEngSyncversion, prjProjectEngDTO.getEngSyncversion())
                .set(prjProjectEngDTO.getEngState() != null, FeeProjecteng::getEngState, prjProjectEngDTO.getEngState())
        );
    }


    private void setParameterValue(PrjDeviceProjectDTO projectDTO, PrjParameter parameter) {
        ClientParameterEnum paramEnum = ClientParameterEnum.getByCode(parameter.getParaCode());
        if (paramEnum == null) return;
        switch (paramEnum) {
            case DESIGNSTAGE:
                projectDTO.setDesignStage(parameter.getParaString());
                break;
            case MAINMATERIALCOEFFICIENT:
                projectDTO.setPriceLevel(parameter.getParaString());
                break;
        }
    }
}
