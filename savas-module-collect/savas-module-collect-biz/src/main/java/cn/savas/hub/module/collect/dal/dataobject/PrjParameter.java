package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/20 09:23
 */
@TableName(value ="prj_parameter")
@Data
public class PrjParameter {
    @TableId
    private Long paraId;
    private Long paraPid;
    private Long paraHostmodel;
    private Long paraParent;
    private String paraSequence;
    private String paraCategory;
    private String paraCode;
    private String paraName;
    private Integer paraSortid;
    private Integer paraEditor;
    private Integer paraStyle;
    private Integer paraValuetype;
    private Long paraInt;
    private BigDecimal paraFloat;
    private String paraString;
    private String paraDescription;
    private Integer paraContext;
    private String paraRegular;
    private String paraRegulartext;
    private String paraCodeitem;
    private Long paraState;
}
