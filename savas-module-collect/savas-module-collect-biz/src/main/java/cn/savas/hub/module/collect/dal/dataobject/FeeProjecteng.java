package cn.savas.hub.module.collect.dal.dataobject;

import cn.savas.hub.framework.common.client.util.V9BinaryToMapUtil;
import cn.savas.hub.framework.common.client.util.V9MapToBinaryUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.ByteArrayTypeHandler;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @TableName prj_projecteng
 */
@TableName(value = "prj_projecteng", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class FeeProjecteng extends BaseFeeEntity {
    /**
     * 工程ID
     */
    @TableId(value = "eng_id")
    private Long engId;

    /**
     * 项目ID
     */
    @TableField(value = "eng_projectid")
    private Long engProjectid;

    /**
     * 原始ID
     */
    @TableField(value = "eng_originalid")
    private Long engOriginalid;

    /**
     * 工程代码
     */
    @TableField(value = "eng_code")
    private String engCode;

    /**
     * 工程名称
     */
    @TableField(value = "eng_name")
    private String engName;

    /**
     * 阶段
     */
    @TableField(value = "eng_phase")
    private String engPhase;

    /**
     * 版本
     */
    @TableField(value = "eng_edition")
    private String engEdition;

    /**
     * 编译版本
     */
    @TableField(value = "eng_CompileVersion")
    private Integer engCompileversion;

    /**
     * 同步版本
     */
    @TableField(value = "eng_syncversion")
    private Integer engSyncversion;

    /**
     * 状态
     */
    @TableField(value = "eng_state")
    private Long engState;

    /**
     * 样式
     */
    @TableField(value = "eng_Style")
    private Integer engStyle;

    /**
     * 用户ID
     */
    @TableField(value = "eng_userid")
    private Long engUserid;

    /**
     * 用户名
     */
    @TableField(value = "eng_username")
    private String engUsername;

    /**
     * 日期
     */
    @TableField(value = "eng_date")
    private BigDecimal engDate;

    /**
     * 主管ID
     */
    @TableField(value = "eng_directorid")
    private Long engDirectorid;

    /**
     * 主管姓名
     */
    @TableField(value = "eng_directorname")
    private String engDirectorname;

    /**
     * 规则
     */
    @TableField(value = "eng_rule")
    private String engRule;

    /**
     * 参数
     */
    @TableField(value = "eng_parameters", typeHandler = ByteArrayTypeHandler.class)
    private byte[] engParameters;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public Long getId() {
        return this.engId;
    }

    @Override
    public Long getPid() {
        return null;
    }

    @Override
    public Map<String, BigDecimal> getFeeMap() {
        return V9BinaryToMapUtil.unpackData(this.engParameters);
    }

    @Override
    public void setBytes(Map<String, BigDecimal> feeMap) {
        this.engParameters = V9MapToBinaryUtil.convertToBinary(feeMap);
    }

    @Override
    public String getName() {
        return this.engName;
    }

    @Override
    public Integer getEngClass() {
        return 0;
    }

    @Override
    public String getCode() {
        return engCode;
    }
}
