package cn.savas.hub.module.collect.util;

import cn.hutool.core.convert.Convert;
import cn.savas.hub.module.collect.dal.dataobject.BaseFeeEntity;
import cn.savas.hub.module.collect.enums.ReduceKeyEnum;
import com.alibaba.fastjson.JSON;
import lombok.extern.log4j.Log4j2;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 初始化各个类型的计算的基础费用
 * 以及费用变动
 */
@Log4j2
public class BaseFeeMapUtil {


    /**
     * 初始化根节点计算费用
     */
    public static Map<String, BigDecimal> initOtherRootFee(Map<String, BigDecimal> source, String code) {
        Map<String, BigDecimal> baseFee = new HashMap<>();
        for (String key : source.keySet()) {
            if (!key.contains("__")) {
                if (key.equals(ReduceKeyEnum.FEE.name())) {
                    initOtherFeeByEng(baseFee, code, source.get(key));
                }
                baseFee.put(key + "__" + code, source.get(key));
            } else {
                baseFee.put(key, source.get(key));
            }
        }
        log.info("基础费用:{}", JSON.toJSONString(baseFee));
        return baseFee;
    }

    /**
     * 补充各行
     * 计算值之后的费用
     * 防止造成干扰 需要重新建
     */
    public static void initOtherFeeByEng(Map<String, BigDecimal> main, String code, BigDecimal value) {
        main.put("FEE__" + code, value);
        main.put("__" + code, value);
        main.put("____" + code, value);
        main.put(code, value);
    }

    /**
     * 补充各行
     * 计算值之后的费用
     * 防止造成干扰 需要重新建
     */
    public static void addFee(Map<String, BigDecimal> main, String code, BigDecimal value) {
        main.put(code, value);
    }

    /**
     * 初始化其他费计算公式
     * 初始Map
     *
     * @return
     */
    public static Map<String, BigDecimal> getOtherExpenseBaseFee(BigDecimal value, BigDecimal taxRate, BigDecimal foreignValue) {
        Map<String, BigDecimal> baseFee = new HashMap<>();
        baseFee.put(ReduceKeyEnum.Value.name(), value);
        baseFee.put(ReduceKeyEnum.TaxRate.name(), taxRate);
        baseFee.put(ReduceKeyEnum.ForeignValue.name(), Convert.toBigDecimal(foreignValue, BigDecimal.ZERO));
        return baseFee;
    }

    /**
     * 计算完后
     * 数据保存至整体缓存
     *
     * @param cacheFee
     */
    public static void updateCacheFeeByOtherRow(String code, BigDecimal value, Map<String, BigDecimal> cacheFee, Map<String, BigDecimal> rowFee) {
        cacheFee.put("FEE__" + code, value);
        cacheFee.put("__" + code, value);
        cacheFee.put("____" + code, value);
        cacheFee.put(code, value);
        for (String key : rowFee.keySet()) {
            cacheFee.put(key + "__" + code, rowFee.get(key));
        }
    }

    /**
     * 获取子类合计
     *
     * @param children
     * @return
     */
    public static Map<String, BigDecimal> getSummeryChildRowFee(List<? extends BaseFeeEntity> children) {
        Map<String, BigDecimal> feeMap = new HashMap<>();
        for (BaseFeeEntity child : children) {
            Map<String, BigDecimal> childFee = child.getFeeMap();
            for (String key : childFee.keySet()) {
                feeMap.put(key, feeMap.getOrDefault(key, BigDecimal.ZERO).add(childFee.get(key)));
            }
        }
        return feeMap;
    }


    /**
     * 合计费用
     *
     * @return
     */
    public static Map<String, BigDecimal> addChildRowFee(Map<String, BigDecimal> map, List<? extends BaseFeeEntity> children) {
        Map<String, BigDecimal> feeMap = getSummeryChildRowFee(children);
        for (String key : feeMap.keySet()) {
            map.put(key, map.getOrDefault(key, BigDecimal.ZERO).add(feeMap.get(key)));
        }
        return map;
    }

}
