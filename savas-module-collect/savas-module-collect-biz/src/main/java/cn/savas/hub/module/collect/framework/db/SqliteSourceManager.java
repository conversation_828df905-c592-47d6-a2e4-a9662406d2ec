package cn.savas.hub.module.collect.framework.db;


import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Set;

/**
 * 动态切换数据源
 *
 * <AUTHOR>
 */
@Component
@Log4j2
public class SqliteSourceManager {

    @Value("${spring.datasource.dynamic.primary}")
    private String master;

    @Resource
    private DataSource dataSource;
    @Resource
    private DefaultDataSourceCreator dataSourceCreator;

    /**
     * 查询数据源
     *
     * @return {@link Set <String> }
     */
    public Set<String> listDatasource() {
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        return ds.getDataSources().keySet();
    }

    /**
     * 切换数据源
     *
     * @return {@link Set <String> }
     */
    public void changeDatasource(String poolName, String url, String driverClassName) {
        this.addDatasource(poolName, url, driverClassName);
        DynamicDataSourceContextHolder.push(poolName);
    }


    /**
     * 添加数据源
     * poolName  数据源名称
     * url  数据库连接
     * driverClassName  数据库驱动
     *
     * @return {@link Set}<{@link String}>
     */
    public void addDatasource(String poolName, String url, String driverClassName) {
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        DataSourceProperty dataSourceProperty = new DataSourceProperty();
        dataSourceProperty.setPoolName(poolName);
        dataSourceProperty.setUrl(url);
        dataSourceProperty.setDriverClassName(driverClassName);
        dataSourceProperty.getDruid().setTestWhileIdle(false); // 不在空闲时进行连接检查
        dataSourceProperty.getDruid().setTestOnBorrow(false); // 不在获取连接时检查
        dataSourceProperty.getDruid().setTestOnReturn(false); // 不在归还连接时检查
        DataSource dataSource = dataSourceCreator.createDataSource(dataSourceProperty);
        ds.addDataSource(poolName, dataSource);
        log.info("[dynamic ds] add datasource [{}] success!", poolName);
    }


    /**
     * 删除数据源
     *
     * @param poolName 池名称
     * @return boolean
     */
    public boolean removeDatasource(String poolName) {
        if ("master".equals(poolName)) {
            throw new RuntimeException("[dynamic ds] datasource master can't remove!");
        }
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        if (!ds.getDataSources().containsKey(poolName)) {
            return true;
        }
        ds.removeDataSource(poolName);
        log.info("[dynamic ds] remove datasource [{}] success!", poolName);
        DynamicDataSourceContextHolder.push(master);
        return true;
    }

    /**
     * 切换回Spring Boot默认主数据源 默认master
     */
    public void changeDefaultDB() {
        // 清除当前线程的数据源选择（避免嵌套切换残留）
        DynamicDataSourceContextHolder.clear();
        // 压入主数据源标识
        DynamicDataSourceContextHolder.push(master);
        log.info("[dynamic ds] switched to default datasource: {}", master);
    }
}
