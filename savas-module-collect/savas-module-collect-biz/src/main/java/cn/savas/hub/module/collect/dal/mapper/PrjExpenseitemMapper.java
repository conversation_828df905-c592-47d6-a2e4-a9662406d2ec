package cn.savas.hub.module.collect.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.collect.dal.dataobject.PrjExpenseitem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【prj_expenseitem】的数据库操作Mapper
* @createDate 2024-11-05 11:03:33
* @Entity generator.domain.PrjExpenseitem
*/
@Mapper
public interface PrjExpenseitemMapper extends BaseMapperX<PrjExpenseitem> {

    List<PrjExpenseitem> selectOtherExpList(@Param("feeExpense") String feeExpense, @Param("engId") Long engId);

    List<PrjExpenseitem> selectExpenseListByWbsId(@Param("lowIds") List<Long> lowIds);
}




