package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @TableName prj_expenseitem
 */
@TableName(value ="prj_expenseitem")
@Data
public class PrjExpenseitem implements Serializable {
    /**
     * 费用项ID
     */
    @TableId(value = "Exp_ID")
    private Long expId;

    /**
     * 父费用项ID
     */
    @TableField(value = "Exp_PID")
    private Long expPid;

    /**
     * 所属模型ID
     */
    @TableField(value = "Exp_HostModel")
    private Long expHostModel;

    /**
     * 序列号
     */
    @TableField(value = "Exp_Sequence")
    private String expSequence;

    /**
     * 费用项名称
     */
    @TableField(value = "Exp_Name")
    private String expName;

    /**
     * 表达式
     */
    @TableField(value = "Exp_Expression")
    private String expExpression;

    /**
     * 表达式代码
     */
    @TableField(value = "Exp_ExpressionCode")
    private String expExpressionCode;

    /**
     * 中文表达式
     */
    @TableField(value = "Exp_CNExpression")
    private String expCnExpression;

    /**
     * 费率
     */
    @TableField(value = "Exp_Rate")
    private BigDecimal expRate;

    /**
     * 费率代码
     */
    @TableField(value = "Exp_RateCode")
    private String expRateCode;

    /**
     * 类型
     */
    @TableField(value = "Exp_Kind")
    private Integer expKind;

    /**
     * 统计信息
     */
    @TableField(value = "Exp_Statistics")
    private String expStatistics;

    /**
     * 排序ID
     */
    @TableField(value = "Exp_SortID")
    private Integer expSortId;

    /**
     * 上下文
     */
    @TableField(value = "Exp_Context")
    private Integer expContext;

    /**
     * 状态
     */
    @TableField(value = "Exp_State")
    private Long expState;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String expenseName;
}