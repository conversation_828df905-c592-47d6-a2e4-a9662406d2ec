package cn.savas.hub.module.collect.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.client.enmus.ENGTypeEnum;
import cn.savas.hub.module.collect.dal.dataobject.*;
import cn.savas.hub.module.collect.dal.mapper.*;
import cn.savas.hub.module.collect.enums.ReduceKeyEnum;
import cn.savas.hub.module.collect.service.PrjCostcalculatorService;
import cn.savas.hub.module.collect.strategy.ProductMergeStrategyResolver;
import cn.savas.hub.module.collect.util.BaseFeeMapUtil;
import cn.savas.hub.module.collect.util.MapDecimalUtil;
import cn.savas.hub.module.collect.util.PrjFeeUtil;
import cn.savas.hub.module.collect.util.TableUtil;
import cn.savas.hub.module.collect.util.aviator.AviatorEvaluatorUtil;
import cn.savas.hub.module.collect.util.aviator.AviatorFormulaUtil;
import cn.savas.hub.module.collect.util.aviator.SpecialFormulaFormatUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 初始化各个类型的计算的基础费用
 * 以及费用变动
 */
@Log4j2
@Component
public class ReduceProjectManager {

    @Resource
    private PrjEngineeringMapper prjEngineeringMapper;
    @Resource
    private PrjEngineeringcostMapper engineeringcostMapper;
    @Resource
    private PrjProjectcostMapper prjProjectcostMapper;
    @Resource
    private PrjCostcalculatorService costcalculatorService;
    @Resource
    private PrjProjectengMapper prjProjectengMapper;
    @Resource
    private PrjExpenseitemMapper prjExpenseitemMapper;
    @Resource
    private PrjParameterMapper prjParameterMapper;
    @Resource
    private ProductMergeStrategyResolver strategyResolver;

    /**
     * 重新计算总和
     * 计算过程: 计算单项:工程其他费=>更新单项费=>计算单项其他费=>项目费
     *
     * @param wbsIds
     */
    @SneakyThrows
    public void reSummaryProjectCostToMain(List<Long> wbsIds, String product) {
        //同理计算项目费用
        FeeProjecteng project = prjProjectengMapper.selectOne(new LambdaQueryWrapper<>());
        FeeEngineering prjSingle = BeanUtil.toBean(project, FeeEngineering.class);
        prjSingle.setEngClass(ClientClassIdEnum.PROJECT.getCode().intValue());
        //汇总eng 并返回受影响的single//1.2返回所有受影响的父级重新计算
        List<FeeEngineering> engList = prjEngineeringMapper.selectList();
        engList.add(prjSingle);
        PrjFeeUtil.baseListToTree(engList);
        Map<Long, BaseFeeEntity> map = engList.stream().collect(Collectors.toMap(FeeEngineering::getEngId, p -> p));
        //忽略检验检测费的wbsId
        List<Long> effectiveWbsId = wbsIds.stream().filter(map::containsKey).collect(Collectors.toList());
        List<Long> lowIds = getLowWbsIds(effectiveWbsId, map);
        List<BaseFeeEntity> singleList = this.getLowChangeWbsList(lowIds, map);
        //计算其他费
        List<PrjCostcalculator> list = costcalculatorService.getCulculatorList();
        Map<String, PrjCostcalculator> calculatorMap = list.stream().collect(Collectors.toMap(PrjCostcalculator::getCalcName, p -> p));
        List<FeeEngineeringcost> otherPrjRows = engineeringcostMapper.selectList(new LambdaQueryWrapper<FeeEngineeringcost>().in(FeeEngineeringcost::getFeeHostmodel, lowIds));
        Map<Long, List<FeeEngineeringcost>> otherPrjMap = otherPrjRows.stream().collect(Collectors.groupingBy(FeeEngineeringcost::getFeeHostmodel));
        List<FeeProjectcost> otherRows = prjProjectcostMapper.selectList(new LambdaQueryWrapper<FeeProjectcost>().in(FeeProjectcost::getFeeHostmodel, lowIds));
        Map<Long, List<FeeProjectcost>> otherMap = otherRows.stream().collect(Collectors.groupingBy(FeeProjectcost::getFeeHostmodel));
        List<PrjParameter> paramList = prjParameterMapper.selectList(new LambdaQueryWrapper<PrjParameter>().in(PrjParameter::getParaHostmodel, lowIds));
        Map<Long, List<PrjParameter>> paraMap = paramList.stream().collect(Collectors.groupingBy(PrjParameter::getParaHostmodel));
        for (BaseFeeEntity single : singleList) {
            //合并费用
            List<Map<String, BigDecimal>> feeList = single.getChildren().stream().map(BaseFeeEntity::getFeeMap).collect(Collectors.toList());
            Map<String, BigDecimal> baseMap = MapDecimalUtil.mergeMap(feeList);
            single.setChildFeeMap(baseMap);
            List<PrjParameter> parameters = paraMap.get(single.getId());
            this.updateSinglePrjRelationFee(single, calculatorMap, new HashMap<>(), otherPrjMap, otherMap, parameters);
            if (Convert.toInt(ClientClassIdEnum.PROJECT.getCode()).equals(single.getEngClass()) || single.getEngClass() == 0 || single.getEngClass() == null) {
                prjProjectengMapper.updateById(BeanUtil.toBean(single, FeeProjecteng.class));
            } else {
                prjEngineeringMapper.updateById((FeeEngineering) single);
            }
        }
    }


    private void updateSinglePrjRelationFee(BaseFeeEntity single, Map<String, PrjCostcalculator> calculatorMap, Map<String, List<BaseCostEntity>> childCodeFeeMap, Map<Long, List<FeeEngineeringcost>> otherPrjMap, Map<Long, List<FeeProjectcost>> otherMap, List<PrjParameter> parameters) {
        //数据缓存池
        List<FeeEngineeringcost> otherPrjRows = otherPrjMap.get(single.getId()) == null ? new ArrayList<>() : otherPrjMap.get(single.getId());
        Table<String, String, BigDecimal> prjTable = this.initPrjOtherCacheFee(single, parameters);
        Map<String, BaseCostEntity> otherPrjRowMap = otherPrjRows.stream().collect(Collectors.toMap(FeeEngineeringcost::getFeeCode, p -> p));
        PrjFeeUtil.baseCostListToTree(otherPrjRows);
        //计算工程其他费 加合获取单项费用
        this.updateProjectOtherFee(single.getId(), prjTable, otherPrjRows, otherPrjRowMap, calculatorMap, childCodeFeeMap);
        //补充工程费更新单项级工程费用
        List<FeeEngineeringcost> childList = otherPrjRows.stream().filter(p -> p.getPid().equals(single.getId())).collect(Collectors.toList());
        Map<String, BigDecimal> singleFee = BaseFeeMapUtil.addChildRowFee(single.getChildFeeMap(), childList);
        single.setBytes(singleFee);
        //计算其他费 //其他费//初始化根节点费用
        List<FeeProjectcost> otherRows = otherMap.get(single.getId()) == null ? new ArrayList<>() : otherMap.get(single.getId());
        Table<String, String, BigDecimal> otherTable = this.initOtherTableCacheFee(single, otherRows, singleFee, parameters);
        Map<String, BaseCostEntity> otherRowMap = otherRows.stream().collect(Collectors.toMap(FeeProjectcost::getFeeCode, p -> p));
        PrjFeeUtil.baseCostListToTree(otherRows);
        this.updateOtherFee(single.getId(), otherTable, otherRows, otherRowMap, calculatorMap, childCodeFeeMap);
    }


    private void updateProjectOtherFee(Long singleId, Table<String, String, BigDecimal> table, List<FeeEngineeringcost> otherPrjRows, Map<String, BaseCostEntity> otherPrjRowMap, Map<String, PrjCostcalculator> calculatorMap, Map<String, List<BaseCostEntity>> childCodeFeeMap) {
        if (CollectionUtils.isEmpty(otherPrjRows)) {
            return;
        }
        for (FeeEngineeringcost row : otherPrjRows) {
            //检查是否计算过
            if (!table.containsRow(row.getFeeCode())) {
                this.reduceOtherFee(singleId, row, table, otherPrjRowMap, calculatorMap, childCodeFeeMap);
                //更新费用缓存
                TableUtil.add(row.getCode(), row.getFeeMap(), table);
            }
        }
        engineeringcostMapper.updateBatch(otherPrjRows);
    }


    private void updateOtherFee(Long singleId, Table<String, String, BigDecimal> table, List<? extends BaseCostEntity> otherRows, Map<String, BaseCostEntity> otherRowMap, Map<String, PrjCostcalculator> calculatorMap, Map<String, List<BaseCostEntity>> childCodeFeeMap) {
        for (BaseCostEntity row : otherRows) {
            //避免重复计算
            if (!table.containsRow(row.getCode())) {
                this.reduceOtherFee(singleId, row, table, otherRowMap, calculatorMap, childCodeFeeMap);
                //更新费用缓存
                TableUtil.add(row.getCode(), row.getFeeMap(), table);
            }
            if (row.getFeeType() == 1) {
                engineeringcostMapper.updateById((FeeEngineeringcost) row);
            } else {
                prjProjectcostMapper.updateById((FeeProjectcost) row);
            }
        }
    }

    private Table<String, String, BigDecimal> initOtherTableCacheFee(BaseFeeEntity single, List<FeeProjectcost> otherRows, Map<String, BigDecimal> singleFee, List<PrjParameter> parameters) {
        Table<String, String, BigDecimal> table = HashBasedTable.create();
        FeeProjectcost rootRow = otherRows.stream().filter(p -> StringUtils.isNotBlank(p.getFeeCostTag()) && p.getFeeCostTag().equals(ReduceKeyEnum.FEE.getDesc())).findAny().get();
        TableUtil.add("base", singleFee, table);
        TableUtil.add(rootRow.getFeeCode(), singleFee, table);
        this.addParamFeeToTable(single, table, parameters);
        rootRow.setBytes(single.getFeeMap());
        return table;
    }

    private void addParamFeeToTable(BaseFeeEntity single, Table<String, String, BigDecimal> table, List<PrjParameter> parameters) {
        //获取自定义参数
        Map<String, BigDecimal> paramMap = Optional.ofNullable(parameters)
                .orElse(Collections.emptyList())
                .stream()
                .filter(p -> StringUtils.isNotBlank(p.getParaCode()) && p.getParaFloat() != null)
                .collect(Collectors.toMap(PrjParameter::getParaCode, PrjParameter::getParaFloat,
                        (oldValue, newValue) -> newValue));
        TableUtil.add("param", paramMap, table);
    }

    private Table<String, String, BigDecimal> initPrjOtherCacheFee(BaseFeeEntity single, List<PrjParameter> parameters) {
        Table<String, String, BigDecimal> table = HashBasedTable.create();
        Map<String, BigDecimal> baseMap = single.getChildFeeMap();
        TableUtil.add("base", baseMap, table);
        //获取自定义参数
        addParamFeeToTable(single, table, parameters);
        return table;
    }

    private Map<String, BigDecimal> reduceFeeByExpense(Long engId, String name, Map<String, BigDecimal> baseMap) {
        List<PrjExpenseitem> expenseList = prjExpenseitemMapper.selectOtherExpList(name, engId);
        //最终计算结果:为了保持高度一致,基本费用不需要返回只需参与计算
        Map<String, BigDecimal> itemFeeMap = new HashMap<>();
        for (PrjExpenseitem item : expenseList) {
            BigDecimal itemVal = AviatorEvaluatorUtil.executeDirect(item.getExpExpression(), baseMap, item.getExpRate());
            baseMap.put(item.getExpSequence(), itemVal);
            itemFeeMap.put(item.getExpStatistics(), itemVal);
        }
        return itemFeeMap;
    }

    /**
     * 从其他费 中获取计算公式
     * 计算过程: 1.先计算本行数据:是特殊的先根据特殊情况计算费用
     * 2.通过单价公式计算 衍生费用
     *
     * @return
     */
    private void reduceOtherFee(Long singleId, BaseCostEntity row, Table<String, String, BigDecimal> table, Map<String, BaseCostEntity> otherRowMap, Map<String, PrjCostcalculator> calculatorMap, Map<String, List<BaseCostEntity>> childCodeFeeMap) {
        //根据expense 查找计算公式
        if (StringUtils.isNotBlank(row.getFeeExpression())) {
            //检查公式 并计算未被处理的
            this.checkExpressionAndReduce(singleId, row.getFeeExpression(), table, otherRowMap, calculatorMap, childCodeFeeMap);
            //全量加和
            if (row.getFeeExpression().contains("@@")) {
                Map<String, BigDecimal> fee = new HashMap<>();
                Set<String> codes = AviatorFormulaUtil.getCodeByFormula(row.getFeeExpression());
                for (String code : codes) {
                    PrjFeeUtil.mergeMapFee(fee, table.row(code));
                }
                row.setBytes(fee);
            } else {
                //特殊计算 需要先计算线性值作为2次计算公式计算的依据 //通过插值法获取 当前行的Fee
                if (StringUtils.isNotBlank(row.getFeeCostTag()) && calculatorMap.containsKey(row.getFeeCostTag())) {
                    //根节点对应关系
                    PrjCostcalculator calculator = calculatorMap.get(row.getFeeCostTag());
                    //需要从basic 计算初始值//检查公式 并计算未被处理的
                    this.checkExpressionAndReduce(singleId, calculator.getCalcConfig().getBasicExpression(), table, otherRowMap, calculatorMap, childCodeFeeMap);
                    BigDecimal basicValue = AviatorEvaluatorUtil.executeDirect(calculator.getCalcConfig().getBasicExpression(), table, BigDecimal.ONE);
                    SpecialFormulaFormatUtil.CalculatorResult result = SpecialFormulaFormatUtil.calculateInterpolation(calculator.getCalcConfig(), basicValue);
                    row.setFeeExpression(result.getFormulaExpression());
                    BigDecimal value = this.getValueByCalculatorResult(result, table);
                    //单价公式计算数据,计算当前子目内需要的行数据
                    if (StringUtils.isNotBlank(row.getFeeExpense())) {
                        Map<String, BigDecimal> baseOther = BaseFeeMapUtil.getOtherExpenseBaseFee(value, row.getFeeTaxRate(), row.getFeeForeignValue());
                        Map<String, BigDecimal> fee = this.reduceFeeByExpense(singleId, row.getFeeExpense(), baseOther);
                        row.setBytes(fee);
                    }
                } else {
                    BigDecimal otherVal = AviatorEvaluatorUtil.executeDirect(row.getFeeExpression(), table, row.getFeeRate());
                    //单价公式计算数据,计算当前子目内需要的行数据
                    if (StringUtils.isNotBlank(row.getFeeExpense())) {
                        Map<String, BigDecimal> baseOther = BaseFeeMapUtil.getOtherExpenseBaseFee(otherVal, row.getFeeTaxRate(), row.getFeeForeignValue());
                        Map<String, BigDecimal> fee = this.reduceFeeByExpense(singleId, row.getFeeExpense(), baseOther);
                        row.setBytes(fee);
                    }
                }
            }
        } else {
            //由子类汇总
            if (row.getCostChildren() != null && !row.getCostChildren().isEmpty()) {
                List<BaseCostEntity> children = row.getCostChildren();
                this.updateOtherFee(singleId, table, children, otherRowMap, calculatorMap, childCodeFeeMap);
                Map<String, BigDecimal> childFee = BaseFeeMapUtil.getSummeryChildRowFee(children);
                row.setBytes(childFee);
                row.setChildFeeMap(childFee);
            } else {
                //从子类汇总
                List<BaseCostEntity> children = childCodeFeeMap.get(row.getCode());
                if (children != null) {
                    Map<String, BigDecimal> map = PrjFeeUtil.summeryChildFee(children);
                    row.setBytes(map);
                }
            }
        }
    }

    private void checkExpressionAndReduce(Long singleId, String expression, Table<String, String, BigDecimal> table, Map<String, BaseCostEntity> otherRowMap, Map<String, PrjCostcalculator> calculatorMap, Map<String, List<BaseCostEntity>> childCodeFeeMap) {
        //正常计算 通过公式正常计算当前值并更绝计算公式计算衍生值
        Set<String> unCodes = AviatorFormulaUtil.pretreatmentFormulaReturnIllegalRowCode(expression, table);
        //递归计算其他行
        List<BaseCostEntity> unList = this.getFeeListByCode(unCodes, otherRowMap);
        this.updateOtherFee(singleId, table, unList, otherRowMap, calculatorMap, childCodeFeeMap);
    }

    private List<BaseCostEntity> getFeeListByCode(Set<String> unCodes, Map<String, BaseCostEntity> otherRowMap) {
        List<BaseCostEntity> list = new ArrayList<>();
        for (String unCode : unCodes) {
            BaseCostEntity feeProjectcost = otherRowMap.get(unCode);
            list.add(feeProjectcost);
        }
        return list;
    }

    private BigDecimal getValueByCalculatorResult(SpecialFormulaFormatUtil.CalculatorResult result, Table<String, String, BigDecimal> table) {
        if (result.getStyle() == 1) {//金额
            //直接返回
            return result.getResult();
        } else {//比例
            //执行计算公式*费率
            return AviatorEvaluatorUtil.executeDirect(result.getFormulaExpression(), table, result.getResult()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        }
    }

    /**
     * 递归获取所有有效的数据ID
     * 去重
     */
    private void searchSingleFromTree(Long wbsId, Map<Long, BaseFeeEntity> map, List<Long> ids, Boolean first) {
        BaseFeeEntity eng = map.get(wbsId);
        if (first && (eng.getEngClass() == null || !eng.getEngClass().equals(ENGTypeEnum.DANWEI.getCode()))) {
            ids.add(eng.getId());
        }
        BaseFeeEntity father = map.get(eng.getPid());
        if (father != null) {
            ids.add(father.getId());
            searchSingleFromTree(father.getId(), map, ids, false);
        }
    }

    /**
     * 需要对数据去掉关联影响
     *
     * @param
     * @param map
     * @return
     */
    private List<BaseFeeEntity> getLowChangeWbsList(List<Long> lowIds, Map<Long, BaseFeeEntity> map) {
        List<BaseFeeEntity> list = new ArrayList<>();
        for (Long id : lowIds) {
            BaseFeeEntity wbs = map.get(id);
            list.add(wbs);
        }
        log.info("受影响的wbs个数：{} 数据:{}", list.size(), list);
        return list;
    }

    /**
     * 获取受影响的呃最底层id
     *
     * @return
     */
    private List<Long> getLowWbsIds(List<Long> wbsIds, Map<Long, BaseFeeEntity> map) {
        List<List<Long>> table = new ArrayList<>();
        for (Long wbsId : wbsIds) {
            List<Long> ids = new ArrayList<>();
            searchSingleFromTree(wbsId, map, ids, true);
            table.add(ids);
        }
        //按照从大到小排序
        table.sort((a, b) -> Integer.compare(b.size(), a.size()));
        int maxDepth = table.get(0).size();
        //补全，从0开始补充到最大
        for (List<Long> longs : table) {
            if (longs.size() < maxDepth) {
                for (int i = 0; i < maxDepth - longs.size(); i++) {
                    longs.add(i, 0L);
                }
            }
        }
        List<Long> lowId = new ArrayList<>();
        //把table按照深度倒叙按照列拉平，保证计算顺序:有点绕需要好好考虑：轻易不要改
        for (int i = 0; i < maxDepth; i++) {
            Set<Long> col = new HashSet<>();
            for (List<Long> longs : table) {
                if (longs.get(i) != 0L) {
                    col.add(longs.get(i));
                }
            }
            lowId.addAll(col);
        }
        return lowId;
    }
}
