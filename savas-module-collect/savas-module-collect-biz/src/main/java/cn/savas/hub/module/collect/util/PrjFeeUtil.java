package cn.savas.hub.module.collect.util;

import cn.savas.hub.module.collect.dal.dataobject.BaseCostEntity;
import cn.savas.hub.module.collect.dal.dataobject.BaseFeeEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Log4j2
public class PrjFeeUtil {

    /**
     * 转化成树形结构, 从顶部汇总计算所有子类节点
     *
     * @param list
     * @return
     */
    public static List<? extends BaseFeeEntity> mergeFeeMapFromTop(List<? extends BaseFeeEntity> list) {
        List<? extends BaseFeeEntity> tree = listToTree(list);
        for (BaseFeeEntity prj : list) {
            calculateAndMergeFees(prj);
        }

        return tree;
    }

    /**
     * list 转 tree
     *
     * @param datas
     * @return
     */
    private static <T extends BaseFeeEntity> List<T> listToTree(List<T> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return Collections.emptyList();
        }
        List<T> result = new ArrayList<>();
        Map<Number, T> dataMap = new HashMap<>(datas.size());
        for (T data : datas) {
            dataMap.put(data.getId(), data);
        }
        datas.forEach(data -> {
            // 获取父ID
            Number pid = data.getPid();
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                result.add(data);
                return;
            }
            // 获取父数据
            T pData = dataMap.get(pid);
            List<BaseFeeEntity> children = pData.getChildren();
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            pData.setChildren(children);
        });
        return result;
    }


    /**
     * 递归汇总费用，从子节点到父节点
     */
    private static <T> Map<String, BigDecimal> calculateAndMergeFees(BaseFeeEntity base) {
        // 获取当前节点的费用 Map，如果为空则初始化
        Map<String, BigDecimal> currentFeeMap = base.getFeeMap();
        if (currentFeeMap == null) {
            currentFeeMap = new HashMap<>();
        }
        // 遍历子节点，递归计算费用并合并到当前节点
        List<BaseFeeEntity> children = base.getChildren();
        if (children != null) {
            for (BaseFeeEntity prj : children) {
                Map<String, BigDecimal> childFeeMap = calculateAndMergeFees(prj);
                mergeMapFee(currentFeeMap, childFeeMap);
            }
        }
        // 更新当前节点的费用 Map
        base.setBytes(currentFeeMap);
        return currentFeeMap;

    }

    @SafeVarargs
    public static void mergeMapFee(Map<String, BigDecimal> main, Map<String, BigDecimal>... sub) {
        mergeMapFee(main, Arrays.asList(sub));
    }

    /**
     * 费用合并到主表,有做加和 没有添加
     *
     * @param main
     */
    public static Map<String, BigDecimal> mergeMapFee(Map<String, BigDecimal> main, List<Map<String, BigDecimal>> sublist) {
        if (main == null) {
            main = new HashMap<>();
        }
        // 将子节点的费用合并到当前节点
        for (Map<String, BigDecimal> map : sublist) {
            for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
                String feeKey = entry.getKey();
                BigDecimal feeValue = entry.getValue();
                // 累加相同的费用项，否则新增
                main.put(feeKey, main.getOrDefault(feeKey, BigDecimal.ZERO).add(feeValue));
            }
        }
        return main;
    }

    /**
     * 转成Tree
     */
    public static List<? extends BaseFeeEntity> baseListToTree(List<? extends BaseFeeEntity> list) {
        List<BaseFeeEntity> result = new ArrayList<>();
        Map<Number, BaseFeeEntity> dataMap = new HashMap<>(list.size());
        for (BaseFeeEntity data : list) {
            data.setChildren(new ArrayList<>());
            dataMap.put(data.getId(), data);
        }
        list.forEach(data -> {
            // 获取父ID
            Number pid = data.getPid();
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                result.add(data);
                return;
            }
            // 获取父数据
            BaseFeeEntity pData = dataMap.get(pid);
            List<BaseFeeEntity> children = pData.getChildren();
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            pData.setChildren(children);
        });
        return result;
    }

    /**
     * 转成Tree
     */
    public static List<? extends BaseCostEntity> baseCostListToTree(List<? extends BaseCostEntity> list) {
        List<BaseCostEntity> result = new ArrayList<>();
        Map<Number, BaseCostEntity> dataMap = new HashMap<>(list.size());
        for (BaseCostEntity data : list) {
            data.setCostChildren(new ArrayList<>());
            dataMap.put(data.getId(), data);
        }
        list.forEach(data -> {
            // 获取父ID
            Number pid = data.getPid();
            // 找不到父ID的情况，不做处理
            if (!dataMap.containsKey(pid)) {
                result.add(data);
                return;
            }
            // 获取父数据
            BaseCostEntity pData = dataMap.get(pid);
            List<BaseCostEntity> children = pData.getCostChildren();
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }
            children.add(data);
            pData.setCostChildren(children);
        });
        return result;
    }

    /**
     * 递归计算每一级数据合计并保存到
     */
    public static Map<String, BigDecimal> summeryChildFee(List<? extends BaseFeeEntity> list) {
        Map<String, BigDecimal> map = new HashMap<>();
        for (BaseFeeEntity node : list) {
            mergeMapFee(map, node.getFeeMap());
        }
        return map;
    }

    /**
     * 汇总费用
     *
     * @param node
     * @param nodeFee
     */
    private static void summeryChildFee(BaseFeeEntity node, Map<String, BigDecimal> nodeFee) {
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (BaseFeeEntity child : node.getChildren()) {
                if (child.getChildren() == null || child.getChildren().isEmpty()) {
                    Map<String, BigDecimal> childFees = child.getFeeMap();
                    if (childFees != null) {
                        for (Map.Entry<String, BigDecimal> entry : childFees.entrySet()) {
                            String key = entry.getKey();
                            BigDecimal value = entry.getValue();
                            // 如果 map 中已存在该键，则累加值；否则新增该键值对
                            nodeFee.put(key, nodeFee.getOrDefault(key, BigDecimal.ZERO).add(value));
                        }
                    }
                } else {
                    summeryChildFee(child, nodeFee);
                }
            }
        }
    }

}
