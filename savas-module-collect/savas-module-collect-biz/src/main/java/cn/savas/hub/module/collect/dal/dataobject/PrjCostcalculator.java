package cn.savas.hub.module.collect.dal.dataobject;

import cn.savas.hub.module.collect.dal.handler.CalcConfigHandler;
import cn.savas.hub.module.collect.dal.handler.CalcConfigHandlerV2;
import cn.savas.hub.module.collect.dal.handler.dto.InterpolationFeeRuleVO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @TableName prj_CostCalculator
 */
@TableName(value = "prj_CostCalculator", autoResultMap = true)
@Data
public class PrjCostcalculator implements Serializable {
    /**
     *
     */
    @TableId(value = "calc_id")
    private Long calcId;

    /**
     *
     */
    @TableField(value = "calc_pid")
    private Long calcPid;

    /**
     *
     */
    @TableField(value = "calc_hostmodel")
    private Long calcHostModel;

    /**
     *
     */
    @TableField(value = "calc_name")
    private String calcName;

    /**
     * v1 配置器规则
     */
    @TableField(exist = false)
    private InterpolationFeeRuleVO calcConfig;

    /**
     * v2 发生变化,只存值
     */
    @TableField(value = "calc_configerxml")
    private String xml;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}