package cn.savas.hub.module.collect.framework.web.config;

import cn.savas.hub.framework.swagger.config.SavasSwaggerAutoConfiguration;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * system 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class CollectWebConfiguration {

    /**
     * system 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi collectGroupedOpenApi() {
        return SavasSwaggerAutoConfiguration.buildGroupedOpenApi("collect");
    }

}
