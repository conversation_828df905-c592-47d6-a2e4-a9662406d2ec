package cn.savas.hub.module.collect.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.collect.dal.dataobject.PrjTestingengineering;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/18 14:51
 */
@Mapper
public interface PrjTestingengineeringMapper extends BaseMapperX<PrjTestingengineering> {
    default List<PrjTestingengineering> selectByWbsIds(List<Long> wbsIds){
        if (wbsIds == null || wbsIds.isEmpty()) {
            return new ArrayList<>();
        }
        return this.selectList(
                new LambdaQueryWrapper<PrjTestingengineering>()
                        .in(PrjTestingengineering::getEngPid, wbsIds)
        );
    }

    default List<PrjTestingengineering> selectByProjectId(Long projectId){
        return this.selectList(
                new LambdaQueryWrapper<PrjTestingengineering>()
                        .eq(PrjTestingengineering::getEngOrginalid, projectId)
        );
    }
}
