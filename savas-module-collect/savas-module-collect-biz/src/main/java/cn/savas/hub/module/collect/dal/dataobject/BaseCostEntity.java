package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @TableName prj_CostCalculator
 */
@Data
public abstract class BaseCostEntity extends BaseFeeEntity {

    @TableField(exist = false)
    private List<BaseCostEntity> costChildren;

    /**
     * 单价公式
     */
    public abstract String getFeeExpense();

    /**
     * 费用标签
     */
    public abstract String getFeeCostTag();

    /**
     * 计算式
     */
    public abstract String getFeeExpression();

    /**
     * 设置计算式
     *
     * @param expression
     */
    public abstract void setFeeExpression(String expression);

    /**
     * 设置fee 值
     */
    public abstract void setFeeValue(BigDecimal value);

    /**
     * 设置fee 值
     */
    public abstract void setFeeRate(BigDecimal rate);

    /**
     * 设置fee 值
     */
    public abstract void setForeignValue(BigDecimal rate);

    /**
     * 设置fee 值
     */
    public abstract BigDecimal getFeeValue();

    /**
     * 获取 税率
     */
    public abstract BigDecimal getFeeTaxRate();

    /**
     * 获取 费率
     */
    public abstract BigDecimal getFeeRate();

    /**
     * 获取 外币费
     */
    public abstract BigDecimal getFeeForeignValue();

    /**
     * 1 工程其他费 2 其他费
     * @return
     */
    public abstract Integer getFeeType();
}