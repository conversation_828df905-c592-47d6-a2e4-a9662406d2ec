package cn.savas.hub.module.collect.test;

import cn.savas.hub.framework.common.client.util.V9BinaryToMapUtil;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestReadUtil {



    public static void main(String[] args) {
        //对比结果
        //compareResult();
        //获取元数据
        JdbcTemplate template = new JdbcTemplate(getMainDataSource());
        String sql = "select * from prj_engineering where eng_code in ('CV','EQ','FU','ST') ";
        List<Map<String, Object>> list = template.queryForList(sql);
        Map<String, BigDecimal> map=new HashMap<>();
        for (Map<String, Object> objectMap : list) {
            Map<String, BigDecimal> data = V9BinaryToMapUtil.unpackData((byte[]) objectMap.get("eng_parameters"));
            for (String key:data.keySet()){
                if ("AZ_SGZZS".equals(key)) {
                    System.out.println(data.get(key));
                }
            }
        }
//        map.forEach((k, v) -> {
//            System.out.println(k + " : " + v);
//        });


    }

    private static void compareResult() {
        JdbcTemplate template = new JdbcTemplate(getDataSource());
        String sql = "select * from prj_engineering where eng_id='372426271604342784' ";
        Map<String, Object> map = template.queryForMap(sql);
        Map<String, BigDecimal> data = V9BinaryToMapUtil.unpackData((byte[]) map.get("eng_parameters"));
        data.forEach((k, v) -> {
            System.out.println(k + " : " + v);
        });


        JdbcTemplate template2 = new JdbcTemplate(getMainDataSource());
        String sql2 = "select * from prj_engineering where eng_id='372426271604342784' ";
        Map<String, Object> map2 = template2.queryForMap(sql2);
        Map<String, BigDecimal> data2 = V9BinaryToMapUtil.unpackData((byte[]) map2.get("eng_parameters"));
        data2.forEach((k, v) -> {
            System.out.println(k + " : " + v);
        });
    }

    private static DataSource getMainDataSource() {

        DriverManagerDataSource dataSource = new DriverManagerDataSource();

        dataSource.setDriverClassName("org.sqlite.JDBC");

        dataSource.setUrl("*********************** Files\\destTop\\v9协同版\\数据demo\\main.db");
        return dataSource;

    }

    private static DataSource getDataSource() {

        DriverManagerDataSource dataSource = new DriverManagerDataSource();

        dataSource.setDriverClassName("org.sqlite.JDBC");

        dataSource.setUrl("*********************** Files\\destTop\\v9协同版\\数据demo\\新建项目1.db");
        return dataSource;

    }

}
