package cn.savas.hub.module.collect.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.savas.hub.framework.common.client.util.V9PackageUtil;
import cn.savas.hub.framework.common.client.util.VersionInfo;
import cn.savas.hub.module.client.api.project.ClientProjectFileApi;
import cn.savas.hub.module.client.api.project.dto.ClientProjectFileSRespDTO;
import cn.savas.hub.module.collect.api.ErrorCodeConstants;
import cn.savas.hub.module.collect.event.SumFilePackageCallbackEvent;
import cn.savas.hub.module.infra.api.file.FileApi;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
@Log4j2
public class PackageServiceImpl implements PackageService {

    @Resource
    private ClientProjectFileApi clientProjectFileApi;
    @Resource
    private FileApi fileApi;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Master
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void packageFileAndRecord(Long projectId, String filePath, String dbPath, Long projectFileId){
        try {
            // 打包文件
            byte[] mainBytes = fileApi.getFileContent(filePath);
            VersionInfo versionInfo = V9PackageUtil.getVersionFromFile(mainBytes);
            String newPath = FileUtil.getParent(filePath, 1) + File.separator + IdWorker.get32UUID() + ".svscp";
            V9PackageUtil.zipDBToFile(versionInfo, dbPath, newPath);

            // 更新记录
            ClientProjectFileSRespDTO latestFileRecord = clientProjectFileApi.getLatestCollectProjectFile(projectId);
            clientProjectFileApi.setActiveFalse(projectId);
            String newFileUrl = fileApi.createFile(newPath, null, IoUtil.readBytes(Files.newInputStream(new File(newPath).toPath())));
            log.info("打包后:db路径:{},项目文件路径:{},minio地址:{}", dbPath, newPath, newFileUrl);

            latestFileRecord.setId(null);
            latestFileRecord.setVersion(latestFileRecord.getVersion() + 1);
            latestFileRecord.setPath(StringUtils.substringAfterLast(newFileUrl, "/"));
            latestFileRecord.setUrl(newFileUrl);
            latestFileRecord.setIsActive(true);
            latestFileRecord.setCreator(null);
            latestFileRecord.setProjectFileId(projectFileId);
            clientProjectFileApi.insertProjectMergeFileSummary(latestFileRecord);
            //汇总文件数据更新到表
            clientProjectFileApi.pullProjectFileSummary(projectId, dbPath);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("未能为projectId打包文件: {}", projectId, e);
            throw exception(ErrorCodeConstants.PACKAGE_ERROR);
        }
        // 发布回调事件
        SumFilePackageCallbackEvent event = new SumFilePackageCallbackEvent(this);
        event.setProjectId(projectId);
        event.setDbPath(dbPath);
        applicationEventPublisher.publishEvent(event);
        log.info("为projectId打包文件完成: {}", projectId);
    }
}
