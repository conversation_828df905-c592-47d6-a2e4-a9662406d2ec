package cn.savas.hub.module.collect.util.aviator2;

import cn.hutool.core.convert.Convert;
import cn.savas.hub.module.collect.enums.OtherFeeKeyEnum;
import lombok.extern.log4j.Log4j2;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 单价公式计算
 */
@Log4j2
public class ExpenseUtil {


    /**
     * 初始化单价公式计算Map
     * @param value 基础费用
     * @param taxRate 费率
     * @param foreignAmount 外币费
     * @return
     */
    public static Map<String, BigDecimal> initExpenFeeMap(BigDecimal value, BigDecimal taxRate, BigDecimal foreignAmount) {
        Map<String, BigDecimal> baseFee = new HashMap<>();
        baseFee.put(OtherFeeKeyEnum.FEE.name(), value);
        baseFee.put(OtherFeeKeyEnum.Value.name(), value);
        baseFee.put(OtherFeeKeyEnum.TaxRate.name(), taxRate);
        baseFee.put(OtherFeeKeyEnum.ForeignValue.name(), Convert.toBigDecimal(foreignAmount, BigDecimal.ZERO));
        return baseFee;

    }

}
