package cn.savas.hub.module.collect.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:48
 */
@Component
public class ProductMergeStrategyResolver {
    private final List<ProductTestingFeeStrategy> strategies;

    @Autowired
    public ProductMergeStrategyResolver(List<ProductTestingFeeStrategy> strategies) {
        this.strategies = strategies;
    }

    public ProductTestingFeeStrategy resolve(String product) {
        return strategies.stream()
                .filter(strategy -> strategy.supports(product))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未找到适合产品 " + product + " 的策略"));
    }
}
