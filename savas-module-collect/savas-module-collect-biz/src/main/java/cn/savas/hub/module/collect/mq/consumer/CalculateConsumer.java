package cn.savas.hub.module.collect.mq.consumer;

import cn.savas.hub.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import cn.savas.hub.module.collect.mq.msg.CalculateRedisStreamMessage;
import cn.savas.hub.module.collect.service.SqliteCollectService;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;

/**
 * // 重点：继承 AbstractRedisStreamMessageListener 类，并填写对应的 Message 类
 * 不要在此类中声明数据源切换,不生效,需要到server中
 */
@Log4j2
@Component
public class CalculateConsumer extends AbstractRedisStreamMessageListener<CalculateRedisStreamMessage> {

    @Resource
    private SqliteCollectService sqliteCollectService;

    @SneakyThrows
    @Override
    public void onMessage(CalculateRedisStreamMessage message) {
//        log.info("自定义收到消息，{} ",message.getStreamKey());
//        //暂停30s
//        Thread.sleep(5000);
//        log.info("完成，stream={} ", message.getStreamKey());
        try {
            // 开始计算
            sqliteCollectService.calculateSqliteData(message);
            log.info("消费成功:{}", message);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.info("消费失败:{}", e.getMessage());
        }
    }

}
