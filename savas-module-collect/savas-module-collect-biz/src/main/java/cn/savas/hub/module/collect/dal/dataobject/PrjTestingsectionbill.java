package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.ByteArrayTypeHandler;

/**
 * <AUTHOR>
 * @date 2025/6/18 14:45
 */
@Data
@TableName(value ="prj_testingsectionbill",autoResultMap = true)
public class PrjTestingsectionbill {
    @TableId
    private Long billId;
    private Long billPid;
    private Long billHostmodel;
    private Long billOrginalid;
    private String billSequence;
    private String billCode;
    private String billName;
    private String billExpense;
    private String billStatistics;
    private String billUnit;
    private String billExpression;
    private Integer billMark;
    private Integer billSortid;
    private Long billState;
    @TableField(typeHandler = ByteArrayTypeHandler.class)
    private byte[] billParameters;
    private Integer billStyle;
    private Long billDesignconditionhash;
    private String billProfessionalcode;
    private String billDescription;
}
