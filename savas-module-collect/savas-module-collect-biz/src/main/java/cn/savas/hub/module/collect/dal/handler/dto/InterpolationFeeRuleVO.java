package cn.savas.hub.module.collect.dal.handler.dto;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.Data;

import java.util.List;

/**
 * 插值法对应解析对象
 */
@Data
@XStreamAlias("Rules")
public class InterpolationFeeRuleVO {

    @XStreamAsAttribute
    @XStreamAlias("BasicExpression")
    private String basicExpression;

    @XStreamAsAttribute
    @XStreamAlias("Multiple")
    private String multiple;

    @XStreamAsAttribute
    @XStreamAlias("BaseCaption")
    private String baseCaption;

    @XStreamImplicit
    private List<Rule> rules;

    @XStreamAlias("factor")
    private Factor factor;

    @Data
    @XStreamAlias("Rule")
    public static class Rule {

        @XStreamAsAttribute
        @XStreamAlias("Name")
        private String name;

        @XStreamAsAttribute
        @XStreamAlias("Value")
        private String value;

        @XStreamImplicit
        private List<Item> items;
    }

    @Data
    @XStreamAlias("Item")
    public static class Item {
        @XStreamAsAttribute
        @XStreamAlias("Style")
        private String style;

        @XStreamAsAttribute
        @XStreamAlias("X1")
        private String x1;

        @XStreamAsAttribute
        @XStreamAlias("Y1")
        private String y1;

        @XStreamAsAttribute
        @XStreamAlias("X2")
        private String x2;

        @XStreamAsAttribute
        @XStreamAlias("Y2")
        private String y2;

        @XStreamAsAttribute
        @XStreamAlias("Value")
        private String value;
    }

    @Data
    @XStreamAlias("factor")
    public static class Factor {

        @XStreamImplicit
        List<Component> componentList;
    }

    @Data
    @XStreamAlias("component")
    public static class Component {
        @XStreamAsAttribute
        @XStreamAlias("Style")
        private String style;

        @XStreamAsAttribute
        @XStreamAlias("Caption")
        private String caption;

        @XStreamAsAttribute
        @XStreamAlias("Name")
        private String name;

        @XStreamAsAttribute
        @XStreamAlias("Value")
        private String value;

        @XStreamAsAttribute
        @XStreamAlias("Operator")
        private String operator;

        @XStreamAsAttribute
        @XStreamAlias("Checked")
        private String checked;

        @XStreamAsAttribute
        @XStreamAlias("Unchecked")
        private String unchecked;
    }
}
