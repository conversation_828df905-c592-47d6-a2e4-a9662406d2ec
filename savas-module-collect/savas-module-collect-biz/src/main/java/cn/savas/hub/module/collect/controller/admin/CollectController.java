package cn.savas.hub.module.collect.controller.admin;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.framework.mq.redis.core.RedisMQTemplate;
import cn.savas.hub.module.collect.api.dto.ClientMergeProjectFileDTO;
import cn.savas.hub.module.collect.mq.consumer.CalculateConsumer;
import cn.savas.hub.module.collect.mq.msg.CalculateRedisStreamMessage;
import cn.savas.hub.module.collect.mq.register.AutoListenerRegister;
import cn.savas.hub.module.collect.service.SqliteCollectService;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import java.util.Arrays;
import java.util.Collections;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;

@Tag(name = "数据同步 - collect")
@RestController
@RequestMapping("/collect/data")
@Validated
@Log4j2
public class CollectController {

    @Resource
    private RedisMQTemplate redisMQTemplate;
    @Resource
    private AutoListenerRegister autoListenerRegister;
    @Resource
    private SqliteCollectService sqliteCollectService;

    @PermitAll
    @PostMapping("/importMq/{projectId}")
    @Operation(summary = "合并数据")
    public CommonResult<Boolean> collectDataMq(@PathVariable String projectId) throws Exception {
        CalculateRedisStreamMessage msg = getCollectRedisStreamMessage(projectId);
        redisMQTemplate.send(msg);
        return success(true);
    }

    private static CalculateRedisStreamMessage getCollectRedisStreamMessage(String projectId) {
        CalculateRedisStreamMessage msg = new CalculateRedisStreamMessage();
        msg.setMainDB("D:\\Program Files\\destTop\\sqlite\\新建项目30.db");
        msg.setWbsIds(Arrays.asList(457909495394225163L));
        msg.setProjectId(Long.valueOf(projectId));
        return msg;
    }

    @PermitAll
    @GetMapping("/add-listener/{projectId}")
    @Operation(summary = "动态注册监听器")
    public CommonResult<Boolean> addListener(@PathVariable String projectId) throws Exception {
        autoListenerRegister.autoRegisterListener(projectId, new CalculateConsumer());
        CalculateRedisStreamMessage msg = getCollectRedisStreamMessage(projectId);
        redisMQTemplate.send(msg);
        return success(true);
    }

    @PermitAll
    @PostMapping("/test2.0")
    @Operation(summary = "合并数据")
    public CommonResult<Boolean> testcollect2() throws Exception {
        String str = "{\"increaseEngVersion\":false,\"mainPath\":\"19972b79c4bf9b955b7b97c6055162e202c54b03127528c7f762e4d8c1773387.svscp\",\"product\":\"1-1-1\",\"projectFileId\":2479,\"projectId\":463642916884054144,\"slavePath\":\"0471b332b4a4507b2e62834dd9f8742e9ffc6d29a60bf5b668bb430c33932e9f.svscp\",\"wbsIds\":[463642967081502373]}";
        ClientMergeProjectFileDTO msg = JSON.parseObject(str, ClientMergeProjectFileDTO.class);
        sqliteCollectService.mergeProjectData(msg, "C:\\Users\\<USER>\\AppData\\Local\\Temp\\hutool8666791958504999781.db", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\hutool1753405717628648197.db");
        return success(true);
    }

    @PermitAll
    @PostMapping("/test3.0")
    @Operation(summary = "合并数据")
    public CommonResult<Boolean> testcollect3() throws Exception {
        CalculateRedisStreamMessage msg = new CalculateRedisStreamMessage();
//        msg.setProduct("1-1-64");
//        msg.setMainDB("D:\\Program Files\\destTop\\sqlite\\67655f31040f4ec49d4d765fc9888b50.db")
//        .setWbsIds(Collections.singletonList(480757375704255488L));
        msg.setProduct("1-1-1");
        msg.setMainDB("D:\\Program Files\\destTop\\sqlite\\2019-5.db")
                .setWbsIds(Collections.singletonList(481028476707536896L));
        sqliteCollectService.calculateSqliteData(msg);
        return success(true);
    }
}
