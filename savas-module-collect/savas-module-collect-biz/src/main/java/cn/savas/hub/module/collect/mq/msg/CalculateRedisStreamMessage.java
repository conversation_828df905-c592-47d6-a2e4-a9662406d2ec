package cn.savas.hub.module.collect.mq.msg;

import cn.savas.hub.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class CalculateRedisStreamMessage extends AbstractRedisStreamMessage {

    /**
     * 公共库 绝对地址(全路径地址)
     */
    @NotEmpty(message = "公共库文件")
    private String mainFilePath;

    /**
     * 公共库 db
     */
    private String mainDB;

    @Size(min = 1, message = "wbsIds 不能为空")
    private List<Long> wbsIds;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 个人库文件ID
     */
    private Long projectFileId;

    /**
     * 产品标识
     */
    private String product;

    @Override
    public String getStreamKey() {
        return String.valueOf(projectId);
    }
}
