package cn.savas.hub.module.collect.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 概算结构影响的表汇总
 * 表名小写
 */
@Getter
@AllArgsConstructor
public enum EngineerCollectTableEnum {
    prj_comments("prj_comments","comment_hostmodel","批注类"),
    prj_engineering("prj_engineering","eng_id","单位工程"),
    prj_specialty("prj_specialty","spec_hostmodel","单位工程专业分支"),
    prj_sectionbill("prj_sectionbill","bill_hostmodel","分部"),
    prj_norm("prj_norm","norm_hostmodel","定额"),
    prj_normconversion("prj_normconversion","Conv_hostmodel","定额换算"),
    prj_consumption("prj_consumption","cons_hostmodel","工料机"),
    prj_ConsumptionConversion("prj_ConsumptionConversion","Conv_hostmodel","非标设备换算"),
    prj_expense("prj_expense","Exp_HostModel","单价公式(主)"),
    prj_expenseitem("prj_expenseitem","Exp_HostModel","单价公式(从)"),
    prj_designcondition("prj_designcondition","design_hostmodel","设计条件"),
    prj_Lecture("prj_Lecture","lect_hostmodel","编制说明"),
    prj_engineeringcost("prj_engineeringcost","fee_hostmodel","工程其他费"),
    prj_projectcost("prj_projectcost","fee_hostmodel","其他费"),
    prj_projecteng("prj_projecteng","eng_projectid","项目工程"),
    prj_historydata("prj_historydata","hd_HostModel","历史数据"),
    prj_CostCalculator("prj_CostCalculator","calc_hostmodel","其他费计算器表"),
    ;
    /**
     * 表名
     */
    private final String table;
    /**
     * wbsId 所在列
     */
    private final String col;
    /**
     * 表描述
     */
    private final String description;


}
