package cn.savas.hub.module.collect.strategy.impl;

import cn.savas.hub.framework.common.client.enmus.ClientProductEnum;
import cn.savas.hub.module.collect.api.dto.ClientMergeProjectFileDTO;
import cn.savas.hub.module.collect.dal.dataobject.FeeEngineering;
import cn.savas.hub.module.collect.dal.dataobject.PrjEffectFeeCoefficientMulti;
import cn.savas.hub.module.collect.dal.dataobject.PrjEffectFeeSet;
import cn.savas.hub.module.collect.strategy.ProductTestingFeeStrategy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:44
 */
@Component
public class NoTestingFeeTestingFeeStrategy implements ProductTestingFeeStrategy {
    @Override
    public void processMergeFee(ClientMergeProjectFileDTO req) {
        // 不处理检验检测费
    }

    @Override
    public void processCalculateFee(List<Long> wbsIds) {
        // 不处理检验检测费
    }
    @Override
    public void deleteDataByEngId(List<Long> engIds) {
        // 不处理检验检测费
    }

    @Override
    public void mergeEffectFeeSet(List<PrjEffectFeeSet> reqList) {

    }

    @Override
    public void mergeEffectFeeCoefficientMulti(List<PrjEffectFeeCoefficientMulti> reqList) {

    }

    @Override
    public boolean supports(String product) {
        return ClientProductEnum.PRODUCT_19.getCode().equals(product);
    }


}
