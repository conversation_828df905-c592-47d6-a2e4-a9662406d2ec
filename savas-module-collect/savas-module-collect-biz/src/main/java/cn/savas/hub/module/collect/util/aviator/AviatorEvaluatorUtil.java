package cn.savas.hub.module.collect.util.aviator;

import cn.hutool.core.convert.Convert;
import cn.savas.hub.framework.common.util.number.NumberUtils;
import cn.savas.hub.module.collect.util.TableUtil;
import com.google.common.collect.Table;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.log4j.Log4j2;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Log4j2
public class AviatorEvaluatorUtil {

    /**
     * 直接计算数据
     *
     * @return
     */
    public static BigDecimal executeDirect(String formula, Table<String, String, BigDecimal> table, BigDecimal rate) {
        //格式化公式去掉其中的@
        String validFormula = AviatorFormulaUtil.formatFormula(formula);
        //公式化Max
        String realFormula = AviatorFormulaUtil.modifyFormula(validFormula);
        //根据formula获取资源code
        Map<String, Object> objectMap = getExecuteMapByTable(formula, table);
        Object execute = null;
        try {
            execute = AviatorEvaluator.execute(realFormula, objectMap);
        } catch (Exception e) {
           log.error("公式:{},参数:{},报错:{}",realFormula,objectMap,e.getMessage());
            execute = BigDecimal.ZERO;
        }
        return Convert.toBigDecimal(execute, BigDecimal.ZERO).multiply(rate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 直接计算数据
     *
     * @return
     */
    public static BigDecimal executeDirect(String formula, Map<String, BigDecimal> map, BigDecimal rate) {
        //公式化Max
        String realFormula = AviatorFormulaUtil.modifyFormula(formula);
        Map<String, Object> objectMap = map.entrySet().stream().filter(entry -> entry.getKey() != null && entry.getValue() != null).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        Object execute = AviatorEvaluator.execute(realFormula, objectMap);
        return Convert.toBigDecimal(execute, BigDecimal.ZERO).multiply(rate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 公式获取所有 变量, 通过变量 组织 Map
     *
     * @param validFormula
     * @param table
     * @return
     */
    private static Map<String, Object> getExecuteMapByTable(String validFormula, Table<String, String, BigDecimal> table) {
        Map<String, Object> map = new HashMap<>();
        Set<String> keys = AviatorFormulaUtil.formatFormulaParameters(validFormula);
        for (String key : keys) {
            //非数字
            if (!NumberUtils.isNumber(key)) {
                if (key.contains("@")) {
                    String[] split = key.split("@");
                    BigDecimal value = TableUtil.getOrDef(table, split[1], split[0]);
                    map.put(split[0] + "__" + split[1], value);
                    continue;
                }
                if (key.contains("#")) {
                    String[] split = key.split("#");
                    BigDecimal value = TableUtil.getOrDef(table, "param", split[1]);
                    map.put("param." + split[1], value);
                    continue;
                }
                if (table.containsRow(key)) {
                    BigDecimal value = TableUtil.getOrDef(table, key, "FEE");
                    map.put(key, value);
                    continue;
                }
                BigDecimal value = TableUtil.getOrDef(table, "base", key);
                map.put(key, value);
            }
        }
        return map;
    }


}
