package cn.savas.hub.module.collect.manager;

import cn.hutool.core.map.MapUtil;
import cn.savas.hub.framework.common.client.enmus.ENGTypeEnum;
import cn.savas.hub.module.collect.dal.dataobject.BaseCostEntity;
import cn.savas.hub.module.collect.dal.dataobject.BaseFeeEntity;
import cn.savas.hub.module.collect.enums.OtherFeeKeyEnum;
import cn.savas.hub.module.collect.util.MapDecimalUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Component
@Log4j2
public class OtherFeeManager {

    /**
     * 初始化其他费基础费用
     * 补充费率信息
     * @param addBase 是否添加基础费用
     * @return
     */
    public Map<String, BigDecimal> addRowFeeToCache(Map<String, BigDecimal> cacheMap, String code, Map<String, BigDecimal> rowMap, boolean addBase, Map<String, BigDecimal> rateMap) {
        if (cacheMap == null) {
            cacheMap = MapUtil.newHashMap();
        }
        if (rowMap == null) {
            return cacheMap;
        }
        //添加code:value
        cacheMap.put(code, rowMap.getOrDefault(OtherFeeKeyEnum.FEE.name(), BigDecimal.ZERO));
        //添加FEE__code:value 对应Fee@F003
        cacheMap.put(OtherFeeKeyEnum.FEE.name() + "__" + code, rowMap.getOrDefault(OtherFeeKeyEnum.FEE.name(), BigDecimal.ZERO));
        for (String key : rowMap.keySet()) {
            if (addBase) {
                cacheMap.put(key, rowMap.get(key));
            }
            cacheMap.put(key + "_" + code, rowMap.get(key));
        }
        if (rateMap != null) {
            for (String key : rateMap.keySet()) {
                cacheMap.put(OtherFeeKeyEnum.rate_.name() + key, rateMap.getOrDefault(key, BigDecimal.ZERO));
            }
        }
        return cacheMap;
    }

    /**
     * 计算@@为所有属性全量相加
     */
    public Map<String, BigDecimal> computeDoubleAtFormula(Set<String> codes, Map<String, BaseCostEntity> otherRowMap) {
        List<Map<String, BigDecimal>> feeMaps = new ArrayList<>();
        for (String code : codes) {
            BaseCostEntity otherDO = otherRowMap.get(code);
            if (otherDO != null) {
                feeMaps.add(otherDO.getFeeMap());
            }
        }
        return MapDecimalUtil.mergeMap(feeMaps);

    }

    /**
     * 递归获取所有有效的数据ID
     * 去重
     */
    private void searchSingleFromTree(Long wbsId, Map<Long, BaseFeeEntity> map, List<Long> ids, Boolean first) {
        BaseFeeEntity eng = map.get(wbsId);
        if (first && (eng.getEngClass() == null || !eng.getEngClass().equals(ENGTypeEnum.DANWEI.getCode()))) {
            ids.add(eng.getId());
        }
        BaseFeeEntity father = map.get(eng.getPid());
        if (father != null) {
            ids.add(father.getId());
            searchSingleFromTree(father.getId(), map, ids, false);
        }
    }

    /**
     * 需要对数据去掉关联影响
     *
     * @param
     * @param map
     * @return
     */
    public List<BaseFeeEntity> getLowChangeWbsList(List<Long> lowIds, Map<Long, BaseFeeEntity> map) {
        List<BaseFeeEntity> list = new ArrayList<>();
        for (Long id : lowIds) {
            BaseFeeEntity wbs = map.get(id);
            list.add(wbs);
        }
        log.info("受影响的wbs个数：{} 数据:{}", list.size(), list);
        return list;
    }

    /**
     * 获取受影响的呃最底层id
     *
     * @return
     */
    public List<Long> getLowWbsIds(List<Long> wbsIds, Map<Long, BaseFeeEntity> map) {
        List<List<Long>> table = new ArrayList<>();
        for (Long wbsId : wbsIds) {
            List<Long> ids = new ArrayList<>();
            searchSingleFromTree(wbsId, map, ids, true);
            table.add(ids);
        }
        //按照从大到小排序
        table.sort((a, b) -> Integer.compare(b.size(), a.size()));
        int maxDepth = table.get(0).size();
        //补全，从0开始补充到最大
        for (List<Long> longs : table) {
            if (longs.size() < maxDepth) {
                int size=maxDepth - longs.size();
                for (int i = 0; i < size; i++) {
                    longs.add(i, 0L);
                }
            }
        }
        List<Long> lowId = new ArrayList<>();
        //把table按照深度倒叙按照列拉平，保证计算顺序:有点绕需要好好考虑：轻易不要改
        for (int i = 0; i < maxDepth; i++) {
            Set<Long> col = new HashSet<>();
            for (List<Long> longs : table) {
                if (longs.get(i) != 0L) {
                    col.add(longs.get(i));
                }
            }
            lowId.addAll(col);
        }
        return lowId;
    }
}
