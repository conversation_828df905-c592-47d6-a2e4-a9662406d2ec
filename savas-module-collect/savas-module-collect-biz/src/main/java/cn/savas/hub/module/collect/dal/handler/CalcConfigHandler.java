package cn.savas.hub.module.collect.dal.handler;

import cn.savas.hub.framework.common.util.json.JsonUtils;
import cn.savas.hub.module.collect.dal.handler.dto.InterpolationFeeRuleVO;
import cn.savas.hub.framework.common.util.io.XStreamUtil;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;

public class CalcConfigHandler extends AbstractJsonTypeHandler<Object> {

    public CalcConfigHandler(Class<?> type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        return XStreamUtil.XML2Bean(json.trim(), InterpolationFeeRuleVO.class);
    }

    @Override
    public String toJson(Object obj) {
        return JsonUtils.toJsonString(obj);
    }
}
