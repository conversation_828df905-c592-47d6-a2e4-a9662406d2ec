package cn.savas.hub.module.collect.dal.mapper;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.collect.dal.dataobject.FeeEngineering;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【prj_engineering】的数据库操作Mapper
* @createDate 2024-10-25 09:07:06
* @Entity cn.savas.hub.module.collect.dal.dataobject.PrjEngineering
*/
@Mapper
public interface PrjEngineeringMapper extends BaseMapperX<FeeEngineering> {

    default List<FeeEngineering> selectWbsTree(){
        return this.selectWbsTreeBase(ClientClassIdEnum.PROJECT.getCode());
    }

    List<FeeEngineering> selectWbsTreeBase(@Param("classId")Long classId);

    default void updateDirectorId(Long engId, Long engDirectorid, String engDirectorname){
        this.update(null, new LambdaUpdateWrapper<FeeEngineering>()
                .set(FeeEngineering::getEngDirectorid, engDirectorid)
                .set(FeeEngineering::getEngDirectorname, engDirectorname)
                .eq(FeeEngineering::getEngId, engId));
    }
}




