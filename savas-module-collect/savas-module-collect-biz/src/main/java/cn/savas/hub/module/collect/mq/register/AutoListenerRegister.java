package cn.savas.hub.module.collect.mq.register;

import cn.savas.hub.framework.mq.redis.RedisConstants;
import cn.savas.hub.framework.mq.redis.core.RedisMQTemplate;
import cn.savas.hub.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.connection.stream.*;
import org.springframework.data.redis.stream.StreamMessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 动态注册消费组
 */
@Log4j2
@Component
public class AutoListenerRegister {

    @Resource
    private RedisMQTemplate redisMQTemplate;


    /**
     * 根据projectId 动态创建消费组
     *
     * @param streamKey
     */
    public void autoRegisterListener(String streamKey, AbstractRedisStreamMessageListener<?> listener) {
        Boolean addFlag = this.checkGroupSubscribe(streamKey);
        if (!addFlag) {
            StreamMessageListenerContainer.StreamMessageListenerContainerOptions<String, ObjectRecord<String, String>> containerOptions =
                    StreamMessageListenerContainer.StreamMessageListenerContainerOptions.builder()
                            .batchSize(10) // 一次性最多拉取多少条消息
                            .targetType(String.class) // 目标类型。统一使用 String，通过自己封装的 AbstractStreamMessageListener 去反序列化
                            .build();
            StreamMessageListenerContainer<String, ObjectRecord<String, String>> container = StreamMessageListenerContainer.create(redisMQTemplate.getRedisTemplate().getRequiredConnectionFactory(), containerOptions);
            // 设置 listener 对应的 redisTemplate
            listener.setRedisMQTemplate(redisMQTemplate);
            // 创建 Consumer 对象
            Consumer consumer = Consumer.from(RedisConstants.REDIS_STREAM_GROUP_NAME, this.buildConsumerName(streamKey));
            // 设置 Consumer 监听
            StreamOffset<String> offset = StreamOffset.create(streamKey, ReadOffset.lastConsumed());
            // 设置 Consumer 监听
            StreamMessageListenerContainer.StreamReadRequestBuilder<String> builder = StreamMessageListenerContainer.StreamReadRequest
                    .builder(offset).consumer(consumer)
                    .autoAcknowledge(false) // 不自动 ack
                    .cancelOnError(throwable -> false); // 默认配置，发生异常就取消消费，显然不符合预期；因此，我们设置为 false
            container.register(builder.build(), listener);
            container.start();
            log.info("[redisStreamMessageListenerContainer][完成注册 StreamKey({}) 对应的监听器({})]", streamKey, listener.getClass().getName());
        }
    }

    /**
     * 固定consumerName
     *
     * @return
     */
    private String buildConsumerName(String streamKey) {
        return "consumer_" + streamKey;
    }

    /**
     * 检查是否存在订阅者
     * 检查是否存在消费组:不存在则创建
     *
     * @param streamKey
     * @return
     */
    private Boolean checkGroupSubscribe(String streamKey) {
        try {
            StreamInfo.XInfoGroups groups = redisMQTemplate.getRedisTemplate().opsForStream().groups(streamKey);
            if (checkConsumerValid(streamKey, groups)) {
                //移除所有消费者
                groups.forEach(group -> {
                    redisMQTemplate.getRedisTemplate().opsForStream().destroyGroup(streamKey, group.groupName());
                });
                redisMQTemplate.getRedisTemplate().opsForStream().createGroup(streamKey, RedisConstants.REDIS_STREAM_GROUP_NAME);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("不存在需要创建:{}", streamKey);
            redisMQTemplate.getRedisTemplate().opsForStream().createGroup(streamKey, RedisConstants.REDIS_STREAM_GROUP_NAME);
            return false;
        }
    }

    private boolean checkConsumerValid(String streamKey, StreamInfo.XInfoGroups groups) {
        StreamInfo.XInfoStream streamInfo = redisMQTemplate.getRedisTemplate().opsForStream().info(streamKey);
        String lastId = groups.get(0).lastDeliveredId();
        long waitM = System.currentTimeMillis() - Long.parseLong(lastId.split("-")[0]);
        if (streamInfo.lastGeneratedId().equals(lastId) && waitM > RedisConstants.CONSUMER_DEAD_TIME_FREE) {
            log.info("正常消费完毕,等待时间超时(死机,重启),需要创建:{}", streamKey);
            return true;
        }
        //计算超时,如果超时即为无效
        long diff = Long.parseLong(streamInfo.lastGeneratedId().split("-")[0]) - Long.parseLong(lastId.split("-")[0]);
        if (diff > RedisConstants.CONSUMER_DEFAULT_TIME) {
            log.error("最后一次afk时间 超时,需要创建:{}", streamKey);
            return true;
        }
        return false;
    }


}
