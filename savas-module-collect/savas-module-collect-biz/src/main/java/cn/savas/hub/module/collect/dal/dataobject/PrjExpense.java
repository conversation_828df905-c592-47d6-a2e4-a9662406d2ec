package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName prj_expense
 */
@TableName(value ="prj_expense")
@Data
public class PrjExpense implements Serializable {
    /**
     * 费用ID
     */
    @TableId(value = "Exp_ID")
    private Long expId;

    /**
     * 主模型ID
     */
    @TableField(value = "Exp_HostModel")
    private Long expHostmodel;

    /**
     * 费用名称
     */
    @TableField(value = "Exp_Name")
    private String expName;

    /**
     * 排序ID
     */
    @TableField(value = "Exp_SortID")
    private Integer expSortid;

    /**
     * 特殊代码
     */
    @TableField(value = "Exp_SpecialCode")
    private String expSpecialcode;

    /**
     * 状态
     */
    @TableField(value = "Exp_State")
    private Long expState;

    /**
     * 分类
     */
    @TableField(value = "Exp_Category")
    private String expCategory;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}