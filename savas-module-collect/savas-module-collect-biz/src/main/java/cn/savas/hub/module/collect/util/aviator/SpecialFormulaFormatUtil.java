package cn.savas.hub.module.collect.util.aviator;

import cn.hutool.core.convert.Convert;
import cn.savas.hub.module.collect.dal.handler.dto.InterpolationFeeRuleVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 特殊公式转为正常计算式
 */
@Log4j2
public class SpecialFormulaFormatUtil {

    public static final int AMOUNT = 1;      // 金额 1
    public static final int RATE = 2;        // 费率 2
    public static final int EXCESS = 4;      // 超额 4
    public static final int UNIT_COST = 8;   // 单位费用

    //判断用什么rule
    public static CalculatorResult calculateInterpolation(InterpolationFeeRuleVO ruleVO, BigDecimal value) {
        //获取有效Item
        List<InterpolationFeeRuleVO.Item> itemList = getValidItems(ruleVO);
        //获取真实结果
        BigDecimal realVal = value.divide(Convert.toBigDecimal(ruleVO.getMultiple(), BigDecimal.ONE), 4, RoundingMode.HALF_UP);
        //遍历获取结果
        CalculatorResult calculatorResult = checkValidValByItem(realVal, itemList, Convert.toBigDecimal(ruleVO.getMultiple(), BigDecimal.ONE));
        //对计算过程做最后一步组装
        initResultFormula(calculatorResult, ruleVO, value);

        return calculatorResult;
    }

    /**
     * 根据类型对计算公式做最后一步处理
     * 处理 factor
     *
     * @param calculatorResult
     * @param ruleVO
     * @param value
     */
    private static void initResultFormula(CalculatorResult calculatorResult, InterpolationFeeRuleVO ruleVO, BigDecimal value) {
        StringBuilder expressionBuffer = new StringBuilder();
        if ((calculatorResult.getStyle() & AMOUNT) == AMOUNT) {
            expressionBuffer.append("(").append(calculatorResult.getFormulaExpression()).append(")");
        } else {
            expressionBuffer.append("(").append(ruleVO.getBasicExpression()).append(")");
        }
        for (InterpolationFeeRuleVO.Component component : ruleVO.getFactor().getComponentList()) {
            String valueStr = formatComponentValue(component);
            //过滤选择性的内容 1.输入 2选择
            if (StringUtils.isNotBlank(component.getOperator())) {
                expressionBuffer.append(component.getOperator()).append(valueStr).append("*").append(calculatorResult.getMultiple());
            } else {
                expressionBuffer.append("*").append(valueStr);
            }
        }
        if ((calculatorResult.getStyle() & AMOUNT) == AMOUNT) {
            //返回元为单位的数据
            calculatorResult.setResult(calculatorResult.getResult().multiply(Convert.toBigDecimal(calculatorResult.getMultiple(), BigDecimal.ONE)));
        }
        calculatorResult.setFormulaExpression(expressionBuffer.toString());
    }

    /**
     * 格式化 最终value值  Style 1输入型 2选择型
     *
     * @param component
     * @return
     */
    private static String formatComponentValue(InterpolationFeeRuleVO.Component component) {
        if ("1".equals(component.getStyle())) {
            return component.getValue();
        } else {
            return "true".equalsIgnoreCase(component.getValue()) ? component.getChecked() : component.getUnchecked();
        }
    }

    private static CalculatorResult checkValidValByItem(BigDecimal realVal, List<InterpolationFeeRuleVO.Item> itemList, BigDecimal multiple) {
        for (InterpolationFeeRuleVO.Item item : itemList) {
            //判断是否是递进
            if ((Integer.parseInt(item.getStyle()) & EXCESS) == EXCESS) {
                //获取限额部分(线性插值)
                CalculatorResult limitVal = getLinearByItem(itemList, Convert.toBigDecimal(item.getValue(), BigDecimal.ZERO), multiple);
                //拼装计算过程
                StringBuffer buffer = new StringBuffer().append(limitVal.getResult().multiply(multiple)).append("+");
                //计算当超额部分 超额部分*1.6/100
                BigDecimal beyondVal = realVal.subtract(Convert.toBigDecimal(item.getValue(), BigDecimal.ZERO)).multiply(Convert.toBigDecimal(item.getY1(), BigDecimal.ZERO)).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
                buffer.append("(").append(realVal.multiply(multiple).subtract(Convert.toBigDecimal(item.getValue(), BigDecimal.ZERO).multiply(multiple)))
                        .append(")").append("*").append(item.getY1()).append("/100");
                return new CalculatorResult(AMOUNT, limitVal.getResult().add(beyondVal), multiple, buffer.toString());
            } else {
                if (checkRuleByValue(realVal, item)) {
                    return interpolateByRule(realVal, item, multiple);
                }
            }
        }
        return new CalculatorResult(1, BigDecimal.ZERO, multiple);

    }

    private static CalculatorResult getLinearByItem(List<InterpolationFeeRuleVO.Item> itemList, BigDecimal value, BigDecimal multiple) {
        for (InterpolationFeeRuleVO.Item item : itemList) {
            if (checkRuleByValue(value, item)) {
                return interpolateByRule(value, item, multiple);
            }
        }
        return new CalculatorResult(1, BigDecimal.ZERO, multiple);
    }


    /**
     * 依赖factor内属性
     * 对比方式 component.Style 1.输入 2.勾选
     * 对比方式 component.name= Rule.Name
     *
     * @param ruleVO
     * @return
     */
    private static List<InterpolationFeeRuleVO.Item> getValidItems(InterpolationFeeRuleVO ruleVO) {
        String name = ruleVO.getRules().get(0).getName();
        if (StringUtils.isNotBlank(name)) {
            InterpolationFeeRuleVO.Component component = ruleVO.getFactor().getComponentList().stream().filter(p -> StringUtils.isNotBlank(p.getName())).filter(p -> p.getName().equals(name)).findAny().get();
            List<InterpolationFeeRuleVO.Rule> collect = ruleVO.getRules().stream().filter(p -> p.getValue().equalsIgnoreCase(component.getValue())).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                return collect.get(0).getItems();
            }
        }
        return ruleVO.getRules().get(0).getItems();
    }

    /**
     * 判定是否满足条件
     *
     * @param value
     * @param item
     * @return
     */
    public static boolean checkRuleByValue(BigDecimal value, InterpolationFeeRuleVO.Item item) {
        if ("∞".equals(item.getX2())) {
            return value.doubleValue() > Double.parseDouble(item.getX1());
        } else {
            return value.doubleValue() >= Double.parseDouble(item.getX1()) && value.doubleValue() <= Double.parseDouble(item.getX2());
        }
    }

    /**
     * 是否符合
     * 使用线性插值公式计算y值：
     * y = y1 + (value - x1) * (y2 - y1) / (x2 - x1)
     *
     * @param value
     * @param multiple
     * @return
     */
    private static CalculatorResult interpolateByRule(BigDecimal value, InterpolationFeeRuleVO.Item item, BigDecimal multiple) {
        // 使用线性插值公式计算y值： y = y1 + (value - x1) * (y2 - y1) / (x2 - x1)
        Double val = Convert.toDouble(item.getY1(), 0.0) +
                (value.doubleValue() - Convert.toDouble(item.getX1(), 0.0)) * (Convert.toDouble(item.getY2(), 0.0) - Convert.toDouble(item.getY1(), 0.0))
                        / (Convert.toDouble(item.getX2(), 0.0) - Convert.toDouble(item.getX1(), 0.0));
        StringBuffer buffer = new StringBuffer().append(Convert.toBigDecimal(item.getY1(), BigDecimal.ZERO))
                .append("+").append("(").append(value.multiply(multiple)).append("-")
                .append(Convert.toBigDecimal(item.getX1(), BigDecimal.ZERO).multiply(multiple)).append(")*(")
                .append(Convert.toBigDecimal(item.getY2(), BigDecimal.ZERO)).append("-")
                .append(Convert.toBigDecimal(item.getY1(), BigDecimal.ZERO)).append(")/(")
                .append(Convert.toBigDecimal(item.getX2(), BigDecimal.ZERO).multiply(multiple)).append("-")
                .append(Convert.toBigDecimal(item.getX1(), BigDecimal.ZERO).multiply(multiple)).append(")");
        if ((Integer.parseInt(item.getStyle()) & AMOUNT) == AMOUNT) {
            return new CalculatorResult(AMOUNT, BigDecimal.valueOf(val), multiple, buffer.toString());
        }
        if ((Integer.parseInt(item.getStyle()) & RATE) == RATE) {
            return new CalculatorResult(RATE, BigDecimal.valueOf(val), multiple, buffer.toString());
        }
        return new CalculatorResult(AMOUNT, BigDecimal.valueOf(val), multiple, buffer.toString());
    }

    /**
     * 命中结果
     */
    @Data
    @AllArgsConstructor
    public static class CalculatorResult {
        //1 金额 2 费率
        private Integer style;
        private BigDecimal result;
        private BigDecimal multiple;
        //计算式
        private String formulaExpression;


        public CalculatorResult(Integer style, BigDecimal result, BigDecimal multiple) {
            this.style = style;
            this.result = result;
            this.multiple = multiple;
        }
    }


}
