package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/17 16:36
 */
@Data
@TableName(value ="dict_EffectFeeSet")
public class PrjEffectFeeSet {
    @TableId
    private Long fsId;
    private Long fsPid;
    private Long fsHostmodel;
    private String fsSequence;
    private String fsCode;
    private String fsName;
    private Double fsValue;
    private String fsDescription;
    private Long fsState;
    private Integer fsSortid;
    private Integer fsStyle;
    private String fsDetailcode;
    private String fsSpecialcode;
}
