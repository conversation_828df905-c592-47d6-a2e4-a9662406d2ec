package cn.savas.hub.module.collect.strategy;

import cn.savas.hub.module.collect.api.dto.ClientMergeProjectFileDTO;
import cn.savas.hub.module.collect.dal.dataobject.FeeEngineering;
import cn.savas.hub.module.collect.dal.dataobject.PrjEffectFeeCoefficientMulti;
import cn.savas.hub.module.collect.dal.dataobject.PrjEffectFeeSet;
import cn.savas.hub.module.collect.dal.mapper.SqliteCollectMapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:43
 */
public interface ProductTestingFeeStrategy {
    /**
     * 处理合并检验检测费
     * @param req
     * @param sqliteCollectMapper
     */
    void processMergeFee(ClientMergeProjectFileDTO req);
    /**
     * 处理计算检验检测费
     */
    void processCalculateFee(List<Long> wbsIds);


    boolean supports(String product); // 判断该策略是否适用于指定产品

    /**
     * 删除指定工程ID的检验检测费数据
     * @param engIds 工程ID列表
     */
    void deleteDataByEngId(List<Long> engIds);

    /**
     * 合并降效设置费用项
     * @param
     */
    void mergeEffectFeeSet(List<PrjEffectFeeSet> reqList);

    /**
     * 合并指标关联的降效费用项
     */
    void mergeEffectFeeCoefficientMulti(List<PrjEffectFeeCoefficientMulti> reqList);

}
