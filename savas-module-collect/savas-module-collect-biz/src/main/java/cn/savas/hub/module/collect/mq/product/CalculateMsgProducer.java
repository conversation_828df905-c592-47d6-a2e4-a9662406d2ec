package cn.savas.hub.module.collect.mq.product;

import cn.savas.hub.framework.mq.redis.core.RedisMQTemplate;
import cn.savas.hub.module.collect.mq.msg.CalculateRedisStreamMessage;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Log4j2
@Component
public class CalculateMsgProducer {

    @Resource
    private RedisMQTemplate redisMQTemplate; // 重点：注入 RedisMQTemplate 对象
    /**
     * 发送 计算消息
     */
    public void sendCalculateMessage(CalculateRedisStreamMessage msg) {
        redisMQTemplate.send(msg);
    }

}
