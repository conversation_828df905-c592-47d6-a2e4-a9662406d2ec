package cn.savas.hub.module.collect.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.collect.dal.dataobject.FeeProjectcost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【prj_projectcost】的数据库操作Mapper
* @createDate 2024-10-25 09:07:06
* @Entity cn.savas.hub.module.collect.dal.dataobject.PrjProjectcost
*/
@Mapper
public interface PrjProjectcostMapper extends BaseMapperX<FeeProjectcost> {

    List<FeeProjectcost> selectProjectCostByEngId(@Param("hostModelId") Long hostModelId);

    void updatePrjCostFeeById(@Param("feeId") Long feeId, @Param("fee") String fee);

    void updateEngFeeById(@Param("feeId") Long feeId, @Param("fee") String fee);

    void updateEngCostFeeById(@Param("feeId") Long feeId, @Param("fee") String fee);

    default List<FeeProjectcost> selectByHostModelId(Long engId){
        return selectList(FeeProjectcost::getFeeHostmodel, engId);
    }
}




