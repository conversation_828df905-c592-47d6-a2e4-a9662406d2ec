package cn.savas.hub.module.collect.util.aviator2;

import cn.savas.hub.module.collect.enums.OtherFeeKeyEnum;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * V9版本 构成公式解析
 */
@Log4j2
public class FormulaUtil {


    private static final List<String> whiteParam = Arrays.asList("IIF", "MAX", "MIN");

    /**
     * 格式化,三目运算符
     *
     * @param formula
     * @param map
     * @return
     */
    public static String formatMikiFormula(String formula, Map<String, Object> map) {
        //去掉公式中的特殊符号
        formula = formatAtFormula(formula);
        if (formula.contains("MAX(")) {
            formula = formula.replaceAll("MAX", "max");
        }
        if (formula.contains("MIN(")) {
            formula = formula.replaceAll("MIN", "min");
        }
        if (formula.contains("IIF")) {
            formula = convertNormalFormula(formula, "IIF", map);
            return formula;
        }
        if (formula.contains("IF")) {
            formula = convertNormalFormula(formula, "IF", map);
        }
        return formula;
    }

    /**
     * 格式化公式:由于无法处理
     * 不在处理@@的符号 外层单独处理了
     * 不能处理单_ 会有影响
     * # 费率表数据,处理其他费公式
     *
     * @param formula
     * @return
     * @这个关键字符
     */
    public static String formatAtFormula(String formula) {
        return formula.replaceAll("@", "_").replace("#", OtherFeeKeyEnum.rate_.name());
    }

    /**
     * 将表达式中的 IIF(...) 转换为 Java 三元表达式格式 A ? B : C
     * 支持嵌套 IIF
     */
    public static String convertNormalFormula(String expr, String code, Map<String, Object> map) {
        while (expr.contains(code)) {
            int iifIndex = expr.indexOf(code);
            int startParen = expr.indexOf("(", iifIndex);
            int endParen = findMatchingParen(expr, startParen);
            String inner = expr.substring(startParen + 1, endParen);
            String[] parts = splitIIFParts(inner);
            //递归处理子表达式中的 IIF
            String condition = parts[0].trim();
            //处理三木运算符中的boolean条件值暂定为false
            convertBooleanParams(condition, map);
            String trueExpr = parts[1].trim();
            String falseExpr = parts[2].trim();
            String ternary = condition + " ? " + trueExpr + " : " + falseExpr;
            // 替换整个 IIF 表达式
            expr = expr.substring(0, iifIndex) + ternary + expr.substring(endParen + 1);
        }
        return expr;
    }

    private static void convertBooleanParams(String condition, Map<String, Object> map) {
        if (map.containsKey(condition)) {
            map.put(condition, true);
        } else {
            map.put(condition, false);
        }
    }

    // 找到从 start 开始的匹配的 ')'
    private static int findMatchingParen(String expr, int start) {
        int depth = 0;
        for (int i = start; i < expr.length(); i++) {
            if (expr.charAt(i) == '(') {
                depth++;
            } else if (expr.charAt(i) == ')') {
                depth--;
            }
            if (depth == 0) {
                return i;
            }
        }
        return -1;
    }

    // 将 IIF 内部字符串按逗号分隔（支持括号内逗号）
    private static String[] splitIIFParts(String inner) {
        int depth = 0;
        StringBuilder part = new StringBuilder();
        java.util.List<String> parts = new java.util.ArrayList<>();

        for (int i = 0; i < inner.length(); i++) {
            char ch = inner.charAt(i);
            if (ch == ',' && depth == 0) {
                parts.add(part.toString());
                part.setLength(0);
            } else {
                if (ch == '(') {
                    depth++;
                } else if (ch == ')') {
                    depth--;
                }
                part.append(ch);
            }
        }
        parts.add(part.toString()); // 加入最后一个
        return parts.toArray(new String[0]);
    }


    /**
     * 提取公式中的参数 包括参数和code
     * 获取所有形参
     *
     * @param formula
     * @return
     */
    public static Set<String> getFormulaCode(String formula) {
        if (StringUtils.isBlank(formula)) {
            throw new RuntimeException("formula is empty!");
        }
        Set<String> codeSet = new HashSet<>();
        // 正则表达式匹配字母、数字和特殊字符（如 @）
        String regex = "[^+\\-*/=()?:]+";
        Matcher matcher = Pattern.compile(regex).matcher(formula);
        while (matcher.find()) {
            //忽略关键字:IFF MAX MIN
            if (!whiteParam.contains(matcher.group())) {
                if (matcher.group().trim().contains("@@")) {
                    codeSet.add(matcher.group().trim().replaceAll("@@", ""));
                    continue;
                }
                if (matcher.group().trim().contains("@")) {
                    codeSet.add(matcher.group().trim().split("@")[1]);
                    continue;
                }
                String regex2 = "^F.*\\d$";
                if (Pattern.matches(regex2, matcher.group().trim())) {
                    codeSet.add(matcher.group().trim());
                }
            }
        }

        return codeSet;
    }

    /**
     * 获取公式中的参数
     *
     * @param formula
     * @return
     */
    public static Set<String> getParamByFormula(String formula) {
        if (StringUtils.isBlank(formula)) {
            throw new RuntimeException("formula is empty!");
        }
        Set<String> paramSet = new HashSet<>();
        // 正则表达式匹配字母、数字和特殊字符（如 @）
        String regex = "[^+\\-*/=()?:]+";
        Matcher matcher = Pattern.compile(regex).matcher(formula);
        while (matcher.find()) {
            paramSet.add(matcher.group().trim());
        }
        return paramSet;
    }


    /**
     * 获取公式中未被计算的code
     * 初始化未被计算参数
     * 默认值为0
     */
    public static Set<String> getUndefinedRowCode(String formula, Map<String, BigDecimal> baseFeeMap) {
        //既包含Code又包含参数
        Set<String> codes = getFormulaCode(formula);
        return diffSetCode(codes, baseFeeMap);
    }

    /**
     * 差量Set
     */
    public static Set<String> diffSetCode(Set<String> needCodeSet, Map<String, BigDecimal> baseFeeMap) {
        Set<String> undefinedCode = new HashSet<>();
        for (String code : needCodeSet) {
            //需要重新计算rowCode
            if (!baseFeeMap.containsKey(code)) {
                undefinedCode.add(code);
            }
        }
        return undefinedCode;
    }

    /**
     * 正式公式  对比参数池中不存在的参数
     *
     * @param validFormula
     * @param paramMap
     */
    public static void addUndefinedParam(String validFormula, Map<String, Object> paramMap) {
        Set<String> params = getParamByFormula(validFormula);
        Set<String> undefinedParams = new HashSet<>();
        for (String param : params) {
            if (!paramMap.containsKey(param)) {
                paramMap.put(param, BigDecimal.ZERO);
                undefinedParams.add(param);
            }
        }
        if (!undefinedParams.isEmpty()) {
            //log.warn("公式中未定义的参数:{}", undefinedParams);
        }
    }
}
