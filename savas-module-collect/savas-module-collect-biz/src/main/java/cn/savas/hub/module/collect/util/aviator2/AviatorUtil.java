package cn.savas.hub.module.collect.util.aviator2;

import cn.hutool.core.convert.Convert;
import cn.savas.hub.module.collect.util.MapDecimalUtil;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;

@Slf4j
public class AviatorUtil {

    /**
     * 执行计算 忽略不存在的属性,并给默认值
     *
     * @param formula
     * @param singleId
     * @return
     */
    @SafeVarargs
    public static BigDecimal execute(String rowCode, Long singleId, String formula, Map<String, BigDecimal> map, BigDecimal... rates) {
        Map<String, Object> paramMap = MapDecimalUtil.mergeMapToObject(map);
        //格式化公式处理特殊自定义函数
        String validFormula = FormulaUtil.formatMikiFormula(formula, paramMap);
        //根据公式补全并初始默认参数值 BigDecimal.ZERO
        FormulaUtil.addUndefinedParam(validFormula, paramMap);
        //执行计算
        Object execute = null;
        try {
            execute = AviatorEvaluator.execute(validFormula, paramMap);
        } catch (Exception e) {
            log.error("公式:{}计算失败,异常信息:{}", validFormula, e.getMessage());
            log.error("单项/项目id:{},rowCode:{}", singleId, rowCode);
            execute = BigDecimal.ZERO;
        }
        BigDecimal result = Convert.toBigDecimal(execute, BigDecimal.ZERO);
        for (BigDecimal rate : rates) {
            result = result.multiply(rate);
        }
        return result;
    }


}
