package cn.savas.hub.module.collect.convert;

import cn.savas.hub.module.collect.dal.dataobject.FeeEngineering;
import cn.savas.hub.module.labeling.api.dto.SingleInfo;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

@Mapper
public interface LabelingDTOConvert {

    LabelingDTOConvert INSTANCE = Mappers.getMapper(LabelingDTOConvert.class);

    @Mappings({
            @Mapping(target= "projectId", source = "projectId"),
            @Mapping(target= "rowId", source = "bean.engId"),
            @Mapping(target= "rowName", source = "bean.engName"),
            @Mapping(target= "rowType", expression = "java(1)"),
    })
    SingleInfo convert(FeeEngineering bean,Long projectId);

   default List<SingleInfo> convertList(List<FeeEngineering> dto,Long projectId){
       List<SingleInfo> list=new ArrayList<>();
       for (FeeEngineering bean : dto) {
           SingleInfo convert = convert(bean, projectId);
           list.add(convert);
       }
       return list;
   }

}
