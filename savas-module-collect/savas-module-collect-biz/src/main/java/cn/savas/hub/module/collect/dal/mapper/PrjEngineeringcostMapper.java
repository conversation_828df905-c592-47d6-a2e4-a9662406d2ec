package cn.savas.hub.module.collect.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.collect.dal.dataobject.FeeEngineeringcost;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【prj_engineeringcost】的数据库操作Mapper
* @createDate 2024-10-25 09:07:06
* @Entity cn.savas.hub.module.collect.dal.dataobject.PrjEngineeringcost
*/
@Mapper
public interface PrjEngineeringcostMapper extends BaseMapperX<FeeEngineeringcost> {

    default List<FeeEngineeringcost> selectByHostModelId(Long engId){
        return selectList(FeeEngineeringcost::getFeeHostmodel, engId);
    }
}




