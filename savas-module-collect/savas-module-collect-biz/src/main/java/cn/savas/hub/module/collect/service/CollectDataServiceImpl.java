package cn.savas.hub.module.collect.service;

import cn.hutool.core.io.FileUtil;
import cn.savas.hub.framework.common.client.util.V9PackageUtil;
import cn.savas.hub.module.collect.api.dto.CalculateReqDTO;
import cn.savas.hub.module.collect.api.dto.ClientMergeProjectFileDTO;
import cn.savas.hub.module.collect.dal.dataobject.FeeEngineering;
import cn.savas.hub.module.collect.dal.dataobject.FeeEngineeringcost;
import cn.savas.hub.module.collect.dal.dataobject.FeeProjectcost;
import cn.savas.hub.module.collect.dal.mapper.PrjEngineeringMapper;
import cn.savas.hub.module.collect.dal.mapper.PrjEngineeringcostMapper;
import cn.savas.hub.module.collect.dal.mapper.PrjProjectcostMapper;
import cn.savas.hub.module.collect.framework.annotations.ChangeSqliteDB;
import cn.savas.hub.module.collect.mq.msg.CalculateRedisStreamMessage;
import cn.savas.hub.module.collect.mq.product.CalculateMsgProducer;
import cn.savas.hub.module.infra.api.file.FileApi;
import com.alibaba.fastjson.JSON;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
@Log4j2
public class CollectDataServiceImpl implements CollectDataService {

    @Resource
    private CalculateMsgProducer producer;
    @Resource
    private PrjProjectcostMapper prjProjectcostMapper;
    @Resource
    private PrjEngineeringcostMapper engineeringcostMapper;
    @Resource
    private PrjEngineeringMapper prjEngineeringMapper;
    @Resource
    private FileApi fileApi;
    @Resource
    private SqliteCollectService sqliteCollectService;

    @Override
    public void mergeProjectFile(ClientMergeProjectFileDTO req) throws Exception {
        // 解压公共库文件
        byte[] mainBytes = fileApi.getFileContent(req.getMainPath());
        String mainDB = FileUtil.createTempFile(".db", false).getAbsolutePath();
        V9PackageUtil.unZipFileToDB(mainBytes, mainDB);
        // 解压个人库文件
        byte[] slaveBytes = fileApi.getFileContent(req.getSlavePath());
        String slaveDB = FileUtil.createTempFile(".db", false).getAbsolutePath();
        V9PackageUtil.unZipFileToDB(slaveBytes, slaveDB);
        log.info("开始合并数据:入参为:{}", req);
        // 合并数据
        sqliteCollectService.mergeProjectData(req, mainDB, slaveDB);
    }

    @Override
    public void calculateProjectData(CalculateReqDTO req) throws Exception {
        // 解压公共库文件
        byte[] mainBytes = fileApi.getFileContent(req.getPath());
        String mainDB = FileUtil.createTempFile(".db", false).getAbsolutePath();
        V9PackageUtil.unZipFileToDB(mainBytes, mainDB);
        CalculateRedisStreamMessage streamMessage = new CalculateRedisStreamMessage();
        streamMessage.setProjectId(req.getProjectId());
        streamMessage.setMainFilePath(req.getPath());
        streamMessage.setMainDB(mainDB);
        streamMessage.setProjectFileId(req.getProjectFileId());
        streamMessage.setWbsIds(req.getHostmodelids());
        streamMessage.setProduct(req.getProduct());
        // 发送计算消息
        producer.sendCalculateMessage(streamMessage);
    }

    @ChangeSqliteDB(value = "#dbUri")
    @Override
    public void initFeeMap(String dbUri) {
        List<FeeProjectcost> feeProjectcosts = prjProjectcostMapper.selectList();
        for (FeeProjectcost feeProjectcost : feeProjectcosts) {
            Map<String, BigDecimal> feeMap = feeProjectcost.getFeeMap();
            prjProjectcostMapper.updatePrjCostFeeById(feeProjectcost.getFeeId(), JSON.toJSONString(feeMap));
        }
        List<FeeEngineering> feeEngineerings = prjEngineeringMapper.selectList();
        for (FeeEngineering eng : feeEngineerings) {
            Map<String, BigDecimal> feeMap = eng.getFeeMap();
            prjProjectcostMapper.updateEngFeeById(eng.getEngId(), JSON.toJSONString(feeMap));
        }
        List<FeeEngineeringcost> engCost = engineeringcostMapper.selectList();
        for (FeeEngineeringcost cost : engCost) {
            Map<String, BigDecimal> feeMap = cost.getFeeMap();
            prjProjectcostMapper.updateEngCostFeeById(cost.getFeeId(), JSON.toJSONString(feeMap));
        }
    }
}
