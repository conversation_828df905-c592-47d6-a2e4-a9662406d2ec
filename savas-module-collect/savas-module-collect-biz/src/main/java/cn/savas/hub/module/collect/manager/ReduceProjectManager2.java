package cn.savas.hub.module.collect.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.client.enmus.ClientProductEnum;
import cn.savas.hub.framework.common.client.util.V9BinaryToMapUtil;
import cn.savas.hub.framework.common.core.KeyValue;
import cn.savas.hub.module.collect.dal.dataobject.*;
import cn.savas.hub.module.collect.dal.mapper.*;
import cn.savas.hub.module.collect.enums.OtherFeeKeyEnum;
import cn.savas.hub.module.collect.service.PrjCostcalculatorService;
import cn.savas.hub.module.collect.strategy.ProductMergeStrategyResolver;
import cn.savas.hub.module.collect.util.MapDecimalUtil;
import cn.savas.hub.module.collect.util.PrjFeeUtil;
import cn.savas.hub.module.collect.util.aviator2.AviatorUtil;
import cn.savas.hub.module.collect.util.aviator2.ExpenseUtil;
import cn.savas.hub.module.collect.util.aviator2.FormulaUtil;
import cn.savas.hub.module.collect.util.aviator2.SpecialFormulaUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 初始化各个类型的计算的基础费用
 * 以及费用变动
 */
@Log4j2
@Component
public class ReduceProjectManager2 {

    @Resource
    private PrjEngineeringMapper prjEngineeringMapper;
    @Resource
    private PrjEngineeringcostMapper engineeringcostMapper;
    @Resource
    private PrjProjectcostMapper prjProjectcostMapper;
    @Resource
    private PrjCostcalculatorService costcalculatorService;
    @Resource
    private PrjProjectengMapper prjProjectengMapper;
    @Resource
    private PrjExpenseitemMapper prjExpenseitemMapper;
    @Resource
    private DictRateMapper dictRateMapper;
    @Resource
    private ProductMergeStrategyResolver strategyResolver;
    @Resource
    private OtherFeeManager otherFeeManager;
    @Resource
    private PrjTestingengineeringMapper prjTestingengineeringMapper;

    /**
     * 重新计算总和
     * 计算过程: 计算单项:工程其他费=>更新单项费=>计算单项其他费=>项目费
     *
     * @param wbsIds
     * @param oldEngList
     */
    @SneakyThrows
    public void reSummaryProjectCostToMain(List<Long> wbsIds, List<FeeEngineering> oldEngList, String product) {
        long start = System.currentTimeMillis();
        //同理计算项目费用
        List<FeeEngineering> engList = getEnginnerAndProjectList();
        Map<Long, BaseFeeEntity> map = engList.stream().collect(Collectors.toMap(FeeEngineering::getEngId, p -> p));
        //获取删除受影响的信息
        List<Long> effectiveWbsId = this.getDelWbsPid(wbsIds, map, oldEngList);
        //受影响的包括 删除+不包括检验检测费的合计
        List<Long> lowIds = otherFeeManager.getLowWbsIds(effectiveWbsId, map);
        List<BaseFeeEntity> singleList = otherFeeManager.getLowChangeWbsList(lowIds, map);
        //单价公式
        Map<Long, Map<String, List<PrjExpenseitem>>> expenseMap = this.getValidExpenseItemList(lowIds);
        //计算其他费
        Map<String, PrjCostcalculator> calculatorMap = getStringPrjCostcalculatorMap();
        //其他工程费
        Map<Long, List<FeeEngineeringcost>> otherPrjMap = getLongListMap(lowIds);
        //其他费
        Map<Long, List<FeeProjectcost>> otherMap = getListMap(lowIds);
        //# 采用的是费率信息
        Map<String, BigDecimal> rateMap = dictRateMapper.getRateFeeCode().stream().collect(Collectors.toMap(KeyValue::getKey, KeyValue::getValue));
        Map<Long, PrjTestingengineering> testingEngineeringMap = new HashMap<>();
        if (ClientProductEnum.PRODUCT_25.getCode().equals(product)) {
            // 检验检测费
            List<PrjTestingengineering> testingEngineeringList = prjTestingengineeringMapper.selectByWbsIds(lowIds);
            testingEngineeringMap = testingEngineeringList.stream()
                    .collect(Collectors.toMap(PrjTestingengineering::getEngPid, p -> p));
        }
        for (BaseFeeEntity single : singleList) {
            //合并费用
            List<Map<String, BigDecimal>> feeList = single.getChildren().stream().map(BaseFeeEntity::getFeeMap).collect(Collectors.toList());
            Map<String, BigDecimal> baseMap = MapDecimalUtil.mergeMap(feeList);
            single.setBytes(baseMap);
            Map<String, List<PrjExpenseitem>> expenseNameMap = expenseMap.get(single.getId());
            //检验检测费
            Map<String, BigDecimal> testEngParamMap = Optional.ofNullable(testingEngineeringMap.get(single.getId()))
                    .map(te -> V9BinaryToMapUtil.unpackData(te.getEngParameters()))
                    .orElse(new HashMap<>());
            this.updateSinglePrjRelationFee(single, calculatorMap, new HashMap<>(), otherPrjMap, otherMap, rateMap, expenseNameMap, testEngParamMap);
            if (Convert.toInt(ClientClassIdEnum.PROJECT.getCode()).equals(single.getEngClass()) || single.getEngClass() == 0 || single.getEngClass() == null) {
                prjProjectengMapper.updateById(BeanUtil.toBean(single, FeeProjecteng.class));
            } else {
                prjEngineeringMapper.updateById((FeeEngineering) single);
            }
        }
        log.info("重新计算用时:{}ms", System.currentTimeMillis() - start);
    }

    /**
     * 获取有效id
     * 1.忽略检验检测费
     * 2.获取删除有效的父id
     *
     * @param wbsIds
     * @param map
     * @param oldEngList
     * @return
     */
    private List<Long> getDelWbsPid(List<Long> wbsIds, Map<Long, BaseFeeEntity> map, List<FeeEngineering> oldEngList) {
        List<Long> list = new ArrayList<>();
        Map<Long, BaseFeeEntity> oldMap = oldEngList.stream().collect(Collectors.toMap(FeeEngineering::getEngId, p -> p));
        for (Long wbsId : wbsIds) {
            if (map.containsKey(wbsId)) {
                // 情况1：wbsId 在 map 中，直接添加
                list.add(wbsId);
            } else if (oldMap.containsKey(wbsId)) {
                // 情况2：wbsId 不在 map 中，但在 oldMap 中
                BaseFeeEntity wbs = oldMap.get(wbsId);
                if (map.containsKey(wbs.getPid())) {
                    // 如果 wbs 的 pid 在 map 中，添加 pid
                    list.add(wbs.getPid());
                }
            }
        }
        return list;
    }


    public List<FeeEngineering> getEnginnerAndProjectList() {
        FeeProjecteng project = prjProjectengMapper.selectOne(new LambdaQueryWrapper<>());
        FeeEngineering prjSingle = BeanUtil.toBean(project, FeeEngineering.class);
        prjSingle.setEngClass(ClientClassIdEnum.PROJECT.getCode().intValue());
        //汇总eng 并返回受影响的single//1.2返回所有受影响的父级重新计算
        List<FeeEngineering> engList = prjEngineeringMapper.selectList();
        engList.add(prjSingle);
        PrjFeeUtil.baseListToTree(engList);
        return engList;
    }


    private void updateSinglePrjRelationFee(BaseFeeEntity single,
                                            Map<String, PrjCostcalculator> calculatorMap,
                                            Map<String, List<BaseCostEntity>> childCodeFeeMap,
                                            Map<Long, List<FeeEngineeringcost>> otherPrjMap,
                                            Map<Long, List<FeeProjectcost>> otherMap,
                                            Map<String, BigDecimal> rateMap,
                                            Map<String, List<PrjExpenseitem>> expenseNameMap,
                                            Map<String, BigDecimal> testEngParamMap) {
        //数据缓存池
        Map<String, BigDecimal> otherBaseFee = new HashMap<>();
        //数据缓存池
        List<FeeEngineeringcost> otherPrjRows = otherPrjMap.get(single.getId()) == null ? new ArrayList<>() : otherPrjMap.get(single.getId());
        if (!otherPrjRows.isEmpty()) {
            Map<String, BaseCostEntity> otherPrjRowMap = otherPrjRows.stream().collect(Collectors.toMap(FeeEngineeringcost::getFeeCode, p -> p));
            PrjFeeUtil.baseCostListToTree(otherPrjRows);
            //计算工程其他费 加合获取单项费用
            Map<String, BigDecimal> prjOtherBaseFee = otherFeeManager.addRowFeeToCache(null, OtherFeeKeyEnum.F004.name(), single.getFeeMap(), true, rateMap);
            this.updateOtherFee(single.getId(), prjOtherBaseFee, otherPrjRows, otherPrjRowMap, calculatorMap, childCodeFeeMap, expenseNameMap);
            engineeringcostMapper.updateBatch(otherPrjRows);
            //补充工程费更新单项级工程费用
            otherBaseFee = MapDecimalUtil.mergeMap(otherPrjRows.stream().filter(p -> p.getPid().equals(single.getId())).map(BaseCostEntity::getFeeMap).collect(Collectors.toList()));
            //处理其他工程费节点
            Map<String, BigDecimal> singleFee = MapDecimalUtil.mergeMap(single.getFeeMap(), otherBaseFee);
            single.setBytes(singleFee);
        }
        //补充检验检测费
        Map<String, BigDecimal> mergeMap = MapDecimalUtil.mergeMap(single.getFeeMap(), testEngParamMap);
        single.setBytes(mergeMap);
        //计算其他费 //其他费//初始化根节点费用
        List<FeeProjectcost> otherRows = otherMap.get(single.getId()) == null ? new ArrayList<>() : otherMap.get(single.getId());
        //补充工程费节点
        if (!otherRows.isEmpty()) {
            Map<String, BaseCostEntity> otherRowMap = otherRows.stream().collect(Collectors.toMap(FeeProjectcost::getFeeCode, p -> p));
            Map<String,BigDecimal> cacheMap=new HashMap<>();
            //补充其他工程费节点
            initDefaultRowFee(null, otherRowMap, OtherFeeKeyEnum.F00402.name(), cacheMap, otherBaseFee);
            Map<String, BigDecimal> mainOtherFee = MapDecimalUtil.diffMapFee(otherBaseFee, mergeMap);
            //补充主要工程费节点
            initDefaultRowFee(null, otherRowMap, OtherFeeKeyEnum.F00401.name(), cacheMap, mainOtherFee);
            //设置工程费
            initDefaultRowFee(rateMap, otherRowMap, OtherFeeKeyEnum.F004.name(), cacheMap, mergeMap);
            PrjFeeUtil.baseCostListToTree(otherRows);
            //计算
            this.updateOtherFee(single.getId(), cacheMap, otherRows, otherRowMap, calculatorMap, childCodeFeeMap, expenseNameMap);
            prjProjectcostMapper.updateBatch(otherRows);
        }
    }

    /**
     * 处理单独处理额的费用节点 并缓存至费用缓存池
     * 1.其他工程费
     * 2.主要工程费
     * 3.工程费
     */
    private void initDefaultRowFee(Map<String, BigDecimal> rateMap, Map<String, BaseCostEntity> otherRowMap, String code, Map<String, BigDecimal> cacheMap, Map<String, BigDecimal> rowFeeMap) {
        BaseCostEntity f004Row = otherRowMap.get(code);
        if (f004Row != null) {
            f004Row.setBytes(rowFeeMap);
            f004Row.setFeeValue(rowFeeMap.get(OtherFeeKeyEnum.FEE.name()));
            f004Row.setForeignValue(rowFeeMap.get(OtherFeeKeyEnum.WBF.name()));
            //数据缓存池
            if (code.equals(OtherFeeKeyEnum.F004.name())) {
                otherFeeManager.addRowFeeToCache(cacheMap, code, rowFeeMap, true, rateMap);
            } else {
                otherFeeManager.addRowFeeToCache(cacheMap, code, rowFeeMap, false, rateMap);
            }
        }
    }

    private void updateOtherFee(Long singleId, Map<String, BigDecimal> baseMap, List<? extends BaseCostEntity> otherRows, Map<String, BaseCostEntity> otherRowMap, Map<String, PrjCostcalculator> calculatorMap, Map<String, List<BaseCostEntity>> childCodeFeeMap, Map<String, List<PrjExpenseitem>> expenseNameMap) {
        if (!CollectionUtils.isEmpty(otherRows)) {
            for (BaseCostEntity row : otherRows) {
                //避免重复计算
                if (!baseMap.containsKey(row.getCode())) {
                    this.reduceOtherFee(singleId, row, baseMap, otherRowMap, calculatorMap, childCodeFeeMap, expenseNameMap);
                    //更新费用缓存
                    otherFeeManager.addRowFeeToCache(baseMap, row.getCode(), row.getFeeMap(), false, null);
                    row.setFeeValue(row.getFeeMap().get(OtherFeeKeyEnum.FEE.name()));
                    row.setForeignValue(row.getFeeMap().get(OtherFeeKeyEnum.WBF.name()));
                }
                log.info("single:{},code:{},Fee:{}",singleId,row.getCode(),row.getFeeValue());
            }
        }
    }

    /**
     * 从其他费 中获取计算公式
     * 计算过程: 1.先计算本行数据:是特殊的先根据特殊情况计算费用
     * 2.通过单价公式计算 衍生费用
     *
     * @return
     */
    private void reduceOtherFee(Long singleId, BaseCostEntity row, Map<String, BigDecimal> baseMap, Map<String, BaseCostEntity> otherRowMap, Map<String, PrjCostcalculator> calculatorMap, Map<String, List<BaseCostEntity>> childCodeFeeMap, Map<String, List<PrjExpenseitem>> expenseNameMap) {
        //根据expense 查找计算公式
        if (StringUtils.isNotBlank(row.getFeeExpression())) {
            //正常计算 通过公式正常计算当前值并更绝计算公式计算衍生值
            Set<String> unCodes = FormulaUtil.getUndefinedRowCode(row.getFeeExpression(), baseMap);
            //递归计算其他行
            if (!unCodes.isEmpty()) {
                List<BaseCostEntity> unList = this.getFeeListByCode(unCodes, otherRowMap);
                this.updateOtherFee(singleId, baseMap, unList, otherRowMap, calculatorMap, childCodeFeeMap, expenseNameMap);
            }
            //全量加和
            if (row.getFeeExpression().contains("@@")) {
                Set<String> codes = FormulaUtil.getFormulaCode(row.getFeeExpression());
                Map<String, BigDecimal> feeMap = otherFeeManager.computeDoubleAtFormula(codes, otherRowMap);
                row.setBytes(feeMap);
            } else {
                //特殊计算 需要先计算线性值作为2次计算公式计算的依据 //通过插值法获取 当前行的Fee
                if (StringUtils.isNotBlank(row.getFeeCostTag()) && calculatorMap.containsKey(row.getFeeCostTag())) {
                    //根节点对应关系
                    PrjCostcalculator calculator = calculatorMap.get(row.getFeeCostTag());
                    //需要从basic 计算初始值//检查公式 并计算未被处理的
                    Set<String> basicUnCodes = FormulaUtil.getUndefinedRowCode(calculator.getCalcConfig().getBasicExpression(), baseMap);
                    if (!basicUnCodes.isEmpty()) {
                        List<BaseCostEntity> basicUnList = this.getFeeListByCode(basicUnCodes, otherRowMap);
                        this.updateOtherFee(singleId, baseMap, basicUnList, otherRowMap, calculatorMap, childCodeFeeMap, expenseNameMap);
                    }
                    //费差法基数:从对象公式计算
                    BigDecimal basicValue = AviatorUtil.execute(row.getCode(), singleId, calculator.getCalcConfig().getBasicExpression(), baseMap);
                    SpecialFormulaUtil.CalculatorResult result = SpecialFormulaUtil.calculateInterpolation(calculator.getCalcConfig(), basicValue);
                    //计算基数 //单价公式计算数据,计算当前子目内需要的行数据
                    BigDecimal expenseValue = this.getValueByCalculatorResult(result, baseMap, row);
                    Map<String, BigDecimal> rowExpenseMap = this.reduceExpenseFeeMap(row, expenseNameMap, expenseValue);
                    row.setBytes(rowExpenseMap);
                } else {
                    BigDecimal otherVal = AviatorUtil.execute(row.getCode(), singleId, row.getFeeExpression(), baseMap, row.getFeeRate());
                    //单价公式计算数据,计算当前子目内需要的行数据
                    Map<String, BigDecimal> rowExpenseMap = this.reduceExpenseFeeMap(row, expenseNameMap, otherVal);
                    row.setBytes(rowExpenseMap);
                }
            }
        } else {
            //由子类汇总
            if (row.getCostChildren() != null && !row.getCostChildren().isEmpty()) {
                List<BaseCostEntity> children = row.getCostChildren();
                this.updateOtherFee(singleId, baseMap, children, otherRowMap, calculatorMap, childCodeFeeMap, expenseNameMap);
                List<Map<String, BigDecimal>> childFeeList = children.stream().map(BaseCostEntity::getFeeMap).collect(Collectors.toList());
                Map<String, BigDecimal> childFee = MapDecimalUtil.mergeMap(childFeeList);
                row.setBytes(childFee);
            } else {
                //从子类汇总
                List<BaseCostEntity> children = childCodeFeeMap.get(row.getCode());
                if (children != null) {
                    Map<String, BigDecimal> map = PrjFeeUtil.summeryChildFee(children);
                    row.setBytes(map);
                }
            }
        }
    }

    private Map<String, BigDecimal> reduceExpenseFeeMap(BaseCostEntity row, Map<String, List<PrjExpenseitem>> expenseNameMap, BigDecimal otherVal) {
        if (StringUtils.isNotBlank(row.getFeeExpense())) {
            Map<String, BigDecimal> expenseMap = ExpenseUtil.initExpenFeeMap(otherVal, row.getFeeTaxRate(), row.getFeeForeignValue());
            Map<String, BigDecimal> cacheMap = new HashMap<>();
            List<PrjExpenseitem> items = expenseNameMap.get(row.getFeeExpense());
            if (items != null) {
                for (PrjExpenseitem rule : items) {
                    BigDecimal value = AviatorUtil.execute(row.getCode(), null, rule.getExpExpression(), expenseMap);
                    expenseMap.put(rule.getExpSequence(), value);
                    //只存储有效数据
                    if (StringUtils.isNotBlank(rule.getExpStatistics())) {
                        if (BigDecimal.ZERO.compareTo(value) != 0) {
                            cacheMap.put(rule.getExpStatistics(), value);
                        }
                    }
                }
                return cacheMap;
            }
        }
        return new HashMap<>();
    }


    private List<BaseCostEntity> getFeeListByCode(Set<String> unCodes, Map<String, BaseCostEntity> otherRowMap) {
        List<BaseCostEntity> list = new ArrayList<>();
        for (String unCode : unCodes) {
            BaseCostEntity costEntity = otherRowMap.get(unCode);
            if (costEntity != null) {
                list.add(costEntity);
            } else {
                log.error("未查询到的code:{}", unCode);
            }
        }
        return list;
    }

    private BigDecimal getValueByCalculatorResult(SpecialFormulaUtil.CalculatorResult result, Map<String, BigDecimal> baseMap, BaseCostEntity row) {
        if (result.getStyle() == 1) {
            row.setFeeExpression(result.getFormulaExpression());
            BigDecimal calculatorResult = AviatorUtil.execute(null, null, result.getFormulaExpression(), baseMap);
            //公式计算金额*费率
            return calculatorResult.multiply(result.getMultiple()).multiply(row.getFeeRate());
        } else {//比例
            //执行计算公式计算费率 * 计算计算式金额
            BigDecimal rate = result.getResult().divide(BigDecimal.valueOf(100));
            row.setFeeRate(rate);
            return AviatorUtil.execute(null, null, row.getFeeExpression(), baseMap, rate);
        }
    }

    private Map<Long, Map<String, List<PrjExpenseitem>>> getValidExpenseItemList(List<Long> lowIds) {
        List<PrjExpenseitem> prjExpenseitemList = prjExpenseitemMapper.selectExpenseListByWbsId(lowIds);
        return prjExpenseitemList.stream().collect(Collectors.groupingBy(PrjExpenseitem::getExpHostModel, Collectors.groupingBy(PrjExpenseitem::getExpenseName)));
    }

    private Map<Long, List<FeeProjectcost>> getListMap(List<Long> lowIds) {
        List<FeeProjectcost> otherRows = prjProjectcostMapper.selectList(new LambdaQueryWrapper<FeeProjectcost>().in(FeeProjectcost::getFeeHostmodel, lowIds));
        Map<Long, List<FeeProjectcost>> otherMap = otherRows.stream().collect(Collectors.groupingBy(FeeProjectcost::getFeeHostmodel));
        return otherMap;
    }

    private Map<Long, List<FeeEngineeringcost>> getLongListMap(List<Long> lowIds) {
        List<FeeEngineeringcost> otherPrjRows = engineeringcostMapper.selectList(new LambdaQueryWrapper<FeeEngineeringcost>().in(FeeEngineeringcost::getFeeHostmodel, lowIds));
        Map<Long, List<FeeEngineeringcost>> otherPrjMap = otherPrjRows.stream().collect(Collectors.groupingBy(FeeEngineeringcost::getFeeHostmodel));
        return otherPrjMap;
    }

    private Map<String, PrjCostcalculator> getStringPrjCostcalculatorMap() {
        List<PrjCostcalculator> list = costcalculatorService.getCulculatorList();
        Map<String, PrjCostcalculator> calculatorMap = list.stream().collect(Collectors.toMap(PrjCostcalculator::getCalcName, p -> p));
        return calculatorMap;
    }
}
