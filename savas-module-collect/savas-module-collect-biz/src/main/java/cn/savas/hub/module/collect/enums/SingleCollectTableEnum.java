package cn.savas.hub.module.collect.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 概算结构影响的表汇总
 * 表名小写
 */
@Getter
@AllArgsConstructor
public enum SingleCollectTableEnum {

    prj_engineering("prj_engineering","eng_id","单位工程"),
    prj_expense("prj_expense","Exp_HostModel","单价公式(主)"),
    prj_expenseitem("prj_expenseitem","Exp_HostModel","单价公式(从)"),
    prj_Lecture("prj_Lecture","lect_hostmodel","编制说明"),
    prj_engineeringcost("prj_engineeringcost","fee_hostmodel","工程其他费"),
    prj_projectcost("prj_projectcost","fee_hostmodel","其他费"),
    prj_parameter("prj_parameter","para_HostModel","工程属性"),

    ;
    /**
     * 表名
     */
    private final String table;
    /**
     * wbsId 所在列
     */
    private final String col;
    /**
     * 表描述
     */
    private final String description;


}
