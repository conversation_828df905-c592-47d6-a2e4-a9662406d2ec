package cn.savas.hub.module.collect.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OtherFeeKeyEnum {


    /**
     * 特殊计算后的key
     */
    Value("特殊计算后的key"),

    /**
     * 其他费 费率
     */
    TaxRate("其他费 费率"),

    /**
     * 其他费 外币费
     */
    ForeignValue("其他费 外币费"),

    /**
     * 外币费:流内字段
     */
    WBF("外币费:流内字段"),

    /**
     * 外币费:流内字段
     */
    FEETAX("外币费:流内字段"),
    /**
     * 父节点Fee
     */
    FEE("工程费"),

    /**
     * 其他费计算根节点
     */
    F004("F004"),

    /**
     * 主要工程费
     */
    F00401("F00401"),

    /**
     * 其他工程费
     */
    F00402("F00402"),

    /**
     * # 替换为字符
     */
    rate_("替换为字符"),
    ;


    private String desc;

}
