package cn.savas.hub.module.collect.dal.dataobject;

import cn.savas.hub.module.collect.dal.handler.CalcConfigHandler;
import cn.savas.hub.module.collect.dal.handler.CalcConfigHandlerV2;
import cn.savas.hub.module.collect.dal.handler.dto.InterpolationFeeRuleVO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName prj_CostCalculator
 */
@TableName(value ="dict_CostCalculator",autoResultMap = true)
@Data
public class DictCostCalculator implements Serializable {

    @TableField(value = "calc_name")
    private String calcName;

    /**
     * 配置器规则
     */
    @TableField(value = "calc_configerxml",typeHandler = CalcConfigHandler.class )
    private InterpolationFeeRuleVO calcConfig;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}