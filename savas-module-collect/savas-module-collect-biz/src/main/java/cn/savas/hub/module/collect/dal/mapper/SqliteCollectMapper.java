package cn.savas.hub.module.collect.dal.mapper;

import cn.savas.hub.module.collect.dal.dataobject.PrjExpenseitem;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

@Mapper
public interface SqliteCollectMapper {

    //忽略检查:报错
    @InterceptorIgnore(tenantLine = "true")
    @Update(value = "ATTACH DATABASE #{slavePath} AS slaveDB")
    void attachDatabase(@Param("slavePath") String slavePath);

    //忽略检查:报错
    @InterceptorIgnore(tenantLine = "true")
    @Update(value = "DETACH DATABASE slaveDB")
    void detachDatabase();

    void deleteOldDataByTemplate(@Param("table") String table, @Param("col") String col, @Param("wbsId") Long wbsId);

    void mergeDataFromSlaveDB(@Param("table") String table, @Param("col") String col, @Param("wbsId") Long wbsId);

    List<PrjExpenseitem> getCalculationFormulaByHostModel(Long hostId);

    /**
     * 增加工程版本
     * @param wbsId WBS ID
     */
    void increaseEngVersion(@Param("wbsId") Long wbsId);

    /**
     * 增加项目版本
     */
    void increaseProjectVersion(@Param("projectId") Long projectId);

    /**
     * 根据engId获取上级工程Id
     */
    Set<Long> getEngParentById(@Param("engIds") List<Long> engIds, @Param("engClass") Long engClass);

    Set<Long> selectTestingFeeEng(@Param("engIds") List<Long> engIds);

    void deleteTestingFeeByEngIds(@Param("delEngIds") Set<Long> delEngIds);
    void deleteTestingFeeByEngIds2(@Param("delEngIds") Set<Long> delEngIds);
    void mergeTestingFeeFromSlaveDB(@Param("engIds") Set<Long> engIds);
    void mergeTestingFeeFromSlaveDB2(@Param("engIds") Set<Long> engIds);

    Set<Long> selectSlaveTestingFeeEng(@Param("engIds") List<Long> engIds);

    void deleteOldDataByTemplates(@Param("table")String table,
                                  @Param("col")String hostmodel,
                                  @Param("engIds")List<Long> wbsIds);

    void mergeDataFromSlaveDBs(@Param("table")String table,
                               @Param("col")String hostmodel,
                               @Param("engIds") List<Long> wbsIds);

    /**
     * 删除工程属性数据
     * @param wbsIds
     */
    void deletePrjParameter(@Param("engIds")List<Long> wbsIds);

    /**
     * 合并工程属性数据
     * @param wbsIds
     */
    void mergePrjParameter(@Param("engIds")List<Long> wbsIds);
}
