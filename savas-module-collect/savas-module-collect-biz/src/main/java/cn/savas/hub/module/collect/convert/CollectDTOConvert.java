package cn.savas.hub.module.collect.convert;

import cn.savas.hub.module.collect.api.dto.CollectSqliteDto;
import cn.savas.hub.module.collect.mq.msg.CalculateRedisStreamMessage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CollectDTOConvert {

    CollectDTOConvert INSTANCE = Mappers.getMapper(CollectDTOConvert.class);

    CalculateRedisStreamMessage convert(CollectSqliteDto dto);

}
