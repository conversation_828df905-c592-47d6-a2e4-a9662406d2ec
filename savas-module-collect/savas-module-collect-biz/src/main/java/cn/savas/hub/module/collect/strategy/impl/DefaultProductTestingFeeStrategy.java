package cn.savas.hub.module.collect.strategy.impl;

import cn.savas.hub.framework.common.client.enmus.BillProfessionEnum;
import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.client.enmus.ClientProductEnum;
import cn.savas.hub.framework.common.client.enmus.EnumUnZipFieldNameV9;
import cn.savas.hub.framework.common.client.util.V9BinaryToMapUtil;
import cn.savas.hub.framework.common.client.util.V9MapToBinaryUtil;
import cn.savas.hub.module.collect.api.dto.ClientMergeProjectFileDTO;
import cn.savas.hub.module.collect.dal.dataobject.*;
import cn.savas.hub.module.collect.dal.mapper.*;
import cn.savas.hub.module.collect.strategy.ProductTestingFeeStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 25定额库策略实现类
 * <AUTHOR>
 * @date 2025/6/20 11:45
 */
@Component
public class DefaultProductTestingFeeStrategy implements ProductTestingFeeStrategy {
    @Resource
    private PrjTestingsectionbillMapper prjTestingsectionbillMapper;
    @Resource
    private PrjTestingengineeringMapper prjTestingengineeringMapper;
    @Resource
    private PrjTestingspecialtyMapper prjTestingspecialtyMapper;
    @Resource
    private SqliteCollectMapper sqliteCollectMapper;
    @Resource
    private PrjEngineeringMapper prjEngineeringMapper;
    @Resource
    private PrjProjectengMapper projectEngMapper;
    @Resource
    private PrjEffectFeeSetMapper prjEffectFeeSetMapper;
    @Resource
    private PrjEffectFeeCoefficientMultiMapper prjEffectFeeCoefficientMultiMapper;

    @Override
    public void processMergeFee(ClientMergeProjectFileDTO req) {
        List<Long> wbsIds = req.getWbsIds();
        // 1. 检验检测费处理
        this.processTestingFee(wbsIds);
        // 2. 降效处理
        this.processEffectFee(wbsIds);

    }


    /**
     * 降效处理
     */
    private void processEffectFee(List<Long> wbsIds) {
        // 1. 删除旧数据
        sqliteCollectMapper.deleteOldDataByTemplates("prj_EffectFeeCoefficientMulti", "fcm_HostModel", wbsIds);
        sqliteCollectMapper.deleteOldDataByTemplates("prj_EffectFeeSet", "fs_HostModel", wbsIds);
        sqliteCollectMapper.deleteOldDataByTemplates("prj_EffectFeeSetDetail", "fsd_HostModel", wbsIds);
        // 2. 合并从库数据
        sqliteCollectMapper.mergeDataFromSlaveDBs("prj_EffectFeeCoefficientMulti", "fcm_HostModel", wbsIds);
        sqliteCollectMapper.mergeDataFromSlaveDBs("prj_EffectFeeSet", "fs_HostModel", wbsIds);
        sqliteCollectMapper.mergeDataFromSlaveDBs("prj_EffectFeeSetDetail", "fsd_HostModel", wbsIds);
    }


    /**
     * 检验检测费处理
     * @param wbsIds
     */
    private void processTestingFee(List<Long> wbsIds) {
        // 1.prj_testingspecialty、prj_testingengineering
        Set<Long> testEngIds = sqliteCollectMapper.selectTestingFeeEng(wbsIds);
        if (!CollectionUtils.isEmpty(testEngIds)) {
            sqliteCollectMapper.deleteTestingFeeByEngIds(testEngIds);
            sqliteCollectMapper.deleteTestingFeeByEngIds2(testEngIds);
        }
        Set<Long> slaveTestEngIds = sqliteCollectMapper.selectSlaveTestingFeeEng(wbsIds);
        if (!CollectionUtils.isEmpty(slaveTestEngIds)) {
            sqliteCollectMapper.mergeTestingFeeFromSlaveDB(slaveTestEngIds);
            sqliteCollectMapper.mergeTestingFeeFromSlaveDB2(slaveTestEngIds);
        }
        // 2.prj_testingsectionbill
        sqliteCollectMapper.deleteOldDataByTemplates("prj_testingsectionbill", "bill_hostmodel", wbsIds);
        sqliteCollectMapper.mergeDataFromSlaveDBs("prj_testingsectionbill", "bill_hostmodel", wbsIds);
        // 3.prj_Measure
        sqliteCollectMapper.deleteOldDataByTemplates("prj_Measure", "meas_hostmodel", wbsIds);
        sqliteCollectMapper.mergeDataFromSlaveDBs("prj_Measure", "meas_hostmodel", wbsIds);
    }

    /**
     * processCalculateFee
     * @param wbsIds
     */
    @Override
    public void processCalculateFee(List<Long> wbsIds) {
        List<FeeEngineering> engList = prjEngineeringMapper.selectWbsTree();
        // 1. 获取受影响单项ID及单位工程ID映射
        Set<Long> parentEngIds = engList.stream()
                .filter(e -> wbsIds.contains(e.getEngId()))
                .map(FeeEngineering::getPid)
                .collect(Collectors.toSet());
        Map<Long, List<Long>> dxIdMap = engList.stream()
                .filter(e->e.getPid() != null)
                .collect(Collectors.groupingBy(
                        FeeEngineering::getPid,
                        Collectors.mapping(FeeEngineering::getEngId, Collectors.toList())));

        // 2. 筛选单项工程ID
        Set<Long> dxEngIds = engList.stream()
                .filter(e -> parentEngIds.contains(e.getEngId()))
                .filter(e -> ClientClassIdEnum.SECTIONENG.getCode().intValue() == e.getEngClass())
                .map(FeeEngineering::getEngId)
                .collect(Collectors.toSet());

        // 3. 汇总testingsectionbill数据
        Map<Long, Map<String, BigDecimal>> sectionBillArchMap = new HashMap<>();
        Map<Long, Map<String, BigDecimal>> sectionBillInstMap = new HashMap<>();
        for (Long dxEngId : dxEngIds) {
            List<Long> dwIds = dxIdMap.getOrDefault(dxEngId, Collections.emptyList());
            if (dwIds.isEmpty()) continue;

            Map<String, BigDecimal> archParams = new HashMap<>();
            Map<String, BigDecimal> instParams = new HashMap<>();
            prjTestingsectionbillMapper.selectList(
                            new LambdaQueryWrapper<PrjTestingsectionbill>().in(PrjTestingsectionbill::getBillHostmodel, dwIds))
                    .forEach(bill -> {
                        Map<String, BigDecimal> params = V9BinaryToMapUtil.unpackData(bill.getBillParameters());
                        Map<String, BigDecimal> target = BillProfessionEnum.BUILD.getCode().equals(bill.getBillCode())
                                ? archParams : instParams;
                        params.forEach((key, value) -> target.merge(key, value, BigDecimal::add));
                    });

            sectionBillArchMap.put(dxEngId, archParams);
            sectionBillInstMap.put(dxEngId, instParams);
        }

        // 4. 更新testingengineering数据
        List<PrjTestingengineering> engListToUpdate = prjTestingengineeringMapper.selectList(
                new LambdaQueryWrapper<PrjTestingengineering>().in(PrjTestingengineering::getEngPid, dxEngIds));
        if (engListToUpdate.isEmpty()) return;
        //暂存Parameters
        Map<Long, Map<String, BigDecimal>> engineeringMapFinal = new HashMap<>();
        engListToUpdate.forEach(eng -> {
            Map<String, BigDecimal> archParams = sectionBillArchMap.getOrDefault(eng.getEngPid(), new HashMap<>());
            Map<String, BigDecimal> instParams = sectionBillInstMap.getOrDefault(eng.getEngPid(), new HashMap<>());
            Map<String, BigDecimal> mergedParams = new HashMap<>(archParams);
            // 合并建筑和安装参数
            instParams.forEach((key, value) -> mergedParams.merge(key, value, BigDecimal::add));
            // 处理工程检验检测费参数
            mergedParams.entrySet().removeIf(entry ->
                    entry.getValue() == null || entry.getValue().compareTo(BigDecimal.ZERO) <= 0
            );
            engineeringMapFinal.put(eng.getEngId(), mergedParams);
        });

        // 5. 更新testingspecialty数据
        Map<Long, Long> engIdToPid = engListToUpdate.stream()
                .collect(Collectors.toMap(PrjTestingengineering::getEngId, PrjTestingengineering::getEngPid));
        List<PrjTestingspecialty> specialtyList = prjTestingspecialtyMapper.selectList(
                new LambdaQueryWrapper<PrjTestingspecialty>().in(PrjTestingspecialty::getSpecPid, engIdToPid.keySet()));
        List<PrjTestingspecialty> specialtyUpdates = specialtyList.stream().peek(spec -> {
            Long engPid = engIdToPid.get(spec.getSpecPid());
            Map<String, BigDecimal> params = BillProfessionEnum.BUILD.getCode().equals(spec.getSpecCode())
                    ? sectionBillArchMap.get(engPid)
                    : sectionBillInstMap.get(engPid);
            if (params != null) {
                // 4.1 补充工程检验检测费参数
                params.forEach((key, value) -> {
                    Map<String, BigDecimal> decimalMap = engineeringMapFinal.get(spec.getSpecPid());
                    decimalMap.put(StringUtils.join(spec.getSpecStatistics(), "_", key), value);
                });
                // 5.1 更新建筑/安装检验检测专业参数
                spec.setSpecParameters(V9MapToBinaryUtil.convertToBinary(params));
            }
        }).collect(Collectors.toList());
        prjTestingspecialtyMapper.updateBatch(specialtyUpdates);

        // 4.2 更新工程检验检测费参数
        List<PrjTestingengineering> engineeringUpdates = engListToUpdate.stream().peek(eng -> {
            Map<String, BigDecimal> params = engineeringMapFinal.get(eng.getEngId());
            if (params != null && !params.isEmpty()) {
                eng.setEngParameters(V9MapToBinaryUtil.convertToBinary(params));
            } else {
                eng.setEngParameters(null); // 清空参数
            }
        }).collect(Collectors.toList());
        prjTestingengineeringMapper.updateBatch(engineeringUpdates);
    }

    @Override
    public boolean supports(String product) {
        return ClientProductEnum.PRODUCT_25.getCode().equals(product);
    }

    @Override
    public void deleteDataByEngId(List<Long> engIds) {
        if (CollectionUtils.isEmpty(engIds)) {
            return;
        }
        prjTestingsectionbillMapper.delete(new LambdaQueryWrapper<PrjTestingsectionbill>()
                .notIn(PrjTestingsectionbill::getBillHostmodel, engIds));
        prjTestingengineeringMapper.delete(new LambdaQueryWrapper<PrjTestingengineering>()
                .notIn(PrjTestingengineering::getEngId, engIds)
        );
        prjTestingspecialtyMapper.delete(new LambdaQueryWrapper<PrjTestingspecialty>()
                .notIn(PrjTestingspecialty::getSpecHostmodel, engIds)

        );

        projectEngMapper.delEffectFeeCoefficientMulti(engIds);
        projectEngMapper.delEffectFeeSet(engIds);
        projectEngMapper.delEffectFeeSetDetail(engIds);
    }

    @Override
    public void mergeEffectFeeSet(List<PrjEffectFeeSet> reqList) {
        prjEffectFeeSetMapper.insertOrUpdate(reqList);
    }

    @Override
    public void mergeEffectFeeCoefficientMulti(List<PrjEffectFeeCoefficientMulti> reqList) {
        prjEffectFeeCoefficientMultiMapper.insertOrUpdate(reqList);
    }
}
