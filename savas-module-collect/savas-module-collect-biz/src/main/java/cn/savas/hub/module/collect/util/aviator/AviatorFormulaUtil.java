package cn.savas.hub.module.collect.util.aviator;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Table;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Log4j2
public class AviatorFormulaUtil {

    private static final List<String> whiteParam = Arrays.asList("IF", "MAX", "MIN");

    /**
     * 提取公式中的参数
     * 获取所有形参
     *
     * @param formula
     * @return
     */
    public static Set<String> formatFormulaParameters(String formula) {
        if (StringUtils.isBlank(formula)) {
            throw new RuntimeException("formula is empty!");
        }
        Set<String> parameters = new HashSet<>();
        // 正则表达式匹配字母、数字和特殊字符（如 @）
        String regex = "[^+\\-*/=()]+";
        Matcher matcher = Pattern.compile(regex).matcher(formula);
        while (matcher.find()) {
            if (!whiteParam.contains(matcher.group())) {
                parameters.add(matcher.group());
            }
        }
        return parameters;
    }

    /**
     * 预处理公式并返回
     * 无法处理的持有引用的引用行号
     */
    public static Set<String> pretreatmentFormulaReturnIllegalRowCode(String formula, Table<String, String, BigDecimal> table) {
        Set<String> keys = formatFormulaParameters(formula);
        Set<String> unExistKey = new HashSet<>();
        for (String parameter : keys) {
            if (parameter.contains("@@")) {
                String code = parameter.split("@@")[1];
                if (!table.containsRow(code)) {
                    unExistKey.add(code);
                }
                continue;
            }
            if (parameter.contains("@")) {
                String code = parameter.split("@")[1];
                if (!table.containsRow(code)) {
                    unExistKey.add(code);
                }
            }
        }
        if (!unExistKey.isEmpty()) {
            log.info("公式:{},需要进一步计算的行号:{}", formatFormula(formula), JSON.toJSONString(unExistKey));
        }
        return unExistKey;
    }

    /**
     * 格式化公式:由于无法处理
     * 由于@符号 无法被公式解析器解析,需要做二次转换
     *
     * @param formula
     * @return
     * @这个关键字符
     */
    public static String formatFormula(String formula) {
        return formula.replaceAll("@", "__").replaceAll("#", "param.");
    }

    /**
     * 获取公式内所有code
     */
    public static Set<String> getCodeByFormula(String formula) {
        Set<String> codes = new HashSet<>();
        Set<String> parameters = formatFormulaParameters(formula);
        for (String str : parameters) {
            if (str.contains("@@")) {
                codes.add(str.split("@@")[1]);
                continue;
            }
            if (str.contains("@")) {
                codes.add(str.split("@")[1]);
            }
        }
        return codes;
    }

    /**
     * 处理公式中的Max,IF 函数
     *
     * @param formula
     * @return
     * @这个关键字符
     */
    public static String modifyFormula(String formula) {
        //处理MAX
        String formulaMax = formula.replaceAll("MAX", "max");
        //处理IF 转为标准的三木运算
        if (formulaMax.contains("IF")) {
            return AviatorIFFormulaUtil.convertToTernary(formulaMax);
        }
        return formulaMax;
    }


}
