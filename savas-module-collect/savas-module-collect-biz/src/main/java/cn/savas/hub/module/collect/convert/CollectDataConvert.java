package cn.savas.hub.module.collect.convert;

import cn.savas.hub.module.collect.api.dto.*;
import cn.savas.hub.module.collect.dal.dataobject.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/19 16:08
 */
@Mapper
public interface CollectDataConvert {
    CollectDataConvert INSTANCE = Mappers.getMapper(CollectDataConvert.class);


    @Mappings({
            @Mapping(target= "id", source = "engId"),
            @Mapping(target= "pid", source = "engPid"),
            @Mapping(target= "name", source = "engName"),
            @Mapping(target= "wbsType", source = "engClass"),
            @Mapping(target= "userName", source = "engUsername"),
            @Mapping(target= "code", source = "engSpecialcode"),
            })
    WbsTreeDTO convert1(FeeEngineering bean);
    List<WbsTreeDTO> convertList1(List<FeeEngineering> bean);

    BudgetProjectInfoDTO convert2(PrjParameter bean);
    List<BudgetProjectInfoDTO> convertList2(List<PrjParameter> bean);

    PrjProjectCostDTO convert3(FeeProjectcost bean);
    List<PrjProjectCostDTO> convertList3(List<FeeProjectcost> bean);

    PrjEngineeringDTO convert4(FeeEngineering bean);
    List<PrjEngineeringDTO> convertList4(List<FeeEngineering> bean);

    PrjSectionBillDTO convert5(PrjSectionBill bean);
    List<PrjSectionBillDTO> convertList5(List<PrjSectionBill> bean);

    PrjNormDTO convert6(PrjNorm prjNorm);
    List<PrjNormDTO> convertList6(List<PrjNorm> prjNorms);

    PrjProjectEngDTO convert7(FeeProjecteng bean);
    List<PrjProjectEngDTO> convertList7(List<FeeProjecteng> bean);

    OtherEngFeeDTO convert8(FeeEngineeringcost engineeringCost);
    List<OtherEngFeeDTO> convertList8(List<FeeEngineeringcost> engineeringCostList);

    OtherFeeDTO convert9(FeeProjectcost bean);
    List<OtherFeeDTO> convertList9(List<FeeProjectcost> bean);

    PrjTestEngineeringDTO convert10(PrjTestingengineering bean);
    List<PrjTestEngineeringDTO> convertList10(List<PrjTestingengineering> bean);

    @Mappings({
            @Mapping(target = "paraId", source = "id"),
            @Mapping(target = "paraPid", source = "parentid"),
            @Mapping(target = "paraHostmodel", source = "hostmodelid"),
            @Mapping(target = "paraCode", source = "code"),
            @Mapping(target = "paraName", source = "name"),
            @Mapping(target = "paraSortid", source = "sortid"),
            @Mapping(target = "paraEditor", source = "editor"),
            @Mapping(target = "paraStyle", source = "style"),
            @Mapping(target = "paraValuetype", source = "datatype"),
            @Mapping(target = "paraInt", source = "intvalue"),
            @Mapping(target = "paraFloat", source = "floatvalue"),
            @Mapping(target = "paraString", source = "strvalue"),
            @Mapping(target = "paraDescription", source = "description"),
            @Mapping(target = "paraContext", source = "context"),
            @Mapping(target = "paraRegular", source = "validateregular"),
            @Mapping(target = "paraRegulartext", source = "validatetext"),
            @Mapping(target= "paraCodeitem", source="codeitem"),
            @Mapping(target= "paraState", source="modelstate")
    })
    PrjParameter convert11(ClientPrjSettingParamDTO bean);
    List<PrjParameter> convertList11(List<ClientPrjSettingParamDTO> bean);

    @Mappings({
            @Mapping(target = "rateId", source = "id"),
            @Mapping(target = "ratePid", source = "parentid"),
            @Mapping(target = "rateHostmodel", source = "hostmodelid"),
            @Mapping(target = "rateCode", source = "code"),
            @Mapping(target = "rateName", source = "name"),
            @Mapping(target = "rateOption", source = "option"),
            @Mapping(target = "rateValue", source = "value"),
            @Mapping(target = "rateDescription", source = "description"),
            @Mapping(target = "rateEditor", source = "editor"),
            @Mapping(target = "rateState", source = "modelstate"),
            @Mapping(target = "rateSortid", source = "sortid"),
            @Mapping(target = "rateOriginalvalue", source = "originalvalue")
    })
    PrjRateStandard convert12(ClientPrjSettingRateDTO bean);
    List<PrjRateStandard> convertList12(List<ClientPrjSettingRateDTO> bean);


    @Mappings({
            @Mapping(target = "dekid", source = "dekid"),
            @Mapping(target = "zmbh", source = "zmbh"),
            @Mapping(target = "data", source = "data"),
    })
    PrjMainMaterialCoefficient convert13(ClientPrjSettingMaterialDTO bean);
    List<PrjMainMaterialCoefficient> convertList13(List<ClientPrjSettingMaterialDTO> bean);

    @Mappings({
            @Mapping(target = "fsId", source = "id"),
            @Mapping(target = "fsPid", source = "parentid"),
            @Mapping(target = "fsHostmodel", source = "hostmodelid"),
            @Mapping(target = "fsSequence", source = "sequence"),
            @Mapping(target = "fsCode", source = "code"),
            @Mapping(target = "fsName", source = "name"),
            @Mapping(target = "fsValue", source = "value"),
            @Mapping(target = "fsDescription", source = "description"),
            @Mapping(target = "fsState", source = "modelstate"),
            @Mapping(target = "fsSortid", source = "sortid"),
            @Mapping(target = "fsStyle", source = "style"),
            @Mapping(target = "fsDetailcode", source = "detailcode"),
            @Mapping(target = "fsSpecialcode", source = "specialcode")
    })
    PrjEffectFeeSet convert14(ClientPrjSettingEffectSetDTO bean);
    List<PrjEffectFeeSet> convertList14(List<ClientPrjSettingEffectSetDTO> bean);


    @Mappings({
            @Mapping(target = "fcmId", source = "id"),
            @Mapping(target = "fcmPid", source = "parentid"),
            @Mapping(target = "fcmHostmodel", source = "hostmodelid"),
            @Mapping(target = "fcmSequence", source = "sequence"),
            @Mapping(target = "fcmCode", source = "code"),
            @Mapping(target = "fcmName", source = "name"),
            @Mapping(target = "fcmValue", source = "value"),
            @Mapping(target = "fcmDescription", source = "description"),
            @Mapping(target = "fcmSpecialcode", source = "specialcode"),
            @Mapping(target = "fcmBook", source = "book"),
            @Mapping(target = "fcmChapter", source = "chapter"),
            @Mapping(target = "fcmBurl", source = "burl"),
            @Mapping(target = "fcmExcludebook", source = "excludebook"),
            @Mapping(target = "fcmExcludechapter", source = "excludechapter"),
            @Mapping(target = "fcmExcludeburl", source = "excludeburl"),
            @Mapping(target = "fcmState", source = "modelstate"),
            @Mapping(target= "fcmSortid", source="sortid"),
            @Mapping(target= "fcmStyle", source="style")
    })
    PrjEffectFeeCoefficientMulti convert15(ClientPrjSettingEffectMultiDTO bean);
    List<PrjEffectFeeCoefficientMulti> convertList15(List<ClientPrjSettingEffectMultiDTO> bean);
}
