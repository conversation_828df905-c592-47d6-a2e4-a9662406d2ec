package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/21 14:34
 */
@Data
@TableName(value ="dict_RateStandardOptionCodeContent")
public class DictRateStandardOptionCodeContent {
    @TableId
    private Long conId;
    private Long conPid;
    private Long conHostmodel;
    private String conCode;
    private String conName;
    private Integer conSortid;
    private Long conState;
}





