package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.ByteArrayTypeHandler;

/**
 * <AUTHOR>
 * @date 2024/11/21 14:34
 */
@Data
@TableName(value ="dict_RateStandardOptionCode")
public class DictRateStandardOptionCode {
    @TableId
    private Long codeId;
    private Long codePid;
    private Long codeHostmodel;
    private String codeCode;
    private String codeName;
    private Integer codeSortid;
    private Long codeState;
}





