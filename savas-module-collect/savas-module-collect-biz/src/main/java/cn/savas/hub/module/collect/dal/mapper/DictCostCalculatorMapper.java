package cn.savas.hub.module.collect.dal.mapper;


import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.collect.dal.dataobject.DictCostCalculator;
import cn.savas.hub.module.collect.dal.dataobject.PrjCostcalculator;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【dict_CostCalculator】的数据库操作Mapper
* @createDate 2024-11-08 13:51:12
* @Entity generator.domain.PrjCostcalculator
*/
@Mapper
public interface DictCostCalculatorMapper extends BaseMapperX<DictCostCalculator> {


}
