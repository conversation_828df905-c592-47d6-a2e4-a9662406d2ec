package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/17 16:03
 */
@Data
@TableName(value ="prj_RateStandard")
public class PrjRateStandard {
    @TableId
    private Long rateId;
    private Long ratePid;
    private Long rateHostmodel;
    private String rateCode;
    private String rateName;
    private String rateOption;
    private Double rateValue;
    private String rateDescription;
    private String rateEditor;
    private Long rateState;
    private Integer rateSortid;
    private BigDecimal rateOriginalvalue;
}
