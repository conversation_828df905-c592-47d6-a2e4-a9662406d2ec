package cn.savas.hub.module.collect.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ReduceKeyEnum {


    /**
     * 特殊计算后的key
     */
    Value("特殊计算后的key"),

    /**
     * 其他费 费率
     */
    TaxRate("其他费 费率"),

    /**
     * 其他费 外币费
     */
    ForeignValue("其他费 外币费"),

    /**
     * 父节点Fee
     */
    FEE("工程费")

    ;


    private String desc;

}
