package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @TableName prj_CostCalculator
 */
@Data
public abstract class BaseFeeEntity implements Serializable {

    /**
     * 子节点费用
     */
    @TableField(exist = false)
    public Map<String, BigDecimal> childFeeMap;

    @TableField(exist = false)
    private List<BaseFeeEntity> children;

    /**
     * 获取主键的方法
     *
     * @return 主键ID
     */
    public abstract Long getId();

    /**
     * 获取父节点ID的方法
     *
     * @return 父节点ID
     */
    public abstract Long getPid();

    /**
     * 获取费用
     * @return
     */
    public abstract Map<String, BigDecimal> getFeeMap();

    public abstract void setBytes(Map<String, BigDecimal> feeMap);

    public abstract String getName();

    public abstract Integer getEngClass();

    public abstract String getCode();

}