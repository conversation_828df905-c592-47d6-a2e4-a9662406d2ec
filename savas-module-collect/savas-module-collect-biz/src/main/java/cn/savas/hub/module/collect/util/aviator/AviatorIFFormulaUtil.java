package cn.savas.hub.module.collect.util.aviator;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class AviatorIFFormulaUtil {

    /**
     * 将原始的 IF 表达式转换为三元运算符格式
     * @param expression 原始的 IF 表达式字符串
     * @return 转换后的三元运算符表达式
     */
    public static String convertToTernary(String expression) {
        return parseExpression(expression);
    }

    /**
     * 解析表达式，递归处理 IF 表达式
     * @param expression 原始表达式
     * @return 转换后的表达式
     */
    private static String parseExpression(String expression) {
        // 找到 IF 节点的位置
        int ifIndex = expression.indexOf("IF");
        if (ifIndex == -1) {
            return expression;  // 如果没有 IF，则返回原始表达式
        }

        // 查找完整的 IF 表达式，包括其括号
        int ifEndIndex = findClosingParenthesis(expression, ifIndex + 2);  // IF后面是 2 个字符
        if (ifEndIndex == -1) {
            throw new IllegalArgumentException("Unmatched parentheses in the IF expression.");
        }

        // 提取 IF 表达式
        String ifExpression = expression.substring(ifIndex + 3, ifEndIndex);
        log.debug("Found IF expression: {}", ifExpression);

        // 提取条件、真值和假值部分
        String condition = extractCondition(ifExpression);
        String trueExpr = extractTrueExpression(ifExpression);
        String falseExpr = extractFalseExpression(ifExpression);

        // 将 IF 表达式转换为三元表达式
        String ternaryExpr = "("+"(" + condition + ") ? " + trueExpr + " : " + falseExpr+")";
        log.debug("Converted to ternary: {}", ternaryExpr);

        // 替换原始表达式中的 IF 部分
        String result = expression.substring(0, ifIndex) + ternaryExpr + expression.substring(ifEndIndex + 1);

        // 递归处理余下的表达式部分
        return parseExpression(result);
    }

    /**
     * 查找匹配的右括号位置
     * @param expression 表达式
     * @param start 起始位置
     * @return 右括号位置
     */
    private static int findClosingParenthesis(String expression, int start) {
        int count = 0;
        for (int i = start; i < expression.length(); i++) {
            char c = expression.charAt(i);
            if (c == '(') {
                count++;
            } else if (c == ')') {
                count--;
                if (count == 0) {
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * 提取 IF 表达式的条件部分
     * @param expression IF 表达式
     * @return 条件部分
     */
    private static String extractCondition(String expression) {
        int commaIndex = findComma(expression);
        if (commaIndex == -1) {
            throw new IllegalArgumentException("Invalid IF expression: No comma found for condition.");
        }
        return expression.substring(0, commaIndex).trim();
    }

    /**
     * 提取 IF 表达式的真值部分
     * @param expression IF 表达式
     * @return 真值部分
     */
    private static String extractTrueExpression(String expression) {
        int firstCommaIndex = findComma(expression);
        int secondCommaIndex = findComma(expression, firstCommaIndex + 1);
        return expression.substring(firstCommaIndex + 1, secondCommaIndex).trim();
    }

    /**
     * 提取 IF 表达式的假值部分
     * @param expression IF 表达式
     * @return 假值部分
     */
    private static String extractFalseExpression(String expression) {
        int secondCommaIndex = findComma(expression, findComma(expression) + 1);
        return expression.substring(secondCommaIndex + 1).trim();
    }

    /**
     * 找到表达式中的逗号位置
     * @param expression 表达式
     * @return 逗号位置
     */
    private static int findComma(String expression) {
        return findComma(expression, 0);
    }

    /**
     * 找到表达式中的逗号位置
     * @param expression 表达式
     * @param start 起始位置
     * @return 逗号位置
     */
    private static int findComma(String expression, int start) {
        int parenthesesCount = 0;
        for (int i = start; i < expression.length(); i++) {
            char c = expression.charAt(i);
            if (c == '(') {
                parenthesesCount++;
            } else if (c == ')') {
                parenthesesCount--;
            } else if (c == ',' && parenthesesCount == 0) {
                return i;
            }
        }
        return -1;
    }
}
