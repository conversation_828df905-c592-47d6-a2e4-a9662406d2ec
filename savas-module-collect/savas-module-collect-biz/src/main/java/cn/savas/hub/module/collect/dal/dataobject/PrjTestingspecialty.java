package cn.savas.hub.module.collect.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.ByteArrayTypeHandler;

/**
 * <AUTHOR>
 * @date 2025/6/18 14:45
 */
@Data
@TableName(value ="prj_testingspecialty",autoResultMap = true)
public class PrjTestingspecialty {
    @TableId
    private Long specId;
    private Long specPid;
    private Long specHostmodel;
    private String specCode;
    private String specName;
    private String specStatistics;
    private Integer specSortid;
    private Long specState;
    @TableField(typeHandler = ByteArrayTypeHandler.class)
    private byte[] specParameters;
}
