package cn.savas.hub.module.collect.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.collect.dal.dataobject.FeeProjecteng;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【prj_projecteng】的数据库操作Mapper
* @createDate 2024-10-25 09:07:06
* @Entity cn.savas.hub.module.collect.dal.dataobject.PrjProjecteng
*/
@Mapper
public interface PrjProjectengMapper extends BaseMapperX<FeeProjecteng> {

    @Delete("<script>" +
            "DELETE FROM prj_attachmentfile WHERE file_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delAttachmentFile(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_bill WHERE bill_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delBill(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_comments WHERE comment_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delComments(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_consumption WHERE cons_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delConsumption(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_ConsumptionConversion WHERE Conv_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delConsumptionConversion(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_CostCalculator WHERE calc_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delCostCalculator(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_CostRegulation WHERE ccr_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delCostRegulation(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_designcondition WHERE design_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delDesigncondition(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_engineering WHERE eng_id NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delEngineering(@Param("engIds") List<Long> engIds);

//    @Delete("<script>" +
//            "DELETE FROM prj_EngineeringAmountVariate WHERE var_HostModel NOT IN " +
//            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
//            "#{id}" +
//            "</foreach>" +
//            "</script>")
//    void delEngineeringAmountVariate(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_engineeringcost WHERE fee_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delEngineeringcost(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_expense WHERE Exp_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delExpense(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_expenseitem WHERE Exp_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delExpenseitem(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_historydata WHERE hd_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delHistorydata(@Param("engIds") List<Long> engIds);

//    @Delete("<script>" +
//            "DELETE FROM prj_independentCost WHERE meas_hostmodel NOT IN " +
//            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
//            "#{id}" +
//            "</foreach>" +
//            "</script>")
//    void delIndependentCost(@Param("engIds") List<Long> engIds);
//
//    @Delete("<script>" +
//            "DELETE FROM prj_independentCostCategory WHERE Category_hostmodel NOT IN " +
//            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
//            "#{id}" +
//            "</foreach>" +
//            "</script>")
//    void delIndependentCostCategory(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_Lecture WHERE lect_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delLecture(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_Measure WHERE meas_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delMeasure(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_norm WHERE norm_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delNorm(@Param("engIds") List<Long> engIds);

//    @Delete("<script>" +
//            "DELETE FROM prj_NormAmountDetail WHERE detail_HostModel NOT IN " +
//            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
//            "#{id}" +
//            "</foreach>" +
//            "</script>")
//    void delNormAmountDetail(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_NormConversion WHERE Conv_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delNormConversion(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_otherbill WHERE meas_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delOtherbill(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_otherbillCategory WHERE Category_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delOtherbillCategory(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_parameter WHERE para_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delParameter(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_projectcost WHERE fee_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delProjectcost(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_RateStandard WHERE rate_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delRateStandard(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_sectionbill WHERE bill_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delSectionbill(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_specialty WHERE spec_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delSpecialty(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_ReportCategory WHERE cat_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delReportCategory(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_ReportTemplate WHERE tmpl_hostmodel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delReportTemplate(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_projecteng WHERE eng_id NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delProjectEng(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM dict_RateStandardOptionCode WHERE code_HostModel is null or code_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delDictRateStandardOptionCode(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM dict_RateStandardOptionCodeContent WHERE con_HostModel is null or con_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delDictRateStandardOptionCodeContent(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_EffectFeeCoefficientMulti WHERE fcm_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delEffectFeeCoefficientMulti(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_EffectFeeSet WHERE fs_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delEffectFeeSet(@Param("engIds") List<Long> engIds);

    @Delete("<script>" +
            "DELETE FROM prj_EffectFeeSetDetail WHERE fsd_HostModel NOT IN " +
            "<foreach collection='engIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void delEffectFeeSetDetail(@Param("engIds") List<Long> engIds);

    default void updateDirectorId(Long engId, Long engDirectorid, String engDirectorname){
        update(null, new LambdaUpdateWrapper<FeeProjecteng>()
                .set(FeeProjecteng::getEngDirectorid, engDirectorid)
                .set(FeeProjecteng::getEngProjectid, engDirectorname)
        );
    }
}




