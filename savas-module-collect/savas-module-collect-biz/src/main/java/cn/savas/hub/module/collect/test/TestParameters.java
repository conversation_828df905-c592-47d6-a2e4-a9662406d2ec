package cn.savas.hub.module.collect.test;

import cn.hutool.core.util.HexUtil;
import cn.savas.hub.framework.common.client.util.V9BinaryToMapUtil;
import com.alibaba.fastjson.JSON;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/12 14:15
 */
public class TestParameters {
    public static void main(String[] args) {
        String parameters = "78DAD3636060E000622F8628867886600677201D05A44162572B5EAA1972586CE1746005F2A2183C188280F26E60392868617300F11CC1BA9D81A4174304AA8A0666073620E5CB100936DF0959B661FE55710776B87E88CD10F9D30B5DB77DFE7B6516B3030F9017C81002B4D91928170F57EB0155590972E49933E784C0AE04B9019B2B59C03E40985F9FB5A764F20EDBD90260D7C1DCEF83AAEF001392EF70F99F0D1E7A6E0CAE400892956409E3D3DD347706B303275C3FEE1040B8005BE8B1C2655D8024B21C03DCC7B8421DE2A210A0FE08B0ECBCF7CB8F9DE6FB344D04AC1335B6E71C51D85094317105B3033390178EEACE59CD2DA2607144083CAC1259E7FE30AA8F0DC9FD8810B8ABC2D638D599FB3A7AFAC016C6CC70978253220F9336BB8153AF0846D84581530042AFF4767170BC8252871B5005480618AB122CCB94360A23A53A6CFA58515215486E8194FE5D954B5B2E093930017998618D8867507843F4804200180C4B217EC0E637645DB0140B76E335DF5F3CE0B48F900D84CA4363A29B099A6AD16504FFB3A0E844A46A50A20346F1EDFFF6B09481CD4DEC28691A667629D051C0547B8705293E09E7776C2AD0732CBAFF777902A3B89D11E87F36149740646FFFACCBDA63730B989F1136A1A654B04379FD5BF9C1A18ED099F0F482D2ED9F72AF90F324C27DA871852BC738A2C530142C80980949690897B48BDD3CF7BD397A1DCC255EA87963159B0300D0E3D780";
        Map<String, BigDecimal> unpackData = V9BinaryToMapUtil.unpackData(HexUtil.decodeHex(parameters));
        System.out.println(JSON.toJSONString(unpackData));
    }
}
