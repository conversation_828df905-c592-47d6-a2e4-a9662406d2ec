package cn.savas.hub.module.collect.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.collect.dal.dataobject.PrjExpense;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【prj_expense】的数据库操作Mapper
* @createDate 2024-11-05 11:03:33
* @Entity generator.domain.PrjExpense
*/
@Mapper
public interface PrjExpenseMapper extends BaseMapperX<PrjExpense> {

}




