package cn.savas.hub.module.collect.dal.dataobject;

import cn.savas.hub.framework.common.client.util.V9BinaryToMapUtil;
import cn.savas.hub.framework.common.client.util.V9MapToBinaryUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.ByteArrayTypeHandler;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @TableName prj_engineeringcost
 */
@TableName(value = "prj_engineeringcost", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class FeeEngineeringcost extends BaseCostEntity {
    /**
     * 费用ID
     */
    @TableId(value = "fee_id")
    private Long feeId;

    /**
     * 父费用ID
     */
    @TableField(value = "fee_pid")
    private Long feePid;

    /**
     * 主模型ID
     */
    @TableField(value = "fee_hostmodel")
    private Long feeHostmodel;

    /**
     * 序列
     */
    @TableField(value = "fee_sequence")
    private String feeSequence;

    /**
     * 费用名称
     */
    @TableField(value = "fee_name")
    private String feeName;

    /**
     * 费用代码
     */
    @TableField(value = "fee_code")
    private String feeCode;

    /**
     * 费用标签
     */
    @TableField(value = "fee_costtag")
    private String feeCosttag;

    /**
     * 费用值
     */
    @TableField(value = "fee_value")
    private BigDecimal feeValue;

    /**
     * 表达式
     */
    @TableField(value = "fee_expression")
    private String feeExpression;

    /**
     * 表达式代码
     */
    @TableField(value = "fee_expressioncode")
    private String feeExpressioncode;

    /**
     * 中文表达式
     */
    @TableField(value = "fee_cnexpression")
    private String feeCnexpression;

    /**
     * 费用说明
     */
    @TableField(value = "fee_expense")
    private String feeExpense;

    /**
     * 样式
     */
    @TableField(value = "fee_style")
    private Integer feeStyle;

    /**
     * 排序
     */
    @TableField(value = "fee_order")
    private Integer feeOrder;

    /**
     * 描述
     */
    @TableField(value = "fee_description")
    private String feeDescription;

    /**
     * 排序ID
     */
    @TableField(value = "fee_sortid")
    private Integer feeSortid;

    /**
     * 税率
     */
    @TableField(value = "fee_taxrate")
    private BigDecimal feeTaxrate;

    /**
     * 税率代码
     */
    @TableField(value = "fee_taxratecode")
    private String feeTaxratecode;

    /**
     * 统计信息
     */
    @TableField(value = "fee_statistics")
    private String feeStatistics;

    /**
     * 费率
     */
    @TableField(value = "fee_rate")
    private BigDecimal feeRate;

    /**
     * 费率代码
     */
    @TableField(value = "fee_ratecode")
    private String feeRatecode;

    /**
     * 原始ID
     */
    @TableField(value = "fee_orginalid")
    private Long feeOrginalid;

    /**
     * 上下文
     */
    @TableField(value = "fee_context")
    private Integer feeContext;

    /**
     * 状态
     */
    @TableField(value = "fee_state")
    private Long feeState;

    /**
     * 参数 (Blob类型)
     */
    @TableField(value = "fee_parameters", typeHandler = ByteArrayTypeHandler.class)
    private byte[] feeParameters;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public Long getId() {
        return this.feeId;
    }

    @Override
    public Long getPid() {
        return this.feePid;
    }

    @Override
    public Map<String, BigDecimal> getFeeMap() {
        return V9BinaryToMapUtil.unpackData(this.feeParameters);
    }

    @Override
    public void setBytes(Map<String, BigDecimal> feeMap) {
        this.feeParameters = V9MapToBinaryUtil.convertToBinary(feeMap);
    }

    @Override
    public String getName() {
        return this.feeName;
    }

    @Override
    public Integer getEngClass() {
        return 0;
    }

    @Override
    public String getCode() {
        return feeCode;
    }

    @Override
    public String getFeeCostTag() {
        return feeCosttag;
    }

    @Override
    public BigDecimal getFeeTaxRate() {
        return feeTaxrate;
    }

    @Override
    public BigDecimal getFeeForeignValue() {
        return BigDecimal.ZERO;
    }

    @Override
    public Integer getFeeType() {
        return 1;
    }

    @Override
    public void setFeeValue(BigDecimal feeValue) {
        this.feeValue = feeValue;
    }

    @Override
    public void setForeignValue(BigDecimal rate) {

    }

    @Override
    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    @Override
    public void setFeeExpression(String feeExpression) {
        this.feeExpression = feeExpression;
    }


}
