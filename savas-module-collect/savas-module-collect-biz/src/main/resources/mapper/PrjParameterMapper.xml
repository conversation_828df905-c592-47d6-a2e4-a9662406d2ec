<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.savas.hub.module.collect.dal.mapper.PrjParameterMapper">

    <select id="selectProjectInfo" resultType="cn.savas.hub.module.collect.dal.dataobject.PrjParameter">
        SELECT
            *
        FROM
            PRJ_Parameter
        WHERE
            para_HostModel = #{hostModelId} or para_PID = '0'
        ORDER BY para_SortID
    </select>
</mapper>
