<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.savas.hub.module.collect.dal.mapper.PrjExpenseitemMapper">


    <select id="selectOtherExpList" resultType="cn.savas.hub.module.collect.dal.dataobject.PrjExpenseitem">
        select prj_expenseitem.*
        from prj_expenseitem
                 inner join prj_expense on prj_expense.Exp_ID = prj_expenseitem.Exp_PID
        where prj_expense.Exp_Name = #{feeExpense}
        and prj_expenseitem.Exp_HostModel=#{engId}
        order by prj_expenseitem.Exp_Sequence
    </select>
    <select id="selectExpenseListByWbsId" resultType="cn.savas.hub.module.collect.dal.dataobject.PrjExpenseitem">
        select prj_expenseitem.*, prj_expense.Exp_Name as expenseName
        from prj_expenseitem
                 inner join prj_expense on prj_expense.Exp_ID = prj_expenseitem.Exp_PID
        where 1=1
        and prj_expenseitem.Exp_HostModel in
          <foreach collection="lowIds" open="(" close=")" item="id" separator="," >
              #{id}
          </foreach>
        order by prj_expenseitem.Exp_Sequence
    </select>
</mapper>
