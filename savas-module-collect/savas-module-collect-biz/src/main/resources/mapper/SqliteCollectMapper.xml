<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.savas.hub.module.collect.dal.mapper.SqliteCollectMapper">

    <insert id="mergeDataFromSlaveDB">
        INSERT INTO ${table}
        SELECT *
        FROM slaveDB.${table}
        WHERE ${col} = #{wbsId};
    </insert>

    <delete id="deleteOldDataByTemplate">
        DELETE FROM ${table} WHERE ${col} = #{wbsId};
    </delete>
    <select id="getCalculationFormulaByHostModel" resultType="cn.savas.hub.module.collect.dal.dataobject.PrjExpenseitem">
        select prj_expense.Exp_Name as expenseName,
               prj_expenseitem.*
        from prj_expenseitem
        left join prj_expense on prj_expense.Exp_ID=prj_expenseitem.Exp_PID
        where prj_expenseitem.Exp_HostModel = #{hostModel}
        order by prj_expenseitem.Exp_Sequence
    </select>

    <update id="increaseEngVersion">
        UPDATE prj_engineering SET eng_compileversion = eng_compileversion + 1, eng_syncversion = eng_compileversion + 1
        WHERE eng_id = #{wbsId};
    </update>
    <update id="increaseProjectVersion">
        UPDATE prj_projecteng SET eng_CompileVersion = eng_CompileVersion +1, eng_syncversion = eng_CompileVersion +1
        WHERE eng_projectid = #{projectId}
    </update>

    <select id="getEngParentById" resultType="java.lang.Long">
        SELECT
        t2.eng_id
        FROM
        slaveDB.prj_engineering t1
        INNER JOIN slaveDB.prj_engineering t2 ON t1.eng_pid = t2.eng_id
        WHERE
        t1.eng_id IN
        <foreach item="id" index="index" collection="engIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND t2.eng_class = #{engClass};

    </select>


    <delete id="deleteTestingFeeByEngIds">
        DELETE FROM prj_testingspecialty
        WHERE spec_pid IN
        <foreach item="id" index="index" collection="delEngIds" open="(" separator="," close=")">
            #{id}
        </foreach>;
    </delete>

    <delete id="deleteTestingFeeByEngIds2">
        DELETE FROM prj_testingengineering
        WHERE eng_id IN
        <foreach item="id" index="index" collection="delEngIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="mergeTestingFeeFromSlaveDB">
        INSERT INTO prj_testingengineering
        SELECT *
        FROM slaveDB.prj_testingengineering
        WHERE eng_id IN
        <foreach item="id" index="index" collection="engIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>
    <insert id="mergeTestingFeeFromSlaveDB2">
        INSERT INTO prj_testingspecialty
        SELECT *
        FROM slaveDB.prj_testingspecialty
        WHERE spec_pid IN
        <foreach item="id" index="index" collection="engIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>
    <select id="selectSlaveTestingFeeEng" resultType="java.lang.Long">
        SELECT eng_id
        FROM slaveDB.prj_testingengineering
        WHERE eng_pid IN
        <foreach item="id" index="index" collection="engIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectTestingFeeEng" resultType="java.lang.Long">
        SELECT eng_id
        FROM prj_testingengineering
        WHERE eng_pid IN
        <foreach item="id" index="index" collection="engIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <delete id="deleteOldDataByTemplates">
        DELETE FROM ${table} WHERE ${col} IN
        <foreach item="engId" index="index" collection="engIds" open="(" separator="," close=")">
            #{engId}
        </foreach>;
    </delete>

    <insert id="mergeDataFromSlaveDBs">
        INSERT INTO ${table}
        SELECT *
        FROM slaveDB.${table}
        WHERE ${col} IN
        <foreach item="engId" index="index" collection="engIds" open="(" separator="," close=")">
            #{engId}
        </foreach>;
    </insert>

    <delete id="deletePrjParameter">
        DELETE FROM prj_parameter WHERE para_HostModel IN
        <foreach item="id" index="index" collection="engIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND (para_State != 67108864 OR para_Code = 'SVERSION_EFFECTFEESET')
        AND para_Style != 128
        AND para_Code != 'MainMaterialCoefficient';
    </delete>

    <insert id="mergePrjParameter">
        INSERT INTO prj_parameter
        SELECT *
        FROM slaveDB.prj_parameter
        WHERE para_HostModel IN
        <foreach item="id" index="index" collection="engIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND (para_State != 67108864 OR para_Code = 'SVERSION_EFFECTFEESET')
        AND para_Style != 128
        AND para_Code != 'MainMaterialCoefficient';
    </insert>
</mapper>
