<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.savas.hub.module.collect.dal.mapper.PrjEngineeringMapper">

    <select id="selectWbsTreeBase" resultType="cn.savas.hub.module.collect.dal.dataobject.FeeEngineering">
        SELECT
            eng_ID         AS engId,
            '0'            AS engPid,
            eng_Name       AS engName,
            #{classId}       as engClass,
            0              as engSortid,
            eng_Code       as engCode,
            eng_username   as engUsername,
            '' as engSpecialcode
        FROM PRJ_ProjectENG
        UNION ALL
        SELECT
            eng_ID         AS engId,
            eng_PID        AS engPid,
            eng_Name       AS engName,
            eng_Class      as engClass,
            eng_SortID     as engSortid,
            eng_Code       as engCode,
            eng_username   as engUsername,
            eng_specialcode as engSpecialcode
        FROM PRJ_Engineering
        Order by engSortid
    </select>
</mapper>
