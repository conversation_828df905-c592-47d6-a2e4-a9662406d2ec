<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.savas.hub.module.collect.dal.mapper.DictRateMapper">


    <select id="getRateFeeCode" resultType="cn.savas.hub.framework.common.core.KeyValue">
        select rate_Code as key, rate_Value as value
        from dict_RateStandard
    </select>
</mapper>
