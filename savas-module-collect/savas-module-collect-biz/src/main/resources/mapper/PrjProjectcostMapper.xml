<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.savas.hub.module.collect.dal.mapper.PrjProjectcostMapper">
    <resultMap id="PrjProjectcost" type="cn.savas.hub.module.collect.dal.dataobject.FeeProjectcost">
        <result property="feeName" column="Fee_Name"/>
        <result property="feeId" column="fee_ID"/>
        <result property="feePid" column="fee_PID"/>
        <result property="feeCode" column="fee_Code"/>
        <result property="feeHostmodel" column="fee_hostmodel"/>
        <result property="feeParameters" column="fee_Parameters" typeHandler="org.apache.ibatis.type.ByteArrayTypeHandler"/>
        <result property="feeSortid" column="fee_SortID"/>
        <result property="feeSequence" column="fee_Sequence"/>
        <result property="feeExpression" column="fee_Expression"/>
        <result property="feeRate" column="fee_Rate"/>
        <result property="feeExpressionbasis" column="fee_ExpressionBasis"/>
        <result property="feeDescription" column="fee_Description"/>
        <result property="feeTaxrate" column="fee_TaxRate"/>
        <result property="feeState" column="fee_State"/>
    </resultMap>
    <update id="updatePrjCostFeeById">
        update PRJ_ProjectCost set fee=#{fee} where fee_ID=#{feeId}
    </update>
    <update id="updateEngFeeById">
        update prj_engineering set fee=#{fee} where eng_id=#{feeId}
    </update>
    <update id="updateEngCostFeeById">
        update  prj_engineeringcost set fee=#{fee} where fee_id=#{feeId}
    </update>

    <select id="selectProjectCostByEngId" resultMap="PrjProjectcost">
        SELECT
            Fee_Name,fee_ID,fee_PID,fee_Code,fee_hostmodel,fee_Parameters,fee_SortID,fee_Sequence,fee_Expression,fee_Rate,fee_ExpressionBasis,fee_Description ,fee_TaxRate, fee_State
        FROM
            PRJ_ProjectCost
        where
            fee_hostmodel = #{hostModelId}
        order by fee_SortID, fee_Sequence;
    </select>
</mapper>
