<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.savas.hub</groupId>
        <artifactId>savas-module-collect</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>savas-module-collect-biz</artifactId>

    <name>${project.artifactId}</name>
    <description>
        collect 汇总计算 模块，主要提供两块能力：
        1. 基于sqlite 数据 汇总 迁移
        2. 基于sqlite 数据重新计算
    </description>
    <dependencies>
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-module-collect-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-module-client-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-module-labeling-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>
    </dependencies>

</project>
