package cn.savas.hub.module.client.enums.project;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/9/12 11:35
 */
@Getter
public enum ProjectSumFileSourceEnum {
    /**
     * 更换工程负责人
     */
    UPDATE_ENG_LEADER("UPDATE_PROJECT_LEADER"),
    /**
     * 修改工程数据
     */
    UPDATE_ENG_DATA("UPDATE_PROJECT_DATA"),
    /**
     * 修改工程设置
     */
    UPDATE_ENG_SETTING("UPDATE_PROJECT_SETTING"),
    /**
     * 项目版本升级
     */
    PROJECT_SCRIPT_UPGRADE("PROJECT_VERSION_UPGRADE"),
    /**
     * 上传项目文件
     */
    UPLOAD_PROJECT_FILE("UPLOAD_PROJECT_FILE"),
    /**
     * 合并项目文件
     */
    MERGE_PROJECT_FILE("MERGE_PROJECT_FILE")
    ;

    private final String value;

    ProjectSumFileSourceEnum(String value) {
        this.value = value;
    }
}
