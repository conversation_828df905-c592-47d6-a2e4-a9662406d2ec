package cn.savas.hub.module.client.api.project;

import cn.savas.hub.framework.common.client.util.VersionInfo;
import cn.savas.hub.module.client.api.project.dto.ClientProjectFileRespDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectFileSRespDTO;
import cn.savas.hub.module.client.enums.project.ProjectSumFileSourceEnum;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2024/11/19 11:49
 */
public interface ClientProjectFileApi {
    /**
     * 根据客户端项目id，获取个人项目文件信息
     */
    ClientProjectFileRespDTO getClientProjectFile(Long projectId);

    /**
     * 根据客户端项目id，获取汇总项目文件信息
     */
    ClientProjectFileSRespDTO getClientProjectSummaryFile(Long projectId);
    /**
     * 获取最新个人项目文件
     */
    ClientProjectFileRespDTO getLatestProjectFile(Long projectId);

    /**
     * 获取最新汇总项目文件
     */
    ClientProjectFileSRespDTO getLatestCollectProjectFile(Long projectId);

    /**
     * 切换到个人项目文件数据源
     * @param projectId
     * @param supplier
     * @return
     * @param <T>
     */
    <T> T changePrjFileDs(Long projectId, Supplier<T> supplier) throws Exception;

    /**
     * 切换到汇总项目文件数据源
     * @param projectId
     * @param supplier
     * @param
     * @return
     * @param <T>
     */
    <T> T changePrjFileSumDs(Long projectId, Supplier<T> supplier);

    /**
     * 切换到汇总项目文件数据源
     * @param projectId
     * @param supplier
     * @param isWriteBack 是否写回最新汇总文件
     * @return
     * @param <T>
     */
    <T> T changePrjFileSumDs(Long projectId, Supplier<T> supplier, boolean isWriteBack, ProjectSumFileSourceEnum source);

    /**
     * 切换到汇总项目文件数据源
     * @param projectId
     * @param supplier
     * @param isWriteBack 是否写回最新汇总文件
     * @param version 写回项目版本
     * @return
     * @param <T>
     */
    <T> T changePrjFileSumDs(Long projectId, Supplier<T> supplier, boolean isWriteBack, String version, ProjectSumFileSourceEnum source);

    /**
     * 插入合并汇总文件
     */
    void insertProjectMergeFileSummary(ClientProjectFileSRespDTO clientProjectFileSRespDTO);

    /**
     * 更新汇总文件
     */
    void updateProjectFileSummary(ClientProjectFileSRespDTO clientProjectFileSRespDTO);

    /**
     * 汇总文件数据更新到表
     */
    void pullProjectFileSummary(Long projectId, String dbPath);

    /**
     * 更新旧文件为非活动状态
     * @param projectId
     */
    void setActiveFalse(Long projectId);

    void writeBackFile(Long projectId, ClientProjectFileSRespDTO file, String sqliteUrl, VersionInfo versionInfo, ProjectSumFileSourceEnum source);
}
