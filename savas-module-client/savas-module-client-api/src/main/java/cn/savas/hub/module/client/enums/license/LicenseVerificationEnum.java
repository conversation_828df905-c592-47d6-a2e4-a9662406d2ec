package cn.savas.hub.module.client.enums.license;

import cn.savas.hub.framework.common.exception.ErrorCode;
import cn.savas.hub.module.client.enums.ErrorCodeConstants;

/**
 * <AUTHOR>
 * @date 2025/8/15 17:25
 */
public enum LicenseVerificationEnum {
    VALID("许可证有效"),
    INVALID_SIGNATURE("数字签名无效"),
    EXPIRED("许可证已过期"),
    HARDWARE_MISMATCH("硬件ID不匹配"),
    PARSE_ERROR("许可证解析失败"),
    FILE_NOT_FOUND("许可证文件不存在");

    private final String message;

    LicenseVerificationEnum(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
    public ErrorCode toErrorCode() {
        switch (this) {
            case INVALID_SIGNATURE:
                return ErrorCodeConstants.INVALID_SIGNATURE;
            case EXPIRED:
                return ErrorCodeConstants.LICENSE_EXPIRED;
            case HARDWARE_MISMATCH:
                return ErrorCodeConstants.HARDWARE_MISMATCH;
            case PARSE_ERROR:
                return ErrorCodeConstants.LICENSE_PARSE_ERROR;
            case FILE_NOT_FOUND:
                return ErrorCodeConstants.LICENSE_FILE_NOT_FOUND;
            case VALID:
            default:
                return ErrorCodeConstants.LICENSE_SYSTEM_ERROR; // VALID 不对应任何错误码
        }
    }
}
