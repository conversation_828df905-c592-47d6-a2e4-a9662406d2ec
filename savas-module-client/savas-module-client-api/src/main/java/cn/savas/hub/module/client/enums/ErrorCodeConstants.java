package cn.savas.hub.module.client.enums;

import cn.savas.hub.framework.common.exception.ErrorCode;

/**
 * client 错误码枚举类
 *
 * client 系统，使用 1-001-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 参数配置 1-020-000-000 ==========
    ErrorCode PROJECT_NOT_EXISTS = new ErrorCode(1_020_000_001, "项目不存在");
    ErrorCode PROJECT_FILE_NOT_EXISTS = new ErrorCode(1_020_000_002, "项目文件不存在");
    ErrorCode PROJECT_ID_INVALID = new ErrorCode(1_020_000_003, "项目ID(HostModel)不统一");
    ErrorCode PROJECT_DATASOURCE_SWITCH_FAIL = new ErrorCode(1_020_000_004, "项目数据源切换失败:{}");
    ErrorCode PROJECT_FILE_NO_PERMISSION = new ErrorCode(1_020_000_005, "没有权限下载该文件");
    ErrorCode PROJECT_NOT_BIND_BPM = new ErrorCode(1_020_000_006, "项目未绑定流程模板");
    ErrorCode SERIALIZE_FAIL = new ErrorCode(1_020_000_007, "序列化返回值失败");
    ErrorCode PROJECT_FILE_MERGE_FAIL = new ErrorCode(1_020_000_008, "项目文件合并失败");
    ErrorCode UNSUPPORTED_ALIAS_TYPE = new ErrorCode(1_020_000_009, "不受支持的alias类型");
    ErrorCode PROJECT_NO_PERMISSION = new ErrorCode(1_020_000_010, "没有权限访问该项目");
    ErrorCode PROJECT_SUMMARY_FILE_NOT_EXISTS = new ErrorCode(1_020_000_011, "项目汇总文件不存在:{}");
    ErrorCode PROJECT_FILE_CALCULATE_FAIL = new ErrorCode(1_020_000_012, "项目文件计算失败:{}");
    ErrorCode PROJECT_NO_PERMISSION_LOCK_FLOW = new ErrorCode(1_020_000_013, "没有权限锁定流程");
    ErrorCode PROJECT_NO_PERMISSION_UNLOCK_FLOW = new ErrorCode(1_020_000_014, "没有权限解锁流程");
    ErrorCode PROJECT_FLOW_LOCKED = new ErrorCode(1_020_000_015, "已被锁定:{}");
    ErrorCode PROJECT_FLOW_FORCE_MERGE_ENGID_EMPTY = new ErrorCode(1_020_000_016, "强制合并工程ID不能为空");
    ErrorCode PROJECT_SUMMARY_FILE_NO_PERMISSION = new ErrorCode(1_020_000_017, "没有权限获取汇总文件");
    ErrorCode PROJECT_FLOW_LOCK_HOSTMODELIDS_EMPTY = new ErrorCode(1_020_000_018, "锁定流程时，工程ID集合不能为空");
    ErrorCode PROJECT_SETTING_TYPE_NOT_FOUND = new ErrorCode(1_020_000_019, "未找到适合的设置类型:{}，请检查设置类型是否正确");
    ErrorCode PROJECT_FILE_CONTENT_FAIL = new ErrorCode(1_020_000_020, "获取项目文件内容失败:{}");

    // ========== 许可证模块 1-020-001-000 ==========

    // ========== 许可证文件相关错误 1-020-001-001 ~ 1-020-001-010 ==========
    ErrorCode LICENSE_FILE_NOT_FOUND = new ErrorCode(1_020_001_001, "许可证文件不存在");
    ErrorCode LICENSE_PARSE_ERROR = new ErrorCode(1_020_001_002, "许可证解析失败");
    ErrorCode INVALID_LICENSE_FORMAT = new ErrorCode(1_020_001_003, "许可证格式无效");
    ErrorCode LICENSE_FILE_READ_ERROR = new ErrorCode(1_020_001_004, "许可证文件读取失败");

    // ========== 许可证验证相关错误 1-020-001-011 ~ 1-020-001-020 ==========
    ErrorCode INVALID_SIGNATURE = new ErrorCode(1_020_001_011, "数字签名无效");
    ErrorCode LICENSE_EXPIRED = new ErrorCode(1_020_001_012, "许可证已过期");
    ErrorCode HARDWARE_MISMATCH = new ErrorCode(1_020_001_013, "硬件ID不匹配");
    ErrorCode VALIDATION_ERROR = new ErrorCode(1_020_001_014, "许可证验证失败");

    // ========== 加密和硬件相关错误 1-020-001-021 ~ 1-020-001-030 ==========
    ErrorCode CRYPTO_ERROR = new ErrorCode(1_020_001_021, "加密操作失败");
    ErrorCode HARDWARE_ID_ERROR = new ErrorCode(1_020_001_022, "硬件ID获取失败");
    ErrorCode LICENSE_DECRYPT_ERROR = new ErrorCode(1_020_001_023, "许可证解密失败");

    // ========== 配置和系统相关错误 1-020-001-031 ~ 1-020-001-040 ==========
    ErrorCode CONFIGURATION_ERROR = new ErrorCode(1_020_001_031, "许可证配置错误");
    ErrorCode LICENSE_FEATURE_NOT_SUPPORTED = new ErrorCode(1_020_001_032, "许可证不支持此功能");

    // ========== 用户限制相关错误 1-020-001-041 ~ 1-020-001-050 ==========
    ErrorCode MAX_USERS_EXCEEDED = new ErrorCode(1_020_001_041, "已达到最大在线用户数量限制");
    ErrorCode LICENSE_USER_LIMIT_INVALID = new ErrorCode(1_020_001_042, "许可证用户数量配置无效");

    // ========== 系统错误 1-020-001-051 ~ 1-020-001-100 ==========
    ErrorCode LICENSE_SYSTEM_ERROR = new ErrorCode(1_020_001_051, "许可证系统内部错误");

    // ========== 更新脚本 1-020-002-001 ~ 1-020-002-010 ==========
    ErrorCode CLIENT_UPDATE_SCRIPT_NOT_FOUND_VERSION = new ErrorCode(1_020_002_001, "更新脚本执行失败，未找到对应版本: {}");
    ErrorCode CLIENT_UPDATE_SCRIPT_NOT_FOUND = new ErrorCode(1_020_002_002, "更新脚本不存在");
    ErrorCode CLIENT_UPDATE_SCRIPT_VERSION_TOO_LOW = new ErrorCode(1_020_002_003, "更新后汇总文件版本仍然过低");
}
