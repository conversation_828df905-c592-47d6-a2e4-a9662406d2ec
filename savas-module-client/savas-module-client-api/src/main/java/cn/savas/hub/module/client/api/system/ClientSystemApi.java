package cn.savas.hub.module.client.api.system;

import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.client.api.system.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/22 16:25
 */
public interface ClientSystemApi {
    boolean hasAnyPermissions(Long userId, String... permissions);

    /**
     * 获取客户端权限列表
     * @return
     */
    List<ClientFuncPermsRespDTO> getFuncPermissions();

    /**
     * 获取角色客户端权限
     * @param roleId
     * @return
     */
    List<Long> getRoleClientPermissions(Long roleId);

    /**
     * 赋予角色客户端权限
     * @param roleId
     * @param menuIds
     */
    void assignRoleClient(Long roleId, List<Long> menuIds);

    Boolean addFuncPermissions(ClientFuncPermsReqDTO req);

    Boolean editFuncPermissions(ClientFuncPermsReqDTO bean);

    Boolean deleteFuncPermissions(Long id);

    PageResult<ClientFuncPermsRespDTO> getFuncPermissionsPage(ClientFuncPermsPageReqDTO pageReqVO);

    /**
     * 获取项目权限数据
     */
    List<ClientDataPermsTreeRespDTO> getProjectPermsDataTree(Long projectId);

    /**
     * 项目权限数据增加人员
     * @param req
     * @return
     */
    Long addProjectPermsUser(ClientDataPermsPrjReqDTO req);

    /**
     * 项目权限数据删除人员
     * @param projectId
     * @param modelId
     * @param userId
     * @return
     */
    Boolean deleteProjectPermsUser(Long projectId, Long modelId, Long userId);

    /**
     * 根据modelId获取权限数据
     * @param projectId
     * @param modelId
     * @return
     */
    List<ClientDataPermsRespDTO> getProjectDataPerms(Long projectId, Long modelId);

    /**
     * 上传客户端升级脚本
     */
    void uploadUpdateScript(String filePath, String fileUrl);
}
