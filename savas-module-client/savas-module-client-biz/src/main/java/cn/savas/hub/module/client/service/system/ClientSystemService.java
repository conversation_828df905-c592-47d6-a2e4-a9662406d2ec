package cn.savas.hub.module.client.service.system;

import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeReqVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/22 15:28
 */
public interface ClientSystemService {
    /**
     * 客户端获取用户通知
     */
    List<ClientNoticeRespVO> getNotice();

    /**
     * 客户端发送用户通知
     */
    void sendNotice(ClientNoticeReqVO notice);
}
