package cn.savas.hub.module.client.license.verifier;

import cn.savas.hub.module.client.enums.license.LicenseVerificationEnum;
import cn.savas.hub.module.client.license.crypto.DigitalSignatureVerifier;
import cn.savas.hub.module.client.license.dto.AiccLicense;
import cn.savas.hub.module.client.license.dto.LicenseProduct;
import cn.savas.hub.module.client.license.dto.LicenseQuotaLibrary;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 许可证验证器
 * 提供完整的许可证验证功能
 */
@Slf4j
@Component
public class LicenseVerifier {

    /**
     * 验证许可证对象
     *
     * @param license 许可证对象
     * @param currentHardwareId 当前机器的硬件ID
     * @return 验证结果
     */
    public LicenseVerificationEnum verify(AiccLicense license, String currentHardwareId) {
        if (license == null) {
            return LicenseVerificationEnum.PARSE_ERROR;
        }

        // 1. 验证数字签名
        if (!verifyDigitalSignature(license)) {
            return LicenseVerificationEnum.INVALID_SIGNATURE;
        }

        // 2. 检查是否过期
        if (license.isExpired()) {
            return LicenseVerificationEnum.EXPIRED;
        }

        // 3. 验证硬件ID
        if (!verifyHardwareId(license, currentHardwareId)) {
            return LicenseVerificationEnum.HARDWARE_MISMATCH;
        }

        return LicenseVerificationEnum.VALID;
    }

    /**
     * 验证数字签名
     *
     * @param license 许可证对象
     * @return 签名是否有效
     */
    private boolean verifyDigitalSignature(AiccLicense license) {
        try {
            if (license.getSignature() == null || license.getSignature().isEmpty()) {
                log.error("许可证缺少数字签名");
                return false;
            }

            // 获取签名数据
            String signatureData = license.getSignatureData();
            log.debug("签名数据: {}", signatureData);

            // 验证签名
            boolean isValid = DigitalSignatureVerifier.verifyLicenseSignature(signatureData, license.getSignature());

            if (!isValid) {
                log.error("数字签名验证失败");
            } else {
                log.info("数字签名验证成功");
            }

            return isValid;
        } catch (Exception e) {
            log.error("验证数字签名时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证硬件ID
     *
     * @param license 许可证对象
     * @param currentHardwareId 当前硬件ID
     * @return 硬件ID是否匹配
     */
    private boolean verifyHardwareId(AiccLicense license, String currentHardwareId) {
        if (license.getHardwareId() == null || license.getHardwareId().isEmpty()) {
            log.error("许可证缺少硬件ID");
            return false;
        }

        if (currentHardwareId == null || currentHardwareId.isEmpty()) {
            log.error("当前硬件ID为空");
            return false;
        }

        boolean matches = license.getHardwareId().equalsIgnoreCase(currentHardwareId);

        if (!matches) {
            log.error("硬件ID不匹配: 许可证硬件ID={}, 当前硬件ID={}",
                license.getHardwareId(), currentHardwareId);
        } else {
            log.info("硬件ID验证成功");
        }

        return matches;
    }

    /**
     * 获取许可证详细信息
     *
     * @param license 许可证对象
     * @return 详细信息字符串
     */
    public static String getLicenseDetails(AiccLicense license) {
        if (license == null) {
            return "无效的许可证";
        }

        StringBuilder details = new StringBuilder();
        details.append("许可证详细信息:\n");
        details.append("  许可证ID: ").append(license.getLicenseId()).append("\n");
        details.append("  公司名称: ").append(license.getCompanyName()).append("\n");
        details.append("  硬件ID: ").append(license.getHardwareId()).append("\n");
        details.append("  签发日期: ").append(license.getIssueDate()).append("\n");
        details.append("  到期日期: ").append(license.getExpiryDate() != null ? license.getExpiryDate() : "永不过期").append("\n");
        details.append("  备注: ").append(license.getRemarks() != null ? license.getRemarks() : "无").append("\n");

        details.append("  授权产品:\n");
        for (int i = 0; i < license.getProducts().size(); i++) {
            LicenseProduct product = license.getProducts().get(i);
            details.append("    ").append(i + 1).append(". ").append(product.getName())
                   .append(" (ID: ").append(product.getId())
                   .append(", 点数: ").append(product.getPoints()).append(")\n");
        }

        details.append("  授权定额库:\n");
        for (int i = 0; i < license.getQuotaLibraries().size(); i++) {
            LicenseQuotaLibrary quota = license.getQuotaLibraries().get(i);
            details.append("    ").append(i + 1).append(". ").append(quota.getName())
                   .append(" (ID: ").append(quota.getId())
                   .append(", 版本: ").append(quota.getVersion()).append(")\n");
        }

        return details.toString();
    }
}
