package cn.savas.hub.module.client.service.project;

import cn.savas.hub.module.client.controller.desktop.project.vo.*;
import cn.savas.hub.module.client.dal.dataobject.ClientEngineeringDO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectDO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectFileDO;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:37
 */
public interface ClientProjectService {
    /**
     * 根据项目id获取项目信息
     */
    ClientProjectDO getProjectByPrjId(Long projectId);

    /**
     * 根据项目原始ID获取项目信息
     */
    ClientProjectDO getProjectByOriginalId(Long originalId);
    /**
     * 上传项目文件（保存文件）
     * @param prjId
     * @param fileName
     * @param content
     * @param compiledversion 个人文件版本号
     */
    void uploadProjectFile(Long prjId, String fileName, byte[] content, Integer compiledversion) throws Exception;

    /**
     * 下载项目文件
     * @param response
     * @param filePath
     * @throws Exception
     */
    void downloadProjectFile(HttpServletResponse response, ClientDownLoadFileReqVO reqVO) throws Exception;

    /**
     * 上传项目数据
     * @param createReqVO
     * @return
     */
    void uploadHostModelTableData(ClientProjectCreateReqVO createReqVO);

    /**
     * 获取项目列表
     * @return
     */
    List<ClientProjectRespVO> getProjectList();

    /**
     * 删除项目
     * @param prjId
     * @return
     */
    void deleteProject(Long prjId);

    /**
     * 修改项目
     * @param createReqVO
     * @return
     */
    boolean updateProject(ClientProjectCreateReqVO createReqVO);

    /**
     * 获取项目文件版本号
     * @param engProjectId
     * @return
     */
    ClientProjectFileVersionVO getProjectFileVersion(Long engProjectId);

    /**
     * 获取工程树结构
     * @param engProjectId
     * @return
     */
    List<ClientEngineeringVO> getEngineeringTree(Long engProjectId, Long originalid);

    /**
     * 获取工程列表（无业务逻辑）
     */
    List<ClientEngineeringVO> getEngineeringList(Long engProjectId);

    /**
     * 获取项目文件
     * @param engProjectId
     * @return
     */
    ClientProjectFileInfoVO getProjectFile(Long engProjectId);

    /**
     * 绑定流程定义
     * @param projectid
     * @param processDefinitionId
     */
    void bindDefinition(Long projectid, String processDefinitionId);

    /**
     * 合并项目文件
     * @param mergeProjectFileReqVO
     */
    void mergeProjectFile(ClientMergeProjectFileReqVO mergeProjectFileReqVO, String product);

    void copyPrjUploadEngineering(ClientEngineeringReqVO.EngineeringClass req);

    /**
     * 更换工程负责人
     */
    void updateDirector(UpdateDirectorIdReqVO req);

    /**
     * 项目文件计算
     * @param mergeProjectFileReqVO
     */
    void calculateProjectData(ClientMergeProjectFileReqVO mergeProjectFileReqVO);

    /**
     * 获取工程
     * @param projectid
     * @param hostmodelids
     */
    List<ClientEngineeringDO> getEngByIds(Long projectid, List<Long> hostmodelids);

    /**
     * 获取个人项目文件
     */
    List<ClientProjectFileDO> getPersonalProjectFile(Long projectid, Collection<Long> userids);

    /**
     * 保存项目用户设置
     * @param reqVO
     * @return
     */
    void saveProjectUserSetting(ClientProjectUserSettingReqVO reqVO);

    /**
     * 获取项目用户设置
     * @param projectId
     * @return
     */
    List<ClientProjectUserSettingRespVO> getProjectUserSetting(Long projectId);

    /**
     * 获取汇总文件版本
     * @param engProjectId
     * @param version
     * @return
     */
    String getSumFileByVersion(Long engProjectId, Integer version);

    /**
     * 获取工程检验检测费
     * @param engProjectId
     * @return
     */
    List<ClientTestEngineeringVO> getTestEngineering(Long engProjectId);

    /**
     * 修改工程/项目数据
     */
    void updateEngineeringData(ClientUpdateEngDataReqVO reqVO);
}
