package cn.savas.hub.module.client.license.service;

import cn.hutool.extra.spring.SpringUtil;
import cn.savas.hub.module.client.enums.license.LicenseVerificationEnum;
import cn.savas.hub.module.client.license.config.LicenseConfiguration;
import cn.savas.hub.module.client.license.constants.LicenseConstants;
import cn.savas.hub.module.client.license.dto.LicenseInfo;
import cn.savas.hub.module.client.license.dto.AiccLicense;
import cn.savas.hub.module.client.license.parser.LicenseParser;
import cn.savas.hub.module.client.license.util.HardwareIdGenerator;
import cn.savas.hub.module.client.license.verifier.LicenseVerifier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.client.enums.ErrorCodeConstants.*;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.concurrent.locks.ReentrantReadWriteLock;


/**
 * 许可证服务
 * 提供许可证验证和信息访问功能
 */
@Slf4j
@Service
public class LicenseService {
    private final LicenseConfiguration licenseConfig;
    private final HardwareIdGenerator hardwareIdGenerator;
    private final LicenseParser licenseParser;
    private final LicenseVerifier licenseVerifier;

    private volatile LicenseInfo cachedLicenseInfo;
    private volatile String cachedHardwareId;
    private volatile LocalDateTime lastValidationTime;

    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    @Autowired
    public LicenseService(LicenseConfiguration licenseConfig,
                         HardwareIdGenerator hardwareIdGenerator,
                         LicenseParser licenseParser,
                         LicenseVerifier licenseVerifier) {
        this.licenseConfig = licenseConfig;
        this.hardwareIdGenerator = hardwareIdGenerator;
        this.licenseParser = licenseParser;
        this.licenseVerifier = licenseVerifier;
    }

    /**
     * 应用启动时自动验证许可证
     */
    @PostConstruct
    public void initializeLicense() {
        if (!licenseConfig.isEnabled()) {
            log.info("许可证验证已禁用");
            return;
        }

        log.info("开始初始化许可证验证...");

        try {
            // 获取硬件ID
            cachedHardwareId = hardwareIdGenerator.getCurrentHardwareId();
            log.info("当前硬件ID: {}", cachedHardwareId);

            // 验证许可证
            LicenseInfo licenseInfo = validateLicense();

            if (licenseInfo.isValid()) {
                cachedLicenseInfo = licenseInfo;
                lastValidationTime = LocalDateTime.now();

                if (licenseConfig.isShowDetailsOnStartup()) {
                    logLicenseDetails(licenseInfo);
                }
                log.info("许可证验证成功，应用程序已获得授权");
            } else {
                handleValidationFailure("许可证验证失败");
            }
        } catch (Exception e) {
            handleValidationFailure("许可证初始化失败: " + e.getMessage());
        }
    }
    /**
     * 缓存的许可证验证
     * 基于许可证内容和硬件ID进行缓存
     */
    @Cacheable(value = LicenseConstants.Cache.LICENSE_VALIDATION_CACHE,
               key = "#license.licenseId + '_' + #hardwareId",
               cacheManager = "licenseCacheManager")
    public LicenseVerificationEnum validateLicenseWithCache(AiccLicense license, String hardwareId) {
        log.debug("验证许可证: licenseId={}, hardwareId={}", license.getLicenseId(), hardwareId);
        return licenseVerifier.verify(license, hardwareId);
    }

    /**
     * 清除许可证相关缓存
     */
    @CacheEvict(value = {
        LicenseConstants.Cache.LICENSE_CONTENT_CACHE,
        LicenseConstants.Cache.LICENSE_VALIDATION_CACHE,
            LicenseConstants.Cache.LICENSE_INFO_CACHE
    }, allEntries = true, cacheManager = "licenseCacheManager")
    public void clearLicenseCache() {
        log.info("清除许可证缓存");
    }

    /**
     * 验证许可证
     */
    private LicenseInfo validateLicense() {
        Path licenseFilePath = findLicenseFile();
        try {
            // 解析许可证文件
            AiccLicense license = licenseParser.parseFile(licenseFilePath);

            // 使用缓存的验证方法
            LicenseVerificationEnum result = getSelf().validateLicenseWithCache(license, cachedHardwareId);

            // 创建许可证信息对象
            LicenseInfo licenseInfo = createLicenseInfo(license);
            licenseInfo.setValid(result == LicenseVerificationEnum.VALID);
            licenseInfo.setLastValidationTime(LocalDateTime.now());

            if (result != LicenseVerificationEnum.VALID) {
                throw exception(result.toErrorCode());
            }

            return licenseInfo;

        } catch (IOException e) {
            throw exception(LICENSE_FILE_READ_ERROR);
        }
    }

    /**
     * 创建许可证信息对象
     */
    private LicenseInfo createLicenseInfo(AiccLicense license) {
        LicenseInfo info = new LicenseInfo();
        info.setLicenseId(license.getLicenseId());
        info.setCompanyName(license.getCompanyName());
        info.setHardwareId(license.getHardwareId());
        info.setIssueDate(license.getIssueDate());
        info.setExpiryDate(license.getExpiryDate());
        info.setRemarks(license.getRemarks());
        info.setProducts(license.getProducts());
        info.setQuotaLibraries(license.getQuotaLibraries());
        return info;
    }



    /**
     * 处理验证失败
     */
    private void handleValidationFailure(String message) {
        log.error(message);

        if (licenseConfig.isFailFast()) {
            throw exception(VALIDATION_ERROR);
        }
    }

    /**
     * 记录许可证详细信息
     */
    private void logLicenseDetails(LicenseInfo licenseInfo) {
        log.info("=== 许可证详细信息 ===");
        log.info("许可证ID: {}", licenseInfo.getLicenseId());
        log.info("公司名称: {}", licenseInfo.getCompanyName());
        log.info("签发日期: {}", licenseInfo.getIssueDate());
        log.info("到期日期: {}", licenseInfo.getExpiryDate() != null ? licenseInfo.getExpiryDate() : "永不过期");

        if (licenseInfo.getExpiryDate() != null) {
            long remainingDays = licenseInfo.getRemainingDays();
            if (remainingDays > 0) {
                log.info("剩余有效天数: {} 天", remainingDays);
            }
        }

        log.info("授权产品数量: {}", licenseInfo.getProducts().size());
        licenseInfo.getProducts().forEach(product ->
            log.info("  - {} (ID: {}, 点数: {})", product.getName(), product.getId(), product.getPoints())
        );

        log.info("授权定额库数量: {}", licenseInfo.getQuotaLibraries().size());
        licenseInfo.getQuotaLibraries().forEach(quota ->
            log.info("  - {} (ID: {}, 版本: {})", quota.getName(), quota.getId(), quota.getVersion())
        );

        if (licenseInfo.getRemarks() != null && !licenseInfo.getRemarks().isEmpty()) {
            log.info("备注: {}", licenseInfo.getRemarks());
        }

        log.info("=====================");
    }

    /**
     * 获取许可证信息
     */
    @Cacheable(value = LicenseConstants.Cache.LICENSE_INFO_CACHE, unless = "#result == null", cacheManager = "licenseCacheManager")
    public LicenseInfo getLicenseInfo() {
        lock.readLock().lock();
        try {
            if (cachedLicenseInfo == null) {
                throw exception(VALIDATION_ERROR);
            }
            return cachedLicenseInfo;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 检查是否有指定产品的授权
     */
    public boolean hasProductLicense(String productId) {
        return getSelf().getLicenseInfo().hasProduct(productId);
    }

    /**
     * 获取指定产品的授权点数
     */
    public int getProductPoints(String productId) {
        return getSelf().getLicenseInfo().getProductPoints(productId);
    }

    /**
     * 检查是否有指定定额库的授权
     */
    public boolean hasQuotaLibraryLicense(String quotaLibraryId) {
        return getSelf().getLicenseInfo().hasQuotaLibrary(quotaLibraryId);
    }

    /**
     * 获取指定定额库的版本
     */
    public String getQuotaLibraryVersion(String quotaLibraryId) {
        return getSelf().getLicenseInfo().getQuotaLibraryVersion(quotaLibraryId);
    }

    /**
     * 检查许可证是否有效
     */
    public boolean isLicenseValid() {
        try {
            LicenseInfo info = getSelf().getLicenseInfo();
            return info.isValid() && !info.isExpired();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取当前硬件ID
     */
    public String getCurrentHardwareId() {
        return cachedHardwareId;
    }

    /**
     * 获取许可证剩余有效天数
     */
    public long getRemainingDays() {
        return getSelf().getLicenseInfo().getRemainingDays();
    }

    /**
     * 强制重新验证许可证
     */
    public void revalidateLicense() {
        lock.writeLock().lock();
        try {
            log.info("开始重新验证许可证...");
            LicenseInfo newLicenseInfo = validateLicense();

            if (newLicenseInfo.isValid()) {
                cachedLicenseInfo = newLicenseInfo;
                lastValidationTime = LocalDateTime.now();
                log.info("许可证重新验证成功");
            } else {
                log.error("许可证重新验证失败");
                throw exception(VALIDATION_ERROR);
            }
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 查找License文件
     */
    private Path findLicenseFile() {
        // 1. 首先在当前工作目录查找
        Path currentDir = Paths.get(System.getProperty("user.dir"), LicenseConstants.Files.DEFAULT_LICENSE_FILE_NAME);
        if (Files.exists(currentDir)) {
            return currentDir;
        }

        // 2. 在应用jar包同目录查找
        String jarPath = LicenseService.class.getProtectionDomain()
                .getCodeSource().getLocation().getPath();
        try {
            Path jarDir = Paths.get(new File(jarPath).getParent(), LicenseConstants.Files.DEFAULT_LICENSE_FILE_NAME);
            if (Files.exists(jarDir)) {
                return jarDir;
            }
        } catch (Exception e) {
            log.debug("在jar包目录查找License文件失败", e);
        }

        // 3. 在classpath根目录查找
        try {
            Path classPath = Paths.get(LicenseService.class.getClassLoader()
                    .getResource("").toURI()).resolve(LicenseConstants.Files.DEFAULT_LICENSE_FILE_NAME);
            if (Files.exists(classPath)) {
                return classPath;
            }
        } catch (Exception e) {
            log.debug("在classpath查找License文件失败", e);
        }
        return null;
    }

    /**
     * 获得自身的代理对象
     *
     * @return 自己
     */
    private LicenseService getSelf() {
        return SpringUtil.getBean(getClass());
    }
}
