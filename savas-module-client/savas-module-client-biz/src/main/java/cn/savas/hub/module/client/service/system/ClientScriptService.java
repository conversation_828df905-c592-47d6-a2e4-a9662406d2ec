package cn.savas.hub.module.client.service.system;

import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeReqVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/22 15:28
 */
public interface ClientScriptService {
    /**
     * 检查项目版本是否是最新
     * @param version 当前版本
     * @return true 是最新版本，false 不是最新版本
     */
    boolean checkProjectVersion(String version);

    /**
     * 上传更新脚本
     */
    void uploadUpdateScript(String filePath, String fileUrl);

    /**
     * 执行更新脚本
     * @param version 当前版本
     */
    void executeUpdateScript(Long projectId, String version);
}
