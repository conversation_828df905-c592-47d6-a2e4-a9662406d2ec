package cn.savas.hub.module.client.controller.desktop.system;

import cn.hutool.core.util.StrUtil;
import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.pojo.ClientResult;
import cn.savas.hub.framework.security.config.SecurityProperties;
import cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientLoginReqVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientLoginRespVO;
import cn.savas.hub.module.client.enums.system.AuthUserErrorEnum;
import cn.savas.hub.module.client.service.system.ClientAuthService;
import cn.savas.hub.module.system.api.auth.AdminAuthApi;
import cn.savas.hub.module.system.api.auth.dto.ClientAuthLoginRespDTO;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

import static cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils.AUTHORIZATION_BEARER;

/**
 * <AUTHOR>
 * @date 2024/10/24 14:51
 */
@Tag(name = "协同客户端 - 认证")
@RestController
@RequestMapping("/client/auth")
@Slf4j
public class ClientAuthController {
    @Resource
    private ClientAuthService clientAuthService;
    @Resource
    private SecurityProperties securityProperties;


    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "客户端登录")
    public ClientResult<ClientLoginRespVO> login(HttpServletResponse response, @Valid @RequestBody ClientLoginReqVO loginReqVO) {
        // 调用登录 API
        ClientAuthLoginRespDTO loginRespDTO = clientAuthService.clientLogin(loginReqVO.getLoginName(), loginReqVO.getPassword());

        // 构建响应对象
        ClientLoginRespVO respVO = buildResponseVO(loginRespDTO);

        // 设置 Authorization 头
        setAuthorizationHeader(response, respVO.getAccessToken());

        // 处理登录结果
        if (loginRespDTO.getValue().equals(AuthUserErrorEnum.LOGIN_SUCCESS.getCode())) {
            return ClientResult.success(respVO);
        }
        return ClientResult.error(4, AuthUserErrorEnum.getMessage(loginRespDTO.getValue()), respVO);
    }

    private ClientLoginRespVO buildResponseVO(ClientAuthLoginRespDTO loginRespDTO) {
        ClientLoginRespVO respVO = new ClientLoginRespVO();
        ClientLoginRespVO.Items items = new ClientLoginRespVO.Items();

        items.setLoginname(loginRespDTO.getLoginName());
        items.setExpiresTime(loginRespDTO.getExpiresTime());
        items.setId(loginRespDTO.getUserId());
        items.setOrganization(loginRespDTO.getDeptName());
        items.setName(loginRespDTO.getNickname());
        items.setLastpasswordtime(loginRespDTO.getLoginDate());
        items.setAlias(ClientClassIdEnum.USER.getCode());
        items.setOperate(0);

        respVO.setItems(Lists.newArrayList(items));
        respVO.setWorkerid(loginRespDTO.getUserId());
        respVO.setValue(loginRespDTO.getValue());
        respVO.setAccessToken(loginRespDTO.getAccessToken());

        return respVO;
    }

    private void setAuthorizationHeader(HttpServletResponse response, String accessToken) {
        if (StringUtils.isNotBlank(accessToken)) {
            response.addHeader(HttpHeaders.AUTHORIZATION, AUTHORIZATION_BEARER + " " + accessToken);
        } else {
            log.warn("Access token is empty for login request");
        }
    }

    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "客户端登出")
    public ClientResult<Map> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            clientAuthService.clientLogout(token);
        }
        return ClientResult.success(new HashMap(0));
    }

    @PostMapping("/forceLogout")
    @PermitAll
    @Operation(summary = "客户端强制登出")
    public ClientResult<Map> forceLogout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(
                request,
                securityProperties.getTokenHeader(),
                securityProperties.getTokenParameter()
        );
        clientAuthService.clientForceLogout(token);
        return ClientResult.success(new HashMap(0));
    }
}
