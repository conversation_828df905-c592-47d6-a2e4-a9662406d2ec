package cn.savas.hub.module.client.license.job;

import cn.savas.hub.module.client.license.config.LicenseConfiguration;
import cn.savas.hub.module.client.license.service.LicenseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 许可证定期验证调度器
 */
@Component
@ConditionalOnProperty(name = "aicc.license.enable-periodic-validation", havingValue = "true", matchIfMissing = true)
public class LicenseValidationScheduler {

    private static final Logger logger = LoggerFactory.getLogger(LicenseValidationScheduler.class);

    private final LicenseService licenseService;
    private final LicenseConfiguration licenseConfig;

    @Autowired
    public LicenseValidationScheduler(LicenseService licenseService, LicenseConfiguration licenseConfig) {
        this.licenseService = licenseService;
        this.licenseConfig = licenseConfig;
    }

    /**
     * 定期验证许可证
     */
    @Scheduled(fixedRateString = "#{@licenseConfiguration.periodicValidationInterval * 1000}")
    public void validateLicensePeriodically() {
        if (!licenseConfig.isEnabled() || !licenseConfig.isEnablePeriodicValidation()) {
            return;
        }

        try {
            logger.debug("开始定期许可证验证...");

            // 检查许可证是否仍然有效
            if (!licenseService.isLicenseValid()) {
                logger.warn("定期验证发现许可证无效，尝试重新验证...");
                licenseService.revalidateLicense();
            }

            // 检查即将过期的许可证
            long remainingDays = licenseService.getRemainingDays();
            if (remainingDays <= 30 && remainingDays > 0) {
                logger.warn("许可证即将在 {} 天后过期，请及时续期", remainingDays);
            }
            logger.debug("定期许可证验证完成");

        } catch (Exception e) {
            logger.error("定期许可证验证失败", e);

            // 根据配置决定是否需要采取进一步行动
            if (licenseConfig.isFailFast()) {
                logger.error("许可证验证失败，应用程序将停止运行");
                System.exit(1);
            }
        }
    }
}
