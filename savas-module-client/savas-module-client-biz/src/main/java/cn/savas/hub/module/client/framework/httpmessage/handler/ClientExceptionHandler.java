package cn.savas.hub.module.client.framework.httpmessage.handler;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.framework.web.core.handler.GlobalExceptionHandler;
import cn.savas.hub.module.client.framework.httpmessage.convert.CustomMappingJackson2HttpMessageConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.client.enums.ErrorCodeConstants.SERIALIZE_FAIL;

/**
 * <AUTHOR>
 * @date 2024/11/15 14:07
 */
@Slf4j
@RestControllerAdvice(basePackages = "cn.savas.hub.module.client.controller.desktop")
public class ClientExceptionHandler implements ResponseBodyAdvice<Object> {
    private static final int ERROR_LEVEL = 4;
    private final HttpMessageConverter<Object> clientConverter;
    private final GlobalExceptionHandler globalExceptionHandler;
    public ClientExceptionHandler(GlobalExceptionHandler globalExceptionHandler) {
        this.globalExceptionHandler = globalExceptionHandler;
        this.clientConverter = new CustomMappingJackson2HttpMessageConverter();
    }
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleCustomException(Exception ex, HttpServletRequest request) {
        CommonResult<?> commonResult = globalExceptionHandler.allExceptionHandler(request, ex);
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("level", ERROR_LEVEL);
        errorResponse.put("msg", commonResult.getMsg());
        return ResponseEntity.status(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(errorResponse);
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        // 使用自定义的 HttpMessageConverter 序列化返回值
        try {
            clientConverter.write(body, selectedContentType, response);
            return null; // 返回 null，表明已经手动处理了响应
        } catch (Exception e) {
            log.error("序列化失败", e);
            throw exception(SERIALIZE_FAIL);
        }
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;// 是否生效;当前环境已经指定了包范围所以默认生效。
    }
}
