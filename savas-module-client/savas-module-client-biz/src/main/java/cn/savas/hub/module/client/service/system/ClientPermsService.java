package cn.savas.hub.module.client.service.system;

import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.client.api.system.dto.ClientFuncPermsPageReqDTO;
import cn.savas.hub.module.client.api.system.dto.ClientFuncPermsReqDTO;
import cn.savas.hub.module.client.controller.desktop.system.vo.*;
import cn.savas.hub.module.client.dal.dataobject.ClientFuncPermsDO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/1 16:27
 */
public interface ClientPermsService {
    /**
     * 获取功能权限
     */
    ClientFuncPermsRespVO getFuncPermissions();

    /**
     * 新增功能权限
     * @param perms
     */
    void addFuncPermissions(ClientFuncPermsReqDTO perms);

    /**
     * 修改功能权限
     * @param perms
     */
    void updateFuncPermissions(ClientFuncPermsReqDTO perms);

    /**
     * 删除功能权限
     * @param id
     */
    void deleteFuncPermissions(Long id);

    /**
     * 获取用户功能权限（客户端）(根据用户ID)
     * @param loginUserId
     * @return
     */
    List<ClientFuncPermsDO> getClientUserFuncPerms(Long loginUserId);

    /**
     * 获取功能权限
     */
    List<ClientFuncPermsDO> getClientFuncPerms();

    /**
     * 获取客户端权限分页
     * @param pageReqVO
     * @return
     */
    PageResult<ClientFuncPermsDO> getFuncPermissionsPage(ClientFuncPermsPageReqDTO pageReqVO);


    /**
     * 获取和当前登录人有关的项目
     */
    Set<Long> getAuthorizedProjectIds();

    /**
     * 获取和当前项目有关的人
     */
    Set<Long> getAuthorizedPrjUserIds(Long projectId);

    /**
     * 当前登录人是否有项目数据权限
     */
    Boolean hasProjectDataPerms(Long projectId);
}
