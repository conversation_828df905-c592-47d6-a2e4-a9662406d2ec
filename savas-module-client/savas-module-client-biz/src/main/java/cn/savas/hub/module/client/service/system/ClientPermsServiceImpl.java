package cn.savas.hub.module.client.service.system;

import cn.savas.hub.framework.common.enums.CommonStatusEnum;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.framework.common.util.collection.CollectionUtils;
import cn.savas.hub.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils;
import cn.savas.hub.module.bpm.api.client.BpmClientApi;
import cn.savas.hub.module.client.api.system.dto.ClientFuncPermsPageReqDTO;
import cn.savas.hub.module.client.api.system.dto.ClientFuncPermsReqDTO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientFuncPermsRespVO;
import cn.savas.hub.module.client.convert.system.ClientPermsConvert;
import cn.savas.hub.module.client.dal.dataobject.*;
import cn.savas.hub.module.client.dal.mapper.*;
import cn.savas.hub.module.system.api.permission.PermissionApi;
import cn.savas.hub.module.system.api.permission.RoleApi;
import cn.savas.hub.module.system.api.permission.dto.RoleRespDTO;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * <AUTHOR>
 * @date 2024/11/1 16:28
 */
@Service
public class ClientPermsServiceImpl implements ClientPermsService {
    @Resource
    private ClientFuncPermsMapper clientFuncPermsMapper;
    @Resource
    private ClientFuncPermsRoleMapper clientFuncPermsRoleMapper;
    @Resource
    private PermissionApi permissionApi;
    @Resource
    private RoleApi roleService;
    @Resource
    private BpmClientApi bpmClientApi;
    @Resource
    private ClientProjectMapper clientProjectMapper;
    @Resource
    private ClientEngineeringMapper clientEngineeringMapper;
    @Resource
    private ClientDataPermsMapper clientDataPermsMapper;

    @Override
    public ClientFuncPermsRespVO getFuncPermissions() {
        ClientFuncPermsRespVO respVO = new ClientFuncPermsRespVO();
        Long loginUserId = getLoginUserId();
        List<ClientFuncPermsDO> funcPermsDOS = getClientUserFuncPerms(loginUserId);
        List<ClientFuncPermsRespVO.Items> items = ClientPermsConvert.INSTANCE.convertList2(funcPermsDOS);
        respVO.setItems(items);
        return respVO;
    }

    @Override
    public List<ClientFuncPermsDO> getClientUserFuncPerms(Long loginUserId) {
        // 获得角色列表
        Set<Long> roleIds = permissionApi.getUserRoleIdListByUserId(loginUserId);
        List<RoleRespDTO> roles = roleService.getRoleList(roleIds);
        if(roles == null || roles.isEmpty()){
            return new ArrayList<>(0);
        }
        roles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())); // 移除禁用的角色
        // 获得功能权限列表
        MPJLambdaWrapper<ClientFuncPermsRuleDO> wrapper = new MPJLambdaWrapper<ClientFuncPermsRuleDO>()
                .selectAll(ClientFuncPermsDO.class)
                .leftJoin(ClientFuncPermsDO.class, ClientFuncPermsDO::getId, ClientFuncPermsRuleDO::getFuncId)
                .in(ClientFuncPermsRuleDO::getRoleId, CollectionUtils.convertSet(roles, RoleRespDTO::getId));
        List<ClientFuncPermsDO> funcPermsDOS = clientFuncPermsRoleMapper.selectJoinList(ClientFuncPermsDO.class, wrapper);
        return funcPermsDOS;
    }

    @Override
    public List<ClientFuncPermsDO> getClientFuncPerms() {
        return clientFuncPermsMapper.selectList();
    }



    @Override
    public PageResult<ClientFuncPermsDO> getFuncPermissionsPage(ClientFuncPermsPageReqDTO pageReqVO) {
        return clientFuncPermsMapper.selectPage(pageReqVO, new LambdaQueryWrapperX<>());
    }


    @Override
    public void addFuncPermissions(ClientFuncPermsReqDTO perms) {
        ClientFuncPermsDO permsDO = ClientPermsConvert.INSTANCE.convert3(perms);
        clientFuncPermsMapper.insert(permsDO);
    }

    @Override
    public void updateFuncPermissions(ClientFuncPermsReqDTO perms) {
        clientFuncPermsMapper.updateById(ClientPermsConvert.INSTANCE.convert3(perms));
    }

    @Override
    public void deleteFuncPermissions(Long id) {
        clientFuncPermsMapper.deleteById(id);
    }

    @Override
    public Set<Long> getAuthorizedProjectIds() {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        Set<Long> projectIds = new HashSet<>();

        // 项目流程执行人相关的任务项目
        Set<Long> taskProjectIds = bpmClientApi.getTaskProjectIds(loginUserId);
        projectIds.addAll(taskProjectIds);

        // 负责人相关的项目
        projectIds.addAll(clientProjectMapper.selectByDirectorId(loginUserId)
                .stream()
                .map(ClientProjectDO::getProjectId)
                .collect(Collectors.toSet()));

        // 负责人相关的工程项目
        projectIds.addAll(clientEngineeringMapper.selectByDirectorId(loginUserId)
                .stream()
                .map(ClientEngineeringDO::getEngProjectId)
                .collect(Collectors.toSet()));

        // 数据读权限相关的项目
        projectIds.addAll(clientDataPermsMapper.selectByUserId(loginUserId)
                .stream()
                .map(ClientDataPermsDO::getProjectId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet()));
        return projectIds;
    }

    @Override
    public Set<Long> getAuthorizedPrjUserIds(Long projectId) {
        // 项目流程相关人
        Set<Long> taskUserIds = bpmClientApi.getFlowUserIdByPrjId(projectId);
        Set<Long> userIds = new HashSet<>(taskUserIds);

        // 获取项目负责人
        ClientProjectDO project = clientProjectMapper.selectById(projectId);
        if (project != null) {
            userIds.add(project.getDirectorId());
        }

        // 获取工程负责人
        List<ClientEngineeringDO> engineeringList = clientEngineeringMapper.selectByPrjId(projectId);
        for (ClientEngineeringDO engineering : engineeringList) {
            Long engDirectorId = engineering.getEngDirectorId();
            if (engDirectorId != null) {
                userIds.add(engDirectorId);
            }
        }

        // 获取数据权限用户
        List<ClientDataPermsDO> dataPermsList = clientDataPermsMapper.selectByProjectId(projectId);
        for (ClientDataPermsDO dataPerms : dataPermsList) {
            userIds.add(dataPerms.getUserId());
        }

        return userIds;
    }

    @Override
    public Boolean hasProjectDataPerms(Long projectId) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        return clientDataPermsMapper.selectCount(new LambdaQueryWrapperX<ClientDataPermsDO>()
                .eq(ClientDataPermsDO::getProjectId, projectId)
                .eq(ClientDataPermsDO::getUserId, loginUserId)) > 0;
    }
}
