package cn.savas.hub.module.client.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.savas.hub.module.client.controller.desktop.project.vo.ClientUpdateEngDataReqVO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/6 09:25
 */
@Mapper
public interface ClientProjectMapper extends BaseMapperX<ClientProjectDO> {
    default int deleteByPrjId(Long prjId){
        return delete(new LambdaQueryWrapperX<ClientProjectDO>().eq(ClientProjectDO::getProjectId, prjId));
    }

     default ClientProjectDO selectByPrjId(Long projectId){
         return selectOne(new LambdaQueryWrapperX<ClientProjectDO>().eq(ClientProjectDO::getProjectId, projectId));
     }

     default ClientProjectDO selectByOriginalId(Long originalId){
         return selectOne(new LambdaQueryWrapperX<ClientProjectDO>().eq(ClientProjectDO::getOriginalId, originalId));
     }

    default void bindDefinition(Long projectid, String processDefinitionId){
        update(new ClientProjectDO(),
                new LambdaUpdateWrapper<ClientProjectDO>()
                        .set(ClientProjectDO::getProcessDefinitionId, processDefinitionId)
                        .eq(ClientProjectDO::getProjectId, projectid)

        );
    }

    default List<ClientProjectDO> selectByDirectorId(Long loginUserId){
        return selectList(new LambdaQueryWrapperX<ClientProjectDO>()
                .eq(ClientProjectDO::getDirectorId, loginUserId)
        );
    }

    default void updateDirectorId(Long projectid, Long directorid, String directorname){
        update(null, new LambdaUpdateWrapper<ClientProjectDO>()
                .eq(ClientProjectDO::getProjectId, projectid)
                .set(ClientProjectDO::getDirectorId, directorid)
                .set(ClientProjectDO::getDirectorName, directorname)
        );
    }

    default List<ClientProjectDO> selectProjectListByIds(Set<Long> projectIds){
        return selectList(new LambdaQueryWrapper<ClientProjectDO>()
                .in(ClientProjectDO::getProjectId, projectIds)
                .orderByDesc(ClientProjectDO::getCreateTime)
        );
    }

    default void updateProject(ClientUpdateEngDataReqVO.UpdateProjectVO updateProjectVO){
        update(null, new LambdaUpdateWrapper<ClientProjectDO>()
                .eq(ClientProjectDO::getProjectId, updateProjectVO.getProjectid())
                .set(ClientProjectDO::getEdition, updateProjectVO.getEdition())
                .set(ClientProjectDO::getCompiledVersion, updateProjectVO.getCompiledversion())
                .set(ClientProjectDO::getSyncVersion, updateProjectVO.getSubmittedversion())
                .set(ClientProjectDO::getModelState, updateProjectVO.getModelstate())
        );

    }
}
