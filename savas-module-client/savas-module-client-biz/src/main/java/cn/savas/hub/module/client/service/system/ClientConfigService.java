package cn.savas.hub.module.client.service.system;

import cn.savas.hub.module.client.controller.desktop.project.vo.*;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientSettingRespVO;
import cn.savas.hub.module.client.enums.project.ProjectSettingEnum;
import cn.savas.hub.module.client.enums.system.CSocketMessageTypeEnum;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/28 17:48
 */
public interface ClientConfigService {
    /**
     * 保存项目设置
     * @return
     */
    Map<String, Object> saveProjectSetting(String product, ProjectSettingReqVO reqVO);

    Integer saveProjectSettingInTransaction(String product, ProjectSettingReqVO reqVO);

    /**
     * 获取项目设置版本
     * @param prjId
     * @return
     */
    ProjectSettingVersionRespVO getProjectSettingVersion(Long prjId, Long hostmodelid, ProjectSettingEnum settingEnum);

    /**
     * 获取(项目设置、费率设置、主材系数设置、装置费用降效设置)
     * @param prjId
     * @param projectSettingEnum
     * @return
     */
    ClientSettingRespVO getProjectSettingActive(Long prjId, Long hostmodelid, ProjectSettingEnum projectSettingEnum);

    /**
     * 保存字典文件
     * @param file
     * @return
     * @throws IOException
     */
    Boolean saveDictFile(MultipartFile file, Long uploadtime, String product, String compilemethod) throws IOException;

    void getDictFile(HttpServletResponse response, String product, String compileMethod) throws Exception;

    /**
     * 获取字典设置版本
     * @param product
     * @return
     */
    DictSettingVersionRespVO getDictSettingVersion(String product, String compileMethod);
}
