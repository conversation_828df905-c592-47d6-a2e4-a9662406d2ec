package cn.savas.hub.module.client.controller.desktop.flow.vo;

import cn.savas.hub.module.client.controller.desktop.flow.vo.resolver.ClientFlowDataBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 校审意见
 * <AUTHOR>
 * @date 2025/2/20 09:44
 */
@Data
public class ClientFlowSuggestVO extends ClientFlowDataBaseVO {
    @Schema(description = "针对的对象ID，对应的子目或分部的ID")
    private Long modelid;

    @Schema(description = "流程进度ID")
    private Long scheduleid;

    @Schema(description = "内容（相当于V8中的*校审意见*）")
    private String content;

    @Schema(description = "批注，回复内容（相当于V8中的*修改说明*）")
    private String comment;

    @Schema(description = "提交日期（相当于V8中的*校审时间*）")
    private String submittime;

    @Schema(description = "回复日期（相当于V8中的*修改时间*）")
    private String commenttime;

    @Schema(description = "创建人ID（相当于V8中的*校审人*）")
    private Long createuserid;

    @Schema(description = "创建人名称")
    private String createusername;

    @Schema(description = "创建时间")
    private String createtime;

    @Schema(description = "顺序号")
    private Integer sortid;
}
