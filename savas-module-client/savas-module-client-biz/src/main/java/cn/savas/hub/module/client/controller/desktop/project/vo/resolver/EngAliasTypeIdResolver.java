package cn.savas.hub.module.client.controller.desktop.project.vo.resolver;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.module.client.controller.desktop.project.vo.ClientUpdateEngDataReqVO;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.DatabindContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.jsontype.impl.TypeIdResolverBase;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:37
 */
public class EngAliasTypeIdResolver extends TypeIdResolverBase {
    private static final String ENG_PROJECT = ClientClassIdEnum.PROJECTENG.getCode().toString();
    @Override
    public String idFromValue(Object value) {
        if (value instanceof ClientUpdateEngDataReqVO.UpdateProjectVO) return ENG_PROJECT;
        return null;
    }

    @Override
    public String idFromValueAndType(Object value, Class<?> suggestedType) {
        return idFromValue(value);
    }

    @Override
    public JavaType typeFromId(DatabindContext context, String id) {
        if (ENG_PROJECT.equals(id)) {
            return context.constructType(ClientUpdateEngDataReqVO.UpdateProjectVO.class);
        }else {
            throw new IllegalArgumentException("Unknown alias: " + id);
        }
    }

    @Override
    public JsonTypeInfo.Id getMechanism() {
        return JsonTypeInfo.Id.CUSTOM;
    }
}
