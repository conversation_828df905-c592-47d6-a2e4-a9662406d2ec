package cn.savas.hub.module.client.service.price;

import cn.savas.hub.module.client.controller.desktop.price.vo.ClientPriceCustomRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13 09:44
 */
public interface ClientPriceCustomService {
    /**
     * 获取自定义标准价格库列表
     * @return
     */
    List<ClientPriceCustomRespVO> getStandardCustomPriceList();

    /**
     * 获取自定义电缆价格库列表
     * @return
     */
    List<ClientPriceCustomRespVO> getCableCustomPriceList();
}
