package cn.savas.hub.module.client.license.constants;

/**
 * 许可证相关常量定义
 * 统一管理所有魔法数字和字符串常量
 *
 * <AUTHOR>
 */
public final class LicenseConstants {

    private LicenseConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 日期格式常量
     */
    public static final class DateFormat {
        public static final String DEFAULT_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSXXX";
        public static final String ALT_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";

        private DateFormat() {}
    }

    /**
     * JSON 字段名常量
     */
    public static final class JsonFields {
        // 基本字段
        public static final String LICENSE_ID = "licenseid";
        public static final String HARDWARE_ID = "hardwareid";
        public static final String COMPANY_NAME = "companyname";
        public static final String REMARKS = "remarks";
        public static final String SIGNATURE = "signature";
        public static final String ISSUE_DATE = "issuedate";
        public static final String EXPIRY_DATE = "expirydate";
        public static final String PRODUCTS = "products";
        public static final String QUOTA_LIBRARIES = "quotalibraries";

        // 产品字段
        public static final String PRODUCT_ID = "id";
        public static final String PRODUCT_NAME = "name";
        public static final String PRODUCT_POINTS = "points";

        // 定额库字段
        public static final String QUOTA_ID = "id";
        public static final String QUOTA_NAME = "name";
        public static final String QUOTA_VERSION = "version";

        private JsonFields() {}
    }

    /**
     * 特殊值常量
     */
    public static final class SpecialValues {
        public static final String NULL_VALUE = "null";
        public static final String EMPTY_STRING = "";
        public static final int DEFAULT_POINTS = 0;

        private SpecialValues() {}
    }

    /**
     * 缓存相关常量
     */
    public static final class Cache {
        public static final String HARDWARE_ID_CACHE = "hardwareId";
        public static final String LICENSE_CONTENT_CACHE = "license-content";
        public static final String LICENSE_VALIDATION_CACHE = "license-validation";
        public static final String LICENSE_INFO_CACHE = "licenseInfo";

        // 缓存时间（秒）
        public static final long DEFAULT_HARDWARE_ID_CACHE_TIME = 3600L;
        public static final long DEFAULT_LICENSE_VALIDATION_CACHE_TIME = 300L;

        private Cache() {}
    }

    /**
     * 加密相关常量
     */
    public static final class Crypto {
        public static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
        public static final String RSA_KEY_ALGORITHM = "RSA";
        public static final String HASH_ALGORITHM = "MD5";

        private Crypto() {}
    }

    /**
     * 文件相关常量
     */
    public static final class Files {
        public static final String DEFAULT_LICENSE_FILE_NAME = "License.json";
        public static final String LICENSE_FILE_EXTENSION = ".json";

        private Files() {}
    }

    /**
     * 硬件信息相关常量
     */
    public static final class Hardware {
        // Windows 命令
        public static final String WINDOWS_MOTHERBOARD_CMD = "wmic baseboard get serialnumber";
        public static final String WINDOWS_CPU_CMD = "wmic cpu get processorid";
        public static final String WINDOWS_DISK_CMD = "wmic diskdrive get serialnumber";

        // Linux 命令
        public static final String LINUX_MOTHERBOARD_CMD = "sudo dmidecode -s baseboard-serial-number";
        public static final String LINUX_CPU_CMD = "cat /proc/cpuinfo | grep 'processor' | wc -l";
        public static final String LINUX_DISK_CMD = "lsblk -o NAME,SERIAL";

        // macOS 命令
        public static final String MACOS_MOTHERBOARD_CMD = "system_profiler SPHardwareDataType | grep 'Serial Number'";
        public static final String MACOS_CPU_CMD = "sysctl -n machdep.cpu.brand_string";
        public static final String MACOS_DISK_CMD = "diskutil list";

        private Hardware() {}
    }

    /**
     * 验证相关常量
     */
    public static final class Validation {
        public static final int MAX_LICENSE_FILE_SIZE = 10 * 1024 * 1024; // 10MB
        public static final int MIN_HARDWARE_ID_LENGTH = 8;
        public static final int MAX_COMPANY_NAME_LENGTH = 200;
        public static final int MAX_REMARKS_LENGTH = 500;

        private Validation() {}
    }
}
