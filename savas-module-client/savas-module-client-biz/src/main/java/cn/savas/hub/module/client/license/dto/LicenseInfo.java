package cn.savas.hub.module.client.license.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 许可证信息数据传输对象
 * 用于在应用程序中传递许可证信息
 */
@Data
public class LicenseInfo {

    private String licenseId;
    private String companyName;
    private String hardwareId;
    private LocalDateTime issueDate;
    private LocalDateTime expiryDate;
    private String remarks;
    private List<LicenseProduct> products;
    private List<LicenseQuotaLibrary> quotaLibraries;
    private boolean valid;
    private LocalDateTime lastValidationTime;

    public LicenseInfo() {
    }

    /**
     * 检查许可证是否过期
     */
    public boolean isExpired() {
        return expiryDate != null && LocalDateTime.now().isAfter(expiryDate);
    }

    /**
     * 检查是否有指定产品的授权
     */
    public boolean hasProduct(String productId) {
        return products != null && products.stream()
                .anyMatch(product -> productId.equals(product.getId()));
    }

    /**
     * 获取指定产品的点数
     */
    public int getProductPoints(String productId) {
        return products != null ? products.stream()
                .filter(product -> productId.equals(product.getId()))
                .mapToInt(LicenseProduct::getPoints)
                .findFirst()
                .orElse(0) : 0;
    }

    /**
     * 检查是否有指定定额库的授权
     */
    public boolean hasQuotaLibrary(String quotaLibraryId) {
        return quotaLibraries != null && quotaLibraries.stream()
                .anyMatch(quota -> quotaLibraryId.equals(quota.getId()));
    }

    /**
     * 获取指定定额库的版本
     */
    public String getQuotaLibraryVersion(String quotaLibraryId) {
        return quotaLibraries != null ? quotaLibraries.stream()
                .filter(quota -> quotaLibraryId.equals(quota.getId()))
                .map(LicenseQuotaLibrary::getVersion)
                .findFirst()
                .orElse(null) : null;
    }

    /**
     * 获取剩余有效天数
     */
    public long getRemainingDays() {
        if (expiryDate == null) {
            return Long.MAX_VALUE; // 永不过期
        }
        return java.time.temporal.ChronoUnit.DAYS.between(LocalDateTime.now(), expiryDate);
    }
}
