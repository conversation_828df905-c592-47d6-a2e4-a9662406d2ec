package cn.savas.hub.module.client.license.crypto;

import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * RSA密钥解析器
 */
public class RSAKeyParser {

    /**
     * 从C#的XML格式公钥中解析出Java的PublicKey对象
     *
     * @param xmlPublicKey .NET格式的XML公钥字符串
     * @return Java的PublicKey对象
     * @throws Exception 解析失败时抛出异常
     */
    public static PublicKey parseXmlPublicKey(String xmlPublicKey) throws Exception {
        // 提取Modulus
        String modulus = extractXmlElement(xmlPublicKey, "Modulus");
        if (modulus == null) {
            throw new IllegalArgumentException("无法从XML中提取Modulus");
        }

        // 提取Exponent
        String exponent = extractXmlElement(xmlPublicKey, "Exponent");
        if (exponent == null) {
            throw new IllegalArgumentException("无法从XML中提取Exponent");
        }

        // Base64解码
        byte[] modulusBytes = Base64.getDecoder().decode(modulus);
        byte[] exponentBytes = Base64.getDecoder().decode(exponent);

        // 转换为BigInteger
        BigInteger modulusBigInt = new BigInteger(1, modulusBytes);
        BigInteger exponentBigInt = new BigInteger(1, exponentBytes);

        // 创建RSA公钥规范
        RSAPublicKeySpec keySpec = new RSAPublicKeySpec(modulusBigInt, exponentBigInt);

        // 生成PublicKey
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");

        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 从XML字符串中提取指定元素的值
     *
     * @param xml XML字符串
     * @param elementName 元素名称
     * @return 元素值，如果未找到则返回null
     */
    private static String extractXmlElement(String xml, String elementName) {
        Pattern pattern = Pattern.compile("<" + elementName + ">(.*?)</" + elementName + ">", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(xml);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }

    /**
     * 硬编码的公钥信息
     * 注意：这里需要替换为实际的公钥值
     */
    public static class HardcodedKeys {
        public static final String PUBLIC_KEY_XML =
            "<RSAKeyValue>" +
            "<Modulus>0no7+wJlLC9c7CODlnBdI2sOwJ8SHyTj2Dv4kQmZFHEEHIYRF3t/7IQ9gajBSbtOeKTg9VUQxpf3op3wd55+NA6Vfls1oygVaULwFYUSUblfDxi5c5NtzCXT0m8yOjGQLRsktIki2rXvpXj5+1aLyci6ASjtYJ3ShgcNsjEYyWrzdgDXKkY5ETis2c7SaF8WijT2lLSdjNNmb/Ylmuhz4iZI1orlzqe6vAUiJCeHbu0t8LroPNovVIe3wMHOyUZ5vNPmmfus29bkhtdC/0+olX9JFhMasVJ4eOxzxI8zM0amkwsXywjsVGQoR316zGQn00Ap04yyh4fLaFDxSLlhIQ==</Modulus>" +
            "<Exponent>AQAB</Exponent>" +
            "</RSAKeyValue>";

        // 备用方案：直接使用Base64编码的模数和指数
        public static final String MODULUS_BASE64 = "YOUR_MODULUS_BASE64_HERE";
        public static final String EXPONENT_BASE64 = "YOUR_EXPONENT_BASE64_HERE";

        /**
         * 获取硬编码的公钥
         */
        public static PublicKey getPublicKey() throws Exception {
            return parseXmlPublicKey(PUBLIC_KEY_XML);
        }

        /**
         * 使用备用方案获取公钥
         */
        public static PublicKey getPublicKeyFromBase64() throws Exception {
            byte[] modulusBytes = Base64.getDecoder().decode(MODULUS_BASE64);
            byte[] exponentBytes = Base64.getDecoder().decode(EXPONENT_BASE64);

            BigInteger modulus = new BigInteger(1, modulusBytes);
            BigInteger exponent = new BigInteger(1, exponentBytes);

            RSAPublicKeySpec keySpec = new RSAPublicKeySpec(modulus, exponent);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(keySpec);
        }
    }
}
