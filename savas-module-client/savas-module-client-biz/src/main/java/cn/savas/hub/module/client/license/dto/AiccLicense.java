package cn.savas.hub.module.client.license.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * AICC许可证模型
 */
@Data
public class AiccLicense {
    private String licenseId;
    private List<LicenseProduct> products;
    private List<LicenseQuotaLibrary> quotaLibraries;
    private String hardwareId;
    private String companyName;
    private LocalDateTime issueDate;
    private LocalDateTime expiryDate;
    private String remarks;
    private String signature;

    public AiccLicense() {
        this.products = new ArrayList<>();
        this.quotaLibraries = new ArrayList<>();
    }

    /**
     * 获取用于签名验证的数据字符串
     * 重要：必须与C#端的GetSignatureData()方法保持一致
     */
    public String getSignatureData() {
        StringBuilder sb = new StringBuilder();
        sb.append("{");

        // LicenseId
        sb.append("\"licenseid\":\"").append(licenseId != null ? licenseId : "").append("\",");

        // Products
        sb.append("\"products\":[");
        for (int i = 0; i < products.size(); i++) {
            if (i > 0) sb.append(",");
            LicenseProduct product = products.get(i);
            sb.append("{\"id\":").append(product.getId()).append(",")
              .append("\"name\":\"").append(product.getName()).append("\",")
              .append("\"points\":").append(product.getPoints()).append("}");
        }
        sb.append("],");

        // QuotaLibraries
        sb.append("\"quotalibraries\":[");
        for (int i = 0; i < quotaLibraries.size(); i++) {
            if (i > 0) sb.append(",");
            LicenseQuotaLibrary quota = quotaLibraries.get(i);
            sb.append("{\"id\":").append(quota.getId()).append(",")
              .append("\"name\":\"").append(quota.getName()).append("\",")
              .append("\"version\":\"").append(quota.getVersion()).append("\"}");
        }
        sb.append("],");

        // HardwareId
        sb.append("\"hardwareid\":\"").append(hardwareId != null ? hardwareId : "").append("\",");

        // CompanyName
        sb.append("\"companyname\":\"").append(companyName != null ? companyName : "").append("\",");

        // IssueDate
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        sb.append("\"issuedate\":\"").append(issueDate != null ? issueDate.format(formatter) : "").append("\",");

        // ExpiryDate
        sb.append("\"expirydate\":").append(expiryDate != null ? "\"" + expiryDate.format(formatter) + "\"" : "null");

        sb.append("}");
        return sb.toString();
    }

    /**
     * 检查许可证是否过期
     */
    public boolean isExpired() {
        return expiryDate != null && LocalDateTime.now().isAfter(expiryDate);
    }
}
