package cn.savas.hub.module.client.service.flow;

import cn.savas.hub.framework.common.client.enmus.ClientOperateEnum;
import cn.savas.hub.framework.common.util.collection.TreeDataUtil;
import cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils;
import cn.savas.hub.module.bpm.api.client.BpmClientApi;
import cn.savas.hub.module.bpm.api.client.dto.ClientFlowDataDTO;
import cn.savas.hub.module.bpm.api.client.dto.ClientFlowNodeDTO;
import cn.savas.hub.module.bpm.api.client.dto.ClientFlowTemplateDTO;
import cn.savas.hub.module.client.controller.desktop.flow.vo.*;
import cn.savas.hub.module.client.controller.desktop.flow.vo.resolver.ClientFlowDataBaseVO;
import cn.savas.hub.module.client.controller.desktop.project.vo.ClientMergeProjectFileReqVO;
import cn.savas.hub.module.client.convert.flow.BpmClientFlowConvert;
import cn.savas.hub.module.client.convert.flow.FlowTemplateConvert;
import cn.savas.hub.module.client.dal.dataobject.ClientEngineeringDO;
import cn.savas.hub.module.client.dal.mapper.ClientEngineeringMapper;
import cn.savas.hub.module.client.dal.redis.RedisKeyConstants;
import cn.savas.hub.module.client.service.project.ClientProjectService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.client.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2024/10/22 17:07
 */
@Service
public class ClientFlowServiceImpl implements ClientFlowService {
    @Resource
    private BpmClientApi bpmClientApi;
    @Resource
    private ClientProjectService clientProjectService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private ClientEngineeringMapper clientEngineeringMapper;


    @Override
    public void saveFlowData(ClientFlowDataReqVO reqVO) {
        List<ClientFlowDataBaseVO> items = reqVO.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        ClientFlowDataDTO flowDataDTO = new ClientFlowDataDTO();
        BpmClientFlowConvert converter = BpmClientFlowConvert.INSTANCE;
        // 处理所有数据项
        items.forEach(item -> {
            ClientOperateEnum operate = ClientOperateEnum.getByCode(item.getOperate());
            ClientFlowDataDTO.FlowDataContainer container = flowDataDTO.getFlowData().get(operate);
            // 根据类型将数据添加到对应的容器中
            if (item instanceof ClientFlowDataNodeVO) {
                container.getNodes().add(converter.convert1((ClientFlowDataNodeVO) item));
            } else if (item instanceof ClientFlowDataNodeDirVO) {
                container.getDirections().add(converter.convert3((ClientFlowDataNodeDirVO) item));
            } else if (item instanceof ClientFlowDataScheduleVO) {
                container.getSchedules().add(converter.convert2((ClientFlowDataScheduleVO) item));
            }else if(item instanceof ClientFlowSuggestVO){
                container.getSuggestions().add(converter.convert6((ClientFlowSuggestVO) item));
            }
        });
        // 保存流程数据
        bpmClientApi.saveFlowData(flowDataDTO);
    }

    @Override
    public List<FlowNodeVO> getFlowData(Long originalid) {
        ClientFlowDataDTO.FlowDataContainer flowData = bpmClientApi.getFlowData(originalid);
        List<FlowNodeVO> flowNodes = BpmClientFlowConvert.INSTANCE.convertList4(flowData.getNodes());
        List<FlowNodeDirectionVO> flowNodeDirs = BpmClientFlowConvert.INSTANCE.convertList5(flowData.getDirections());
        // 合并 来源 和 目标 的direction 数据
        Map<Long, List<FlowNodeDirectionVO>> directionMap = new LinkedHashMap<>();
        for (FlowNodeDirectionVO direction : flowNodeDirs) {
            directionMap.computeIfAbsent(direction.getSourceflownodeid(), k -> new ArrayList<>()).add(direction);
        }
        // 为每个 flowNode 设置对应的 directions
        for (FlowNodeVO flowNode : flowNodes) {
            List<FlowNodeDirectionVO> directionsList = directionMap.getOrDefault(flowNode.getId(), new ArrayList<>());
            flowNode.setDirections(new FlowNodeVO.Items(directionsList));
        }
        return flowNodes;
    }

    @Override
    public List<ClientFlowTemplateRespVO> getFlowTemplate() {
        // 1. 获取基础数据
        ClientFlowTemplateDTO flowTemplate = bpmClientApi.getFlowTemplate();
        FlowTemplateConvert converter = FlowTemplateConvert.INSTANCE;
        // 2. 转换基础数据
        List<ClientFlowTemplateRespVO> templates = converter.convertList1(flowTemplate.getFlowTemplateList());
        List<TempNodeVO> nodes = converter.convertList2(flowTemplate.getFlowNodeList());
        List<TempNodeDirectionVO> directions = converter.convertList3(flowTemplate.getFlowDirectionList());
        // 3. 构建方向映射
        // 先构建 id -> index 的顺序映射
        Map<Long, Integer> nodeOrderMap = new HashMap<>();
        for (int i = 0; i < nodes.size(); i++) {
            nodeOrderMap.put(nodes.get(i).getId(), i);
        }
        // 构建方向映射并排序
        Map<Long, List<TempNodeDirectionVO>> directionMap = directions.stream()
                .collect(Collectors.groupingBy(
                        TempNodeDirectionVO::getSourceflownodeid,
                        Collectors.collectingAndThen(Collectors.toList(), list -> {
                            list.sort(Comparator.comparingInt(
                                    d -> nodeOrderMap.getOrDefault(d.getTargetflownodeid(), Integer.MAX_VALUE)
                            ));
                            return list;
                        })
                ));

        // 4. 构建节点映射，按模板ID分组
        Map<Long, List<TempNodeVO>> nodesByTemplateId = nodes.stream()
                .collect(Collectors.groupingBy(TempNodeVO::getHostmodelid));

        // 5. 处理每个模板
        templates.forEach(template -> processTemplate(template, nodesByTemplateId, directionMap));
        return templates;
    }

    @Override
    public List<FlowScheduleRespVO> getFlowSchedule(Long originalid) {
        ClientFlowDataDTO.FlowDataContainer flowSchedule = bpmClientApi.getFlowData(originalid);
        return BpmClientFlowConvert.INSTANCE.convertList8(flowSchedule.getSchedules());
    }

    @Override
    public List<FlowSuggestionRespVO> getFlowSuggestion(Long originalid) {
        ArrayList<FlowSuggestionRespVO> respVOS = new ArrayList<>();
        ClientFlowDataDTO.FlowDataContainer flowData = bpmClientApi.getFlowData(originalid);
        // 进度信息
        Map<Long, ClientFlowDataDTO.FlowSchedule> scheduleMap = flowData.getSchedules().stream().collect(Collectors.toMap(ClientFlowDataDTO.FlowSchedule::getSchId, Function.identity()));
        // 节点信息
        Map<Long, ClientFlowDataDTO.FlowNode> nodeMap = flowData.getNodes().stream().collect(Collectors.toMap(ClientFlowDataDTO.FlowNode::getNodeId, Function.identity()));
        BpmClientFlowConvert instance = BpmClientFlowConvert.INSTANCE;
        for (ClientFlowDataDTO.FlowSuggestion suggestion : flowData.getSuggestions()) {
            FlowSuggestionRespVO respVO = instance.convert7(suggestion);
            ClientFlowDataDTO.FlowSchedule flowSchedule = scheduleMap.get(suggestion.getSugSchedule());
            // 设置节点名称
            if (flowSchedule != null) {
                ClientFlowDataDTO.FlowNode sourceNode = nodeMap.get(flowSchedule.getSchSource());
                if (sourceNode != null) {
                    respVO.setFlownodename(sourceNode.getNodeName());
                }
                ClientFlowDataDTO.FlowNode targetNode = nodeMap.get(flowSchedule.getSchTarget());
                if (targetNode != null) {
                    respVO.setTargetflownodename(targetNode.getNodeName());
                }
            }
            respVOS.add(respVO);
        }
        return respVOS;
    }

    @Override
    public void lockFlow(ClientFlowLockReqVO reqVO) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        List<Long> hostmodelids = new ArrayList<>();
        // 判断流程节点负责人，只做判断不加锁
        if (!CollectionUtils.isEmpty(reqVO.getFlownodeids())) {
            List<ClientFlowNodeDTO> flowNodes = bpmClientApi.getFlowNodeById(reqVO.getFlownodeids());
            // 根据pid分一下组
            Map<Long, List<ClientFlowNodeDTO>> flowNodeMap = flowNodes.stream()
                    .collect(Collectors.groupingBy(ClientFlowNodeDTO::getNodePid));
            // 同一分组节点内有一个负责人是当前用户，则跳过
            for (Map.Entry<Long, List<ClientFlowNodeDTO>> entry : flowNodeMap.entrySet()) {
                List<ClientFlowNodeDTO> nodeList = entry.getValue();
                boolean hasPermission = nodeList.stream().anyMatch(node -> loginUserId.equals(node.getNodeUserId()));
                if (!hasPermission) {
                    throw exception(PROJECT_NO_PERMISSION_LOCK_FLOW);
                }
            }
        }else if (!CollectionUtils.isEmpty(reqVO.getEdithostmodelids())) {
            List<ClientEngineeringDO> engineeringDOList = clientProjectService.getEngByIds(reqVO.getProjectid(), reqVO.getEdithostmodelids());
            engineeringDOList.forEach(engineeringDO -> {
                if (!loginUserId.equals(engineeringDO.getEngDirectorId())) {
                    throw exception(PROJECT_NO_PERMISSION_LOCK_FLOW);
                }
            });
            hostmodelids = reqVO.getEdithostmodelids();
        }else if (!CollectionUtils.isEmpty(reqVO.getDelhostmodelids())) {
            // 如果是删除工程结构
            List<ClientEngineeringDO> engList = clientEngineeringMapper.selectByPrjIdTree(reqVO.getProjectid());
            List<ClientEngineeringDO> engTreeList = TreeDataUtil.baseTree(engList, true, ClientEngineeringDO::getEngId, ClientEngineeringDO::getEngPid, ClientEngineeringDO::setChildren, ClientEngineeringDO::getChildren);
            Set<Long> engIdSet = new HashSet<>(reqVO.getDelhostmodelids());
            // 遍历树列表，递归检查权限
            for (ClientEngineeringDO node : engTreeList) {
                checkNodePermission(node, engIdSet, loginUserId);
            }
            // 如果engIdSet不为空，说明有ID未被检查或无权限
            if (!engIdSet.isEmpty()) {
                throw exception(PROJECT_NO_PERMISSION_LOCK_FLOW);
            }
            hostmodelids = reqVO.getDelhostmodelids();
        }else {
            throw exception(PROJECT_FLOW_LOCK_HOSTMODELIDS_EMPTY);
        }


        // 判断是否有流程锁
        hostmodelids.forEach(hostmodelid -> {
            String key = RedisKeyConstants.CLIENT_LOCK_FLOW + ":" + reqVO.getProjectid() + ":" + hostmodelid;
            String value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                throw exception(PROJECT_FLOW_LOCKED, hostmodelid);
            }
        });
        // 锁定流程
        hostmodelids.forEach(hostmodelid -> {
            String key = RedisKeyConstants.CLIENT_LOCK_FLOW + ":" + reqVO.getProjectid() + ":" + hostmodelid;
            redisTemplate.opsForValue().set(key, String.valueOf(loginUserId));
        });
    }

    /**
     * 递归检查节点权限
     * @param node
     * @param engIdSet
     * @param loginUserId
     */
    private void checkNodePermission(ClientEngineeringDO node, Set<Long> engIdSet, Long loginUserId) {
        // 检查engDirectorId是否等于loginUserId
        if (node.getEngDirectorId().equals(loginUserId)) {
            // 当前节点有权限，移除当前engId及其所有子节点的engId
            engIdSet.remove(node.getEngId());
            removeChildIds(node, engIdSet);
            return;
        }
        // 当前节点无权限，不抛异常，继续检查子节点

        // 递归检查子节点
        if (node.getChildren() != null) {
            for (ClientEngineeringDO child : node.getChildren()) {
                checkNodePermission(child, engIdSet, loginUserId);
            }
        }
    }

    // 新增方法：递归移除所有子节点的engId
    private void removeChildIds(ClientEngineeringDO node, Set<Long> engIdSet) {
        if (node.getChildren() != null) {
            for (ClientEngineeringDO child : node.getChildren()) {
                engIdSet.remove(child.getEngId()); // 移除子节点的engId
                removeChildIds(child, engIdSet); // 递归移除更低层级的子节点
            }
        }
    }

    @Override
    public void unlockFlow(ClientFlowUnLockReqVO reqVO) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        // 解锁流程
        reqVO.getHostmodelids().forEach(hostmodelid -> {
            String key = RedisKeyConstants.CLIENT_LOCK_FLOW + ":" + reqVO.getProjectid() + ":" + hostmodelid;
            String value = redisTemplate.opsForValue().get(key);
            if (value == null) {
                return;
            }
            if (!value.equals(String.valueOf(loginUserId))) {
                throw exception(PROJECT_NO_PERMISSION_UNLOCK_FLOW);
            }
            redisTemplate.delete(key);
        });
    }

    @Override
    public void delFlowLock(Long projectId) {
        // 删除流程锁
        List<ClientEngineeringDO> engineeringDOList = clientEngineeringMapper.selectByPrjId(projectId);
        List<Long> engIds = engineeringDOList.stream().map(ClientEngineeringDO::getEngId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(engIds)) {
            return;
        }
        // 删除工程级锁
        engIds.forEach(engId -> {
            String key = RedisKeyConstants.CLIENT_LOCK_FLOW + ":" + projectId + ":" + engId;
            redisTemplate.delete(key);
        });
        // 删除项目级锁
        String projectKey = RedisKeyConstants.CLIENT_LOCK_FLOW + ":" + projectId + ":" + projectId;
        redisTemplate.delete(projectKey);
    }

    @Override
    public void forceMergeFlow(ClientFlowForceMergeReqVO reqVO, String product) {
        List<ClientFlowForceMergeReqVO.ForceMergeReqVOItem> items = reqVO.getItems();
        Long projectid = reqVO.getProjectid();
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        // 判断流程锁
        List<Long> engidlist = items.stream().map(ClientFlowForceMergeReqVO.ForceMergeReqVOItem::getEngid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(engidlist)) {
            throw exception(PROJECT_FLOW_FORCE_MERGE_ENGID_EMPTY);
        }
        for (Long engId : engidlist) {
            String key = RedisKeyConstants.CLIENT_LOCK_FLOW + ":" + projectid + ":" + engId;
            String value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                throw exception(PROJECT_FLOW_LOCKED, engId);
            }
        }
        // 根据用户ID分组
        Map<Long, List<Long>> userGroupMap = items.stream().collect(
                Collectors.groupingBy(ClientFlowForceMergeReqVO.ForceMergeReqVOItem::getUserid, Collectors.mapping(ClientFlowForceMergeReqVO.ForceMergeReqVOItem::getEngid, Collectors.toList()))
        );
        // key:用户ID，value:工程ID列表
        for (Map.Entry<Long, List<Long>> entry : userGroupMap.entrySet()) {
            // 合并
            ClientMergeProjectFileReqVO mergeVO = new ClientMergeProjectFileReqVO();
            mergeVO.setProjectid(projectid);
            mergeVO.setHostmodelids(entry.getValue());
            mergeVO.setUserid(entry.getKey());
            mergeVO.setIncreaseEngVersion(true);
            clientProjectService.mergeProjectFile(mergeVO, product);
        }
    }

    @Override
    public void bindSumFileVersion(ClientFlowBindSumFileVersionReqVO reqVO) {
        bpmClientApi.bindSumFileVersion(reqVO.getFlowscheduleid(), reqVO.getSumfileversion());
    }


    /**
     * 流程模板处理方法
     * @param template
     * @param nodesByTemplateId
     * @param directionMap
     */
    private void processTemplate(ClientFlowTemplateRespVO template,
                                 Map<Long, List<TempNodeVO>> nodesByTemplateId,
                                 Map<Long, List<TempNodeDirectionVO>> directionMap) {
        List<TempNodeVO> templateNodes = nodesByTemplateId.getOrDefault(template.getId(), Collections.emptyList());

        List<TempNodeVO> rootNodes = new ArrayList<>();
        template.setItems(rootNodes);

        if (templateNodes.isEmpty()) {
            return;
        }

        // 构建节点ID映射
        Map<Long, TempNodeVO> nodeMap = templateNodes.stream()
                .collect(Collectors.toMap(TempNodeVO::getId, Function.identity()));


        // 处理每个节点
        templateNodes.forEach(node -> {
            // 设置节点方向
            setNodeDirections(node, directionMap);

            // 处理节点层级关系
            if (!processNodeHierarchy(node, nodeMap)) {
                rootNodes.add(node);
            }
        });


    }

    private void setNodeDirections(TempNodeVO node, Map<Long, List<TempNodeDirectionVO>> directionMap) {
        List<TempNodeDirectionVO> nodeDirections = directionMap.getOrDefault(node.getId(), Collections.emptyList());
        TempNodeVO.Items directions = new TempNodeVO.Items();
        directions.setItems(nodeDirections);
        node.setDirections(directions);
    }

    private boolean processNodeHierarchy(TempNodeVO node, Map<Long, TempNodeVO> nodeMap) {
        TempNodeVO parent = nodeMap.get(node.getParentid());
        if (parent == null) {
            return false;
        }
        parent.getItems().add(node);
        return true;
    }
}
