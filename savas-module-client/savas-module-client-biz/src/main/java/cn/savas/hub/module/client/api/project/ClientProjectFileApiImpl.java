package cn.savas.hub.module.client.api.project;

import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.savas.hub.framework.common.client.util.V9PackageUtil;
import cn.savas.hub.framework.common.client.util.VersionInfo;
import cn.savas.hub.framework.mybatis.core.util.SQLiteDataSourceUtil;
import cn.savas.hub.framework.web.core.util.WebFrameworkUtils;
import cn.savas.hub.module.client.api.project.dto.ClientProjectFileRespDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectFileSRespDTO;
import cn.savas.hub.module.client.convert.project.ClientProjectConvert;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectDO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectFileDO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectFileSummaryDO;
import cn.savas.hub.module.client.dal.mapper.ClientEngineeringMapper;
import cn.savas.hub.module.client.dal.mapper.ClientProjectFileMapper;
import cn.savas.hub.module.client.dal.mapper.ClientProjectFileSummaryMapper;
import cn.savas.hub.module.client.dal.mapper.ClientProjectMapper;
import cn.savas.hub.module.client.dal.redis.RedisKeyConstants;
import cn.savas.hub.module.client.enums.project.ProjectSumFileSourceEnum;
import cn.savas.hub.module.collect.api.CollectDataApi;
import cn.savas.hub.module.collect.api.dto.PrjEngineeringDTO;
import cn.savas.hub.module.collect.api.dto.PrjProjectEngDTO;
import cn.savas.hub.module.infra.api.file.FileApi;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.client.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2024/11/19 11:49
 */
@Slf4j
@Service
public class ClientProjectFileApiImpl implements ClientProjectFileApi {
    public static final String DB_STORAGE_PATH = FileUtil.getUserHomePath() + "/savas-hub/sqlite_dbs/";
    private final ConcurrentHashMap<Long, Object> lockMap = new ConcurrentHashMap<>();

    @Resource
    private ClientProjectFileMapper clientProjectFileMapper;
    @Resource
    private ClientProjectFileSummaryMapper clientProjectFileSummaryMapper;
    @Resource
    private CollectDataApi collectDataApi;
    @Resource
    private FileApi fileApi;
    @Resource
    private ClientEngineeringMapper clientEngineeringMapper;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private ClientProjectMapper clientProjectMapper;
    @Resource
    private SQLiteDataSourceUtil sqliteDataSourceUtil;
    private static final long PROJECT_CACHE_EXPIRE = 24;
    private static class ResultHolder {
        List<PrjEngineeringDTO> engineeringList;
        PrjProjectEngDTO projectEng;
    }
    @Override
    public ClientProjectFileRespDTO getClientProjectFile(Long projectId) {
        ClientProjectFileDO fileDO = clientProjectFileMapper.selectActiveByPrjId(projectId, WebFrameworkUtils.getLoginUserId());
        return ClientProjectConvert.INSTANCE.convert5(fileDO);
    }

    @Override
    public ClientProjectFileSRespDTO getClientProjectSummaryFile(Long projectId) {
        ClientProjectFileSummaryDO fileDO = clientProjectFileSummaryMapper.selectActiveByPrjId(projectId);
        return ClientProjectConvert.INSTANCE.convert6(fileDO);
    }

    @Override
    public ClientProjectFileRespDTO getLatestProjectFile(Long projectId) {
        ClientProjectFileDO prjId = clientProjectFileMapper.selectActiveByPrjId(projectId, WebFrameworkUtils.getLoginUserId());
        return ClientProjectConvert.INSTANCE.convert5(prjId);
    }

    @Override
    public ClientProjectFileSRespDTO getLatestCollectProjectFile(Long projectId) {
        ClientProjectFileSummaryDO prjId = clientProjectFileSummaryMapper.selectActiveByPrjId(projectId);
        return ClientProjectConvert.INSTANCE.convert6(prjId);
    }

    @Override
    public <T> T changePrjFileDs(Long projectId, Supplier<T> supplier) throws Exception {
        ClientProjectFileRespDTO file = getClientProjectFile(projectId);
        if (file != null) {
            byte[] fileContent = fileApi.getFileContent(file.getPath());
            String sqliteUrl = V9PackageUtil.unZipFileToDB(fileContent);
            return sqliteDataSourceUtil.executeWithSQLite(sqliteUrl, supplier);
        }
        return null;
    }

    @Override
    public <T> T changePrjFileSumDs(Long projectId, Supplier<T> supplier) {
       return changePrjFileSumDs(projectId, supplier, false, null);
    }

    @Override
    public <T> T changePrjFileSumDs(Long projectId, Supplier<T> supplier, boolean isWriteBack, ProjectSumFileSourceEnum source) {
        return changePrjFileSumDsBase(projectId, supplier, isWriteBack, null, source);
    }

    @Override
    public <T> T changePrjFileSumDs(Long projectId, Supplier<T> supplier, boolean isWriteBack, String version, ProjectSumFileSourceEnum source) {
        return changePrjFileSumDsBase(projectId, supplier, isWriteBack, version, source);
    }
    private <T> T changePrjFileSumDsBase(Long projectId, Supplier<T> supplier, boolean isWriteBack, String version, ProjectSumFileSourceEnum source) {
        ClientProjectFileSRespDTO file = getClientProjectSummaryFile(projectId);
        if (file == null) {
            log.warn("[changePrjFileSumDs] 未找到项目文件, projectId: {}", projectId);
            throw exception(PROJECT_SUMMARY_FILE_NOT_EXISTS, projectId);
        }
        byte[] fileContent;
        try {
            fileContent = fileApi.getFileContent(file.getPath());
        } catch (Exception e) {
            throw exception(PROJECT_FILE_CONTENT_FAIL, projectId);
        }
        String sqliteUrl = getProjectSqliteUrl(projectId, fileContent);
        T execute = sqliteDataSourceUtil.executeWithSQLite(sqliteUrl, supplier);
        if (isWriteBack) {
            VersionInfo versionInfo = V9PackageUtil.getVersionFromFile(fileContent);
            if (StringUtils.isNotBlank(version)) {
                versionInfo.setVersion(version);
            }
            getSelf().writeBackFile(projectId, file, sqliteUrl, versionInfo, source);
            // 写回后更新缓存
            redisTemplate.opsForValue().set(RedisKeyConstants.PROJECT_SUMMARY_FILE_SQLITE_URL + ":" + projectId, sqliteUrl, PROJECT_CACHE_EXPIRE, TimeUnit.MINUTES);
        }
        return execute;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void writeBackFile(Long projectId, ClientProjectFileSRespDTO file, String sqliteUrl, VersionInfo versionInfo, ProjectSumFileSourceEnum source) {
        byte[] updatedContent = V9PackageUtil.zipDBToFile(versionInfo, sqliteUrl, null);
        String summaryFileUrl = fileApi.createFile(file.getName(), null, updatedContent);

        ClientProjectFileSummaryDO newFileSummaryDO = new ClientProjectFileSummaryDO();
        newFileSummaryDO.setProjectId(projectId);
        newFileSummaryDO.setName(file.getName());
        newFileSummaryDO.setPath(StringUtils.substringAfterLast(summaryFileUrl, "/"));
        newFileSummaryDO.setUrl(summaryFileUrl);
        newFileSummaryDO.setSize(updatedContent.length);
        newFileSummaryDO.setVersion(file.getVersion() + 1);
        newFileSummaryDO.setIsActive(true);
        newFileSummaryDO.setSource(source.name());
        // 更新旧文件为非活动
        clientProjectFileSummaryMapper.updateActiveFalse(projectId);
        // 插入新文件
        clientProjectFileSummaryMapper.insert(newFileSummaryDO);
    }

    @Override
    public void insertProjectMergeFileSummary(ClientProjectFileSRespDTO clientProjectFileSRespDTO) {
        ClientProjectFileSummaryDO fileDO = ClientProjectConvert.INSTANCE.convert6_1(clientProjectFileSRespDTO);
        fileDO.setSource(ProjectSumFileSourceEnum.MERGE_PROJECT_FILE.name());
        clientProjectFileSummaryMapper.insert(fileDO);
    }

    @Override
    public void updateProjectFileSummary(ClientProjectFileSRespDTO clientProjectFileSRespDTO) {
        ClientProjectFileSummaryDO fileDO = ClientProjectConvert.INSTANCE.convert6_1(clientProjectFileSRespDTO);
        clientProjectFileSummaryMapper.updateById(fileDO);
    }

    @Override
    public void pullProjectFileSummary(Long projectId, String dbPath) {
        ResultHolder result = sqliteDataSourceUtil.executeWithSQLite(dbPath, () -> {
            ResultHolder holder = new ResultHolder();
            holder.engineeringList = collectDataApi.getEngineering();
            holder.projectEng = collectDataApi.getProjectEng(projectId);
            return holder;
        });

        // 保存工程数据
        clientEngineeringMapper.deleteByPrjId(projectId);
        clientEngineeringMapper.insertBatch(
                ClientProjectConvert.INSTANCE.convertList10(result.engineeringList, projectId)
        );

        // 保存项目数据
        PrjProjectEngDTO projectEng = result.projectEng;
        clientProjectMapper.update(null, new LambdaUpdateWrapper<ClientProjectDO>()
                .eq(ClientProjectDO::getProjectId, projectId)
                .set(ClientProjectDO::getCompiledVersion, projectEng.getEngCompileversion())
                .set(ClientProjectDO::getEdition, projectEng.getEngEdition())
                .set(ClientProjectDO::getFactors, projectEng.getEngParameters())
                .set(ClientProjectDO::getSyncVersion, projectEng.getEngSyncversion())
                .set(ClientProjectDO::getModelState, projectEng.getEngState())
        );
    }

    @Override
    public void setActiveFalse(Long projectId) {
        clientProjectFileSummaryMapper.updateActiveFalse(projectId);
    }


    /**
     * 同步工程数据到主数据源
     * @param projectId
     * @param engineeringList
     */
    private void syncToMainDatasource(Long projectId, List<PrjEngineeringDTO> engineeringList){
        clientEngineeringMapper.deleteByPrjId(projectId);
        clientEngineeringMapper.insertBatch(ClientProjectConvert.INSTANCE.convertList10(engineeringList, projectId));
    }

    /**
     * 获取 SQLite 数据库文件路径
     * @param projectId
     * @return
     */
    private String getProjectSqliteUrl(Long projectId, byte[] fileContent) {
        String cacheKey = RedisKeyConstants.PROJECT_SUMMARY_FILE_SQLITE_URL + ":" + projectId;
        String sqliteUrl = redisTemplate.opsForValue().get(cacheKey);

        if (sqliteUrl != null && new File(sqliteUrl).exists()) {
            log.debug("[getProjectSqliteUrl] 缓存命中且文件存在, projectId: {}, sqliteUrl: {}", projectId, sqliteUrl);
            return sqliteUrl;
        }
        Object lock = lockMap.computeIfAbsent(projectId, k -> new Object());
        synchronized (lock) {
            // double-check
            sqliteUrl = redisTemplate.opsForValue().get(cacheKey);
            if (sqliteUrl != null && new File(sqliteUrl).exists()) {
                log.debug("[getProjectSqliteUrl] double-check 命中缓存, projectId: {}, sqliteUrl: {}", projectId, sqliteUrl);
                return sqliteUrl;
            }
            try {
                String filePath = DB_STORAGE_PATH + "project_" + projectId + ".db";
                V9PackageUtil.unZipFileToDB(fileContent, filePath);
                // 写入缓存
                redisTemplate.opsForValue().set(cacheKey, filePath, PROJECT_CACHE_EXPIRE, TimeUnit.HOURS);
                log.info("[getProjectSqliteUrl] 缓存 SQLite URL, projectId: {}, sqliteUrl: {}", projectId, filePath);
                return filePath;
            } catch (Exception e) {
                log.error("[getProjectSqliteUrl] 获取 SQLite URL 失败, projectId: {}", projectId, e);
                throw exception(PROJECT_DATASOURCE_SWITCH_FAIL, e.getLocalizedMessage());
            } finally {
                lockMap.remove(projectId);
            }
        }
    }


    private ClientProjectFileApiImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }


}
