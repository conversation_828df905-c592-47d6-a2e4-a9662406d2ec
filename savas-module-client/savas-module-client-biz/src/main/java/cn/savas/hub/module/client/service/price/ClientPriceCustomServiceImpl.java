package cn.savas.hub.module.client.service.price;

import cn.savas.hub.module.client.controller.desktop.price.vo.ClientPriceCustomRespVO;
import cn.savas.hub.module.client.convert.price.PriceCustomConvert;
import cn.savas.hub.module.price.api.custom.PriceCustomApi;
import cn.savas.hub.module.price.api.custom.dto.PublishedCustomPriceDTO;
import cn.savas.hub.module.price.enums.custom.PriceCustomTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13 09:44
 */
@Service
public class ClientPriceCustomServiceImpl implements ClientPriceCustomService{
    @Resource
    private PriceCustomApi priceCustomApi;

    @Override
    public List<ClientPriceCustomRespVO> getStandardCustomPriceList() {
        List<PublishedCustomPriceDTO> customPriceList = priceCustomApi.getPublishedCustomPriceList(PriceCustomTypeEnum.PRICE_CUSTOM_STANDARD);
        return PriceCustomConvert.INSTANCE.convertList(customPriceList);
    }

    @Override
    public List<ClientPriceCustomRespVO> getCableCustomPriceList() {
        List<PublishedCustomPriceDTO> customPriceList = priceCustomApi.getPublishedCustomPriceList(PriceCustomTypeEnum.PRICE_CUSTOM_CABLE);
        return PriceCustomConvert.INSTANCE.convertList(customPriceList);
    }
}
