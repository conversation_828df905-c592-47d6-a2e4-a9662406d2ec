package cn.savas.hub.module.client.service.project.listener;

import cn.hutool.core.io.FileUtil;
import cn.savas.hub.module.client.dal.redis.RedisKeyConstants;
import cn.savas.hub.module.collect.event.SumFilePackageCallbackEvent;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 合并汇总文件打包的监听器
 * <AUTHOR>
 * @date 2025/5/9 15:18
 */
@Component
@Log4j2
public class PrjSumFIlePackListener{
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @EventListener
    @Async
    public void onApplicationEvent(SumFilePackageCallbackEvent event) {
        log.info("收到合并汇总文件打包的事件:{}", event);
        // 处理事件
        String cacheKey = RedisKeyConstants.PROJECT_SUMMARY_FILE_SQLITE_URL + ":" + event.getProjectId();
        // 删除缓存
        redisTemplate.delete(cacheKey);
    }
}
