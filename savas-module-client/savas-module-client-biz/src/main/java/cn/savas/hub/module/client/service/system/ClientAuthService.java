package cn.savas.hub.module.client.service.system;

import cn.savas.hub.module.system.api.auth.dto.ClientAuthLoginRespDTO;

/**
 * <AUTHOR>
 * @date 2025/8/13 15:06
 */
public interface ClientAuthService {
    /**
     * 客户端登录
     */
    ClientAuthLoginRespDTO clientLogin(String username, String password);

    /**
     * 客户端登出
     */
    void clientLogout(String token);

    /**
     * 客户端强退用户
     */
    void clientForceLogout(String accessToken);
}
