package cn.savas.hub.module.client.service.system;

import cn.hutool.extra.spring.SpringUtil;
import cn.savas.hub.framework.common.enums.UserTypeEnum;
import cn.savas.hub.framework.common.util.io.FileTypeUtils;
import cn.savas.hub.framework.websocket.core.sender.WebSocketMessageSender;
import cn.savas.hub.module.client.api.project.ClientProjectFileApi;
import cn.savas.hub.module.client.controller.desktop.project.vo.*;
import cn.savas.hub.module.client.controller.desktop.system.vo.*;
import cn.savas.hub.module.client.convert.project.ClientProjectConvert;
import cn.savas.hub.module.client.convert.system.ClientConfigConvert;
import cn.savas.hub.module.client.dal.dataobject.ClientDictBaseFileDO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectDO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectSettingDO;
import cn.savas.hub.module.client.dal.mapper.ClientDictBaseFileMapper;
import cn.savas.hub.module.client.dal.mapper.ClientProjectSettingMapper;
import cn.savas.hub.module.client.dal.redis.RedisKeyConstants;
import cn.savas.hub.module.client.enums.project.ProjectSettingEnum;
import cn.savas.hub.module.client.enums.project.ProjectSumFileSourceEnum;
import cn.savas.hub.module.client.enums.system.CSocketMessageTypeEnum;
import cn.savas.hub.module.client.service.project.ClientProjectService;
import cn.savas.hub.module.collect.api.CollectDataApi;
import cn.savas.hub.module.infra.api.file.FileApi;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/28 17:48
 */
@Log4j2
@Service
public class ClientConfigServiceImpl implements ClientConfigService {
    @Resource
    private ClientProjectSettingMapper projectSettingMapper;
    @Resource
    private WebSocketMessageSender webSocketMessageSender;
    @Resource
    private ClientDictBaseFileMapper dictBaseFileMapper;
    @Resource
    private FileApi fileApi;
    @Resource
    private ClientProjectFileApi clientProjectFileApi;
    @Resource
    private CollectDataApi collectDataApi;
    @Resource
    private ClientPermsService clientPermsService;
    @Resource
    private ClientProjectService clientProjectService;

    @Override
    public Map<String, Object> saveProjectSetting(String product, ProjectSettingReqVO reqVO) {
        // 获取项目信息
        ClientProjectDO projectDO = clientProjectService.getProjectByPrjId(reqVO.getProjectid());
        ProjectSettingEnum projectSettingEnum = ProjectSettingEnum.getByCode(reqVO.getSettingtype());
        Integer settingVersion = getSelf().saveProjectSettingInTransaction(product, reqVO);
        // 2. 更新汇总文件数据
        try {
            updateProjectFileSummary(product, reqVO);
        } catch (Exception e) {
            log.error("更新项目文件汇总数据失败, projectId: {}, settingType: {}", reqVO.getProjectid(), projectSettingEnum.getCode(), e);
            throw new RuntimeException("更新项目文件汇总数据失败", e);
        }
        // 获取和当前项目有关的人
        Set<Long> prjUserIds = clientPermsService.getAuthorizedPrjUserIds(reqVO.getProjectid());
        // 通知指定人员
        CSocketMessageTypeEnum messageTypeEnum = projectSettingEnum.getSocketMessageTypeEnum();
        prjUserIds.forEach(userId -> webSocketMessageSender.send(UserTypeEnum.DESKTOP.getValue(), userId, messageTypeEnum.getCode(), String.format(messageTypeEnum.getMessage(), projectDO.getName())));
        return ImmutableMap.of("version", settingVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveProjectSettingInTransaction(String product, ProjectSettingReqVO reqVO) {
        ProjectSettingEnum projectSettingEnum = ProjectSettingEnum.getByCode(reqVO.getSettingtype());
        Long projectid = reqVO.getProjectid();
        ClientProjectSettingDO currentSetting = projectSettingMapper.selectActiveByType(
                projectid,
                reqVO.getHostmodelid(),
                projectSettingEnum.getCode()
        );
        ObjectMapper objectMapper = new ObjectMapper();
        List itemsList = reqVO.getItems().stream().map(e -> objectMapper.convertValue(e, Map.class)).collect(Collectors.toList());

        ClientProjectSettingDO newSetting = new ClientProjectSettingDO();
        newSetting.setProjectId(projectid);
        newSetting.setEngId(reqVO.getHostmodelid());
        newSetting.setSettingType(projectSettingEnum.getCode());
        newSetting.setIsActive(true);
        newSetting.setSettingValue(itemsList);
        if (currentSetting != null) {
            projectSettingMapper.dull(currentSetting.getId());
            newSetting.setVersion(currentSetting.getVersion() + 1);
        } else {
            newSetting.setVersion(1);
        }
        projectSettingMapper.insert(newSetting);
        return newSetting.getVersion();
    }
    private void updateProjectFileSummary(String product, ProjectSettingReqVO reqVO) {
        // 更新汇总文件内数据
        clientProjectFileApi.changePrjFileSumDs(reqVO.getProjectid(), () -> {
            List<ClientPrjSettingEffectMultiVO> effectMultiList = new ArrayList<>();
            List<ClientPrjSettingEffectSetVO> effectSetList = new ArrayList<>();
            List<ClientPrjSettingMaterialVO> effectMaterialList = new ArrayList<>();
            List<ClientPrjSettingParamVO> effectParamList = new ArrayList<>();
            List<ClientPrjSettingRateVO> effectRateList = new ArrayList<>();
            List<ProjectSettingReqBaseVO> items = reqVO.getItems();
            items.forEach(item -> {
                if(item instanceof ClientPrjSettingEffectMultiVO){
                    ClientPrjSettingEffectMultiVO effectMulti = (ClientPrjSettingEffectMultiVO) item;
                    effectMultiList.add(effectMulti);
                } else if(item instanceof ClientPrjSettingEffectSetVO){
                    ClientPrjSettingEffectSetVO effectSet = (ClientPrjSettingEffectSetVO) item;
                    effectSetList.add(effectSet);
                } else if(item instanceof ClientPrjSettingMaterialVO){
                    ClientPrjSettingMaterialVO material = (ClientPrjSettingMaterialVO) item;
                    effectMaterialList.add(material);
                } else if(item instanceof ClientPrjSettingParamVO){
                    ClientPrjSettingParamVO param = (ClientPrjSettingParamVO) item;
                    effectParamList.add(param);
                } else if(item instanceof ClientPrjSettingRateVO){
                    ClientPrjSettingRateVO rate = (ClientPrjSettingRateVO) item;
                    effectRateList.add(rate);
                }
            });
            if (!effectParamList.isEmpty()) {
                collectDataApi.syncProjectParam(ClientConfigConvert.INSTANCE.convertList1(effectParamList));
            }
            if(!effectRateList.isEmpty()){
                collectDataApi.syncProjectRate(ClientConfigConvert.INSTANCE.convertList2(effectRateList));
            }
            if(!effectSetList.isEmpty()){
                collectDataApi.syncProjectEffectSet(product, ClientConfigConvert.INSTANCE.convertList3(effectSetList));
            }
            if(!effectMultiList.isEmpty()){
                collectDataApi.syncProjectEffectMulti(product, ClientConfigConvert.INSTANCE.convertList4(effectMultiList));
            }
            if(!effectMaterialList.isEmpty()){
                collectDataApi.syncProjectMainMaterial(ClientConfigConvert.INSTANCE.convertList5(effectMaterialList));
            }
            return true;
        }, true, ProjectSumFileSourceEnum.UPDATE_ENG_SETTING);
    }
    @Override
    public ClientSettingRespVO getProjectSettingActive(Long prjId, Long hostmodelid, ProjectSettingEnum settingEnum) {
        ClientProjectSettingDO setting = projectSettingMapper.selectActiveByType(prjId, hostmodelid, settingEnum.getCode());
        if (setting == null) {
            return new ClientSettingRespVO(Lists.newArrayList(), 0);
        }
        return new ClientSettingRespVO(setting.getSettingValue(), setting.getVersion());
    }

    @Override
    public ProjectSettingVersionRespVO getProjectSettingVersion(Long prjId, Long hostmodelid, ProjectSettingEnum settingEnum) {
        // 项目设置版本
        List<ClientProjectSettingDO> selectSettingVersionDO = projectSettingMapper.selectSettingVersion(prjId, hostmodelid, settingEnum.getCode());
        List<ProjectSettingVersionRespVO.Items> prjSetting = ClientProjectConvert.INSTANCE.convertList4(selectSettingVersionDO);
        return new ProjectSettingVersionRespVO(new ArrayList<>(prjSetting));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CachePut(cacheNames = RedisKeyConstants.CLIENT_DICT_SETTING, key = "#product + '_' + #compileMethod")
    public Boolean saveDictFile(MultipartFile file, Long uploadtime, String product, String compileMethod) throws IOException {
        String fileUrl = fileApi.createFile(file.getOriginalFilename(), null, file.getBytes());
        // 最新版本
        ClientDictBaseFileDO dictBaseFileDO = dictBaseFileMapper.selectActiveData(product, compileMethod);
        ClientDictBaseFileDO baseFileDO = new ClientDictBaseFileDO();
        baseFileDO.setPath(StringUtils.substringAfterLast(fileUrl, "/"));
        baseFileDO.setUrl(fileUrl);
        baseFileDO.setSize(file.getBytes().length);
        baseFileDO.setUploadTime(uploadtime);
        baseFileDO.setProduct(product);
        baseFileDO.setCompileMethod(compileMethod);
        if (dictBaseFileDO != null) {
            dictBaseFileMapper.dull(dictBaseFileDO.getId());
            baseFileDO.setVersion(dictBaseFileDO.getVersion() + 1);
        }else {
            baseFileDO.setVersion(1);
        }
        baseFileDO.setIsActive(Boolean.TRUE);
        dictBaseFileMapper.insert(baseFileDO);
        // 通知所有客户端用户
        webSocketMessageSender.sendAck(UserTypeEnum.DESKTOP.getValue(), null, CSocketMessageTypeEnum.DICT_CHANGE.getCode(), "字典配置变更");
        return true;
    }

    @Override
    public void getDictFile(HttpServletResponse response, String product, String compileMethod) throws Exception {
        ClientDictBaseFileDO baseFileDO = dictBaseFileMapper.selectActiveData(product, compileMethod);
        if (baseFileDO != null) {
            // 读取内容
            byte[] content = fileApi.getFileContent(baseFileDO.getPath());
            if (content == null) {
                response.setStatus(HttpStatus.NOT_FOUND.value());
                return;
            }
            FileTypeUtils.writeAttachment(response, baseFileDO.getPath(), content);
        }
    }



    @Override
    @Cacheable(cacheNames = RedisKeyConstants.CLIENT_DICT_SETTING, key = "#product + '_' + #compileMethod")
    public DictSettingVersionRespVO getDictSettingVersion(String product, String compileMethod) {
        DictSettingVersionRespVO versionRespVO = new DictSettingVersionRespVO();
        // 字典库文件版本
        ClientDictBaseFileDO dictBaseFileDO = dictBaseFileMapper.selectActiveData(product, compileMethod);
        if (dictBaseFileDO != null) {
            versionRespVO.setUploadtime(dictBaseFileDO.getUploadTime());
        }
        return versionRespVO;
    }


    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private ClientConfigServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}
