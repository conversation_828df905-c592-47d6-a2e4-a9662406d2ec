package cn.savas.hub.module.client.license.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;



/**
 * 许可证配置类
 */
@Data
@Configuration
@EnableScheduling
@ConfigurationProperties(prefix = "aicc.license")
@ConditionalOnProperty(name = "aicc.license.enabled", havingValue = "true", matchIfMissing = true)
public class LicenseConfiguration {
    /**
     * 是否启用许可证验证
     */
    private boolean enabled = true;

    /**
     * 验证失败时是否阻止应用启动
     */
    private boolean failFast = true;

    /**
     * 是否在启动时显示许可证详细信息
     */
    private boolean showDetailsOnStartup = true;

    /**
     * 是否启用定期验证
     */
    private boolean enablePeriodicValidation = true;

    /**
     * 定期验证间隔（秒）
     */
    private long periodicValidationInterval = 3600;
}
