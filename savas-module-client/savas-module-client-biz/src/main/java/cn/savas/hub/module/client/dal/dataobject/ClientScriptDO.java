package cn.savas.hub.module.client.dal.dataobject;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/9/10 14:32
 */
@TableName(value = "client_script")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
public class ClientScriptDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 脚本类型
     */
    private String scriptType;
    /**
     * 脚本路径
     */
    private String path;
    /**
     * 文件上传 URL
     */
    private String url;
    /**
     * 版本
     */
    private String version;
}
