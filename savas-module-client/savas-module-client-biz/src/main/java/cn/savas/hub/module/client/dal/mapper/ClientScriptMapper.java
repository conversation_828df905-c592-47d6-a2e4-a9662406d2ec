package cn.savas.hub.module.client.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.client.dal.dataobject.ClientScriptDO;
import cn.savas.hub.module.client.dal.dataobject.ClientScriptUpgradeDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/10 14:36
 */
@Mapper
public interface ClientScriptMapper extends BaseMapperX<ClientScriptDO> {


    @Select("SELECT version FROM Upgrade order by date DESC limit 1")
    String getLastVersion();

    default ClientScriptDO getLastUpdateFile(){
        return selectOne(new LambdaQueryWrapper<>(ClientScriptDO.class)
                .orderByDesc(ClientScriptDO::getCreateTime)
                .last("limit 1")
        );
    }

    @Select("SELECT id, version FROM Upgrade order by date asc")
    List<ClientScriptUpgradeDO> selectUpgradeAll();

    default ClientScriptDO selectOneByMaxDate(){
        return selectOne(new LambdaQueryWrapper<>(ClientScriptDO.class)
                .orderByDesc(ClientScriptDO::getCreateTime)
                .last("limit 1")
        );
    }

    @Select("SELECT content FROM upgradeitem WHERE version >= #{versionId} and tag & 1 = 1 AND state & 1 = 1 and state & 8 <> 8 ORDER BY version, sortid")
    List<String> selectUpdateScript(@Param("versionId") Integer versionId);
}
