package cn.savas.hub.module.client.license.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 许可证产品信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LicenseProduct {
    private Integer id;
    private String name;
    private int points;

    @Override
    public String toString() {
        return "LicenseProduct{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", points=" + points +
                '}';
    }
}
