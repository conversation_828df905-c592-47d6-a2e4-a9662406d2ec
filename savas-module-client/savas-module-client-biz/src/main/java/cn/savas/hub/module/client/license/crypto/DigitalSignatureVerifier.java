package cn.savas.hub.module.client.license.crypto;

import cn.savas.hub.module.client.license.constants.LicenseConstants;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.security.Signature;
import java.util.Base64;

/**
 * 数字签名验证器
 */
@Slf4j
public class DigitalSignatureVerifier {
    /**
     * 验证数字签名
     *
     * @param data 原始数据
     * @param signatureBase64 Base64编码的签名字符串
     * @param publicKey 公钥
     * @return 签名是否有效
     */
    public static boolean verifySignature(String data, String signatureBase64, PublicKey publicKey) {
        try {
            if (data == null || signatureBase64 == null || publicKey == null) {
                return false;
            }

            // 将数据转换为字节数组（使用UTF-8编码，与C#端保持一致）
            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);

            // Base64解码签名
            byte[] signatureBytes = Base64.getDecoder().decode(signatureBase64);

            // 创建签名验证器
            Signature signature = Signature.getInstance(LicenseConstants.Crypto.SIGNATURE_ALGORITHM);
            signature.initVerify(publicKey);
            signature.update(dataBytes);

            // 验证签名
            return signature.verify(signatureBytes);

        } catch (Exception e) {
            log.error("签名验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 使用硬编码公钥验证签名
     *
     * @param data 原始数据
     * @param signatureBase64 Base64编码的签名字符串
     * @return 签名是否有效
     */
    public static boolean verifySignatureWithHardcodedKey(String data, String signatureBase64) {
        try {
            PublicKey publicKey = RSAKeyParser.HardcodedKeys.getPublicKey();
            return verifySignature(data, signatureBase64, publicKey);
        } catch (Exception e) {
            log.error("使用硬编码公钥验证签名失败: {}", e.getMessage(), e);
            // 尝试备用方案
            try {
                PublicKey publicKey = RSAKeyParser.HardcodedKeys.getPublicKeyFromBase64();
                return verifySignature(data, signatureBase64, publicKey);
            } catch (Exception ex) {
                log.error("备用公钥验证也失败: {}", ex.getMessage(), ex);
                return false;
            }
        }
    }

    /**
     * 验证许可证的数字签名
     *
     * @param licenseData 许可证签名数据
     * @param signature 数字签名
     * @return 验证结果
     */
    public static boolean verifyLicenseSignature(String licenseData, String signature) {
        return verifySignatureWithHardcodedKey(licenseData, signature);
    }
}
