package cn.savas.hub.module.client.license.parser;

import cn.savas.hub.module.client.license.constants.LicenseConstants;
import cn.savas.hub.module.client.license.dto.AiccLicense;
import cn.savas.hub.module.client.license.dto.LicenseProduct;
import cn.savas.hub.module.client.license.dto.LicenseQuotaLibrary;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.client.enums.ErrorCodeConstants.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * 许可证解析器
 */
@Slf4j
@Component
public class LicenseParser {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(LicenseConstants.DateFormat.DEFAULT_FORMAT);
    private static final DateTimeFormatter DATE_FORMATTER_ALT = DateTimeFormatter.ofPattern(LicenseConstants.DateFormat.ALT_FORMAT);

    /**
     * 从文件解析许可证
     *
     * @param filePath 许可证文件路径
     * @return 解析后的许可证对象
     * @throws IOException 文件读取异常
     */
    public AiccLicense parseFile(Path filePath) throws IOException {
        // 1. 校验 Path
        if (filePath == null || !Files.exists(filePath)) {
            throw exception(LICENSE_FILE_NOT_FOUND);
        }

        // 2. 读取文件 NIO
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = Files.newBufferedReader(filePath)) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        } catch (IOException e) {
            log.error("读取许可证文件失败: {}", filePath, e);
            throw exception(LICENSE_FILE_READ_ERROR);
        }
        // 3. 解析 JSON
        return parseJson(content.toString());
    }

    /**
     * 从JSON字符串解析许可证*
     * @param jsonContent JSON内容
     * @return 解析后的许可证对象
     */
    public AiccLicense parseJson(String jsonContent) {
        if (!StringUtils.hasText(jsonContent)) {
            throw exception(INVALID_LICENSE_FORMAT);
        }

        try {
            // 使用 FastJSON 解析 JSON
            JSONObject jsonObject = JSON.parseObject(jsonContent);

            AiccLicense license = new AiccLicense();

            // 解析基本字段
            license.setLicenseId(jsonObject.getString(LicenseConstants.JsonFields.LICENSE_ID));
            license.setHardwareId(jsonObject.getString(LicenseConstants.JsonFields.HARDWARE_ID));
            license.setCompanyName(jsonObject.getString(LicenseConstants.JsonFields.COMPANY_NAME));
            license.setRemarks(jsonObject.getString(LicenseConstants.JsonFields.REMARKS));
            license.setSignature(jsonObject.getString(LicenseConstants.JsonFields.SIGNATURE));

            // 解析日期字段
            String issueDateStr = jsonObject.getString(LicenseConstants.JsonFields.ISSUE_DATE);
            if (StringUtils.hasText(issueDateStr)) {
                license.setIssueDate(parseDateTime(issueDateStr));
            }

            String expiryDateStr = jsonObject.getString(LicenseConstants.JsonFields.EXPIRY_DATE);
            if (StringUtils.hasText(expiryDateStr) && !LicenseConstants.SpecialValues.NULL_VALUE.equals(expiryDateStr)) {
                license.setExpiryDate(parseDateTime(expiryDateStr));
            }

            // 解析产品列表
            List<LicenseProduct> products = parseProducts(jsonObject);
            license.setProducts(products);

            // 解析定额库列表
            List<LicenseQuotaLibrary> quotaLibraries = parseQuotaLibraries(jsonObject);
            license.setQuotaLibraries(quotaLibraries);

            log.debug("成功解析许可证，许可证ID: {}", license.getLicenseId());
            return license;

        } catch (JSONException e) {
            log.error("JSON格式错误: {}", e.getMessage(), e);
            throw exception(INVALID_LICENSE_FORMAT);
        } catch (Exception e) {
            log.error("解析许可证时发生未知错误: {}", e.getMessage(), e);
            throw exception(LICENSE_PARSE_ERROR);
        }
    }

    /**
     * 解析日期时间
     * 支持多种日期格式的解析
     *
     * @param dateStr 日期字符串
     * @return 解析后的日期时间，解析失败返回 null
     */
    private LocalDateTime parseDateTime(String dateStr) {
        if (!StringUtils.hasText(dateStr)) {
            return null;
        }

        try {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            try {
                return LocalDateTime.parse(dateStr, DATE_FORMATTER_ALT);
            } catch (DateTimeParseException ex) {
                log.warn("无法解析日期格式: {}", dateStr);
                return null;
            }
        }
    }
    /**
     * 解析产品列表
     * 使用 FastJSON 解析产品数组
     *
     * @param jsonObject 解析后的 JSON 对象
     * @return 产品列表
     */
    private List<LicenseProduct> parseProducts(JSONObject jsonObject) {
        List<LicenseProduct> products = new ArrayList<>();

        try {
            JSONArray productsArray = jsonObject.getJSONArray(LicenseConstants.JsonFields.PRODUCTS);
            if (productsArray != null && !productsArray.isEmpty()) {
                for (int i = 0; i < productsArray.size(); i++) {
                    JSONObject productObj = productsArray.getJSONObject(i);
                    if (productObj != null) {
                        LicenseProduct product = parseProduct(productObj);
                        products.add(product);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析产品列表时发生错误: {}", e.getMessage(), e);
            // 返回空列表而不是抛出异常，保证许可证解析的健壮性
        }

        log.debug("解析到 {} 个产品", products.size());
        return products;
    }

    /**
     * 解析单个产品对象
     *
     * @param productObj 产品 JSON 对象
     * @return 产品对象
     */
    private LicenseProduct parseProduct(JSONObject productObj) {
        LicenseProduct product = new LicenseProduct();

        product.setId(productObj.getInteger(LicenseConstants.JsonFields.PRODUCT_ID));
        product.setName(productObj.getString(LicenseConstants.JsonFields.PRODUCT_NAME));

        // 安全地解析点数，提供默认值
        Integer points = productObj.getInteger(LicenseConstants.JsonFields.PRODUCT_POINTS);
        product.setPoints(points != null ? points : LicenseConstants.SpecialValues.DEFAULT_POINTS);

        return product;
    }

    /**
     * 解析定额库列表
     * 使用 FastJSON 解析定额库数组
     *
     * @param jsonObject 解析后的 JSON 对象
     * @return 定额库列表
     */
    private List<LicenseQuotaLibrary> parseQuotaLibraries(JSONObject jsonObject) {
        List<LicenseQuotaLibrary> quotaLibraries = new ArrayList<>();

        try {
            JSONArray quotasArray = jsonObject.getJSONArray(LicenseConstants.JsonFields.QUOTA_LIBRARIES);
            if (quotasArray != null && !quotasArray.isEmpty()) {
                for (int i = 0; i < quotasArray.size(); i++) {
                    JSONObject quotaObj = quotasArray.getJSONObject(i);
                    if (quotaObj != null) {
                        LicenseQuotaLibrary quota = parseQuotaLibrary(quotaObj);
                        quotaLibraries.add(quota);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析定额库列表时发生错误: {}", e.getMessage(), e);
            // 返回空列表而不是抛出异常，保证许可证解析的健壮性
        }

        log.debug("解析到 {} 个定额库", quotaLibraries.size());
        return quotaLibraries;
    }

    /**
     * 解析单个定额库对象
     *
     * @param quotaObj 定额库 JSON 对象
     * @return 定额库对象
     */
    private LicenseQuotaLibrary parseQuotaLibrary(JSONObject quotaObj) {
        LicenseQuotaLibrary quota = new LicenseQuotaLibrary();

        quota.setId(quotaObj.getInteger(LicenseConstants.JsonFields.QUOTA_ID));
        quota.setName(quotaObj.getString(LicenseConstants.JsonFields.QUOTA_NAME));
        quota.setVersion(quotaObj.getString(LicenseConstants.JsonFields.QUOTA_VERSION));

        return quota;
    }
}
