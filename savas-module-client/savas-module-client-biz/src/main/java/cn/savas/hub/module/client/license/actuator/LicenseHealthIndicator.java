package cn.savas.hub.module.client.license.actuator;

import cn.savas.hub.module.client.license.dto.LicenseInfo;
import cn.savas.hub.module.client.license.service.LicenseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 许可证健康检查指示器
 */
@Component
@ConditionalOnClass(HealthIndicator.class)
@ConditionalOnProperty(name = "aicc.license.enabled", havingValue = "true", matchIfMissing = true)
public class LicenseHealthIndicator implements HealthIndicator {

    private final LicenseService licenseService;

    @Autowired
    public LicenseHealthIndicator(LicenseService licenseService) {
        this.licenseService = licenseService;
    }

    @Override
    public Health health() {
        try {
            if (!licenseService.isLicenseValid()) {
                return Health.down()
                        .withDetail("status", "INVALID")
                        .withDetail("message", "许可证无效或已过期")
                        .build();
            }

            LicenseInfo licenseInfo = licenseService.getLicenseInfo();
            Health.Builder builder = Health.up();

            // 基本信息
            builder.withDetail("status", "VALID")
                   .withDetail("licenseId", licenseInfo.getLicenseId())
                   .withDetail("companyName", licenseInfo.getCompanyName())
                   .withDetail("hardwareId", licenseInfo.getHardwareId());

            // 时间信息
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (licenseInfo.getIssueDate() != null) {
                builder.withDetail("issueDate", licenseInfo.getIssueDate().format(formatter));
            }

            if (licenseInfo.getExpiryDate() != null) {
                builder.withDetail("expiryDate", licenseInfo.getExpiryDate().format(formatter));
                long remainingDays = licenseInfo.getRemainingDays();
                builder.withDetail("remainingDays", remainingDays);

                // 根据剩余天数设置健康状态
                if (remainingDays <= 0) {
                    return Health.down()
                            .withDetail("status", "EXPIRED")
                            .withDetail("message", "许可证已过期")
                            .build();
                } else if (remainingDays <= 7) {
                    builder.status("WARNING")
                           .withDetail("warning", "许可证即将过期");
                }
            } else {
                builder.withDetail("expiryDate", "永不过期");
                builder.withDetail("remainingDays", "无限制");
            }

            // 授权信息
            builder.withDetail("authorizedProducts", licenseInfo.getProducts().size())
                   .withDetail("authorizedQuotaLibraries", licenseInfo.getQuotaLibraries().size());

            // 验证时间
            if (licenseInfo.getLastValidationTime() != null) {
                builder.withDetail("lastValidationTime",
                    licenseInfo.getLastValidationTime().format(formatter));
            }

            return builder.build();

        } catch (Exception e) {
            return Health.down()
                    .withDetail("status", "ERROR")
                    .withDetail("message", "许可证检查失败: " + e.getMessage())
                    .withException(e)
                    .build();
        }
    }
}
