package cn.savas.hub.module.client.api.system;

import cn.hutool.extra.spring.SpringUtil;
import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.framework.common.util.object.BeanUtils;
import cn.savas.hub.framework.mybatis.core.util.SQLiteDataSourceUtil;
import cn.savas.hub.module.client.api.system.dto.*;
import cn.savas.hub.module.client.controller.desktop.project.vo.ClientEngineeringVO;
import cn.savas.hub.module.client.convert.system.ClientSystemConvert;
import cn.savas.hub.module.client.dal.dataobject.ClientDataPermsDO;
import cn.savas.hub.module.client.dal.dataobject.ClientFuncPermsDO;
import cn.savas.hub.module.client.dal.dataobject.ClientFuncPermsRuleDO;
import cn.savas.hub.module.client.dal.dataobject.ClientScriptDO;
import cn.savas.hub.module.client.dal.mapper.ClientDataPermsMapper;
import cn.savas.hub.module.client.dal.mapper.ClientFuncPermsRoleMapper;
import cn.savas.hub.module.client.dal.mapper.ClientScriptMapper;
import cn.savas.hub.module.client.dal.redis.RedisKeyConstants;
import cn.savas.hub.module.client.service.project.ClientProjectService;
import cn.savas.hub.module.client.service.system.ClientPermsService;
import cn.savas.hub.module.client.service.system.ClientScriptService;
import cn.savas.hub.module.infra.api.file.FileApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/22 16:27
 */
@Slf4j
@Service
public class ClientSystemApiImpl implements ClientSystemApi{
    @Resource
    private ClientPermsService clientPermsService;
    @Resource
    private ClientFuncPermsRoleMapper clientFuncPermsRoleMapper;
    @Resource
    private ClientProjectService  clientProjectService;
    @Resource
    private ClientDataPermsMapper clientDataPermsMapper;
    @Resource
    private ClientScriptService clientScriptService;

    @Override
    public boolean hasAnyPermissions(Long userId, String... permissions) {
        List<String> userPermissionList = getSelf().getClientUserFuncPermsCache(userId);
        for (String permission : permissions) {
            if (userPermissionList.contains(permission)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<ClientFuncPermsRespDTO> getFuncPermissions() {
        List<ClientFuncPermsDO> clientFuncPerms = clientPermsService.getClientFuncPerms();
        return BeanUtils.toBean(clientFuncPerms, ClientFuncPermsRespDTO.class);
    }

    @Override
    public List<Long> getRoleClientPermissions(Long roleId) {
        List<ClientFuncPermsRuleDO> clientPermissions = clientFuncPermsRoleMapper.getRoleClientPermissions(roleId);
        return clientPermissions.stream().map(ClientFuncPermsRuleDO::getFuncId).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.CLIENT_USER_FUNC_PERMS, allEntries = true)
    public void assignRoleClient(Long roleId, List<Long> menuIds) {
        clientFuncPermsRoleMapper.deleteByRoleId(roleId);
        clientFuncPermsRoleMapper.insertBatch(roleId, menuIds);
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.CLIENT_USER_FUNC_PERMS, allEntries = true)
    public Boolean addFuncPermissions(ClientFuncPermsReqDTO req) {
        clientPermsService.addFuncPermissions(req);
        return true;
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.CLIENT_USER_FUNC_PERMS, allEntries = true)
    public Boolean editFuncPermissions(ClientFuncPermsReqDTO bean) {
        clientPermsService.updateFuncPermissions(bean);
        return true;
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.CLIENT_USER_FUNC_PERMS, allEntries = true)
    public Boolean deleteFuncPermissions(Long id) {
        clientPermsService.deleteFuncPermissions(id);
        return true;
    }

    @Override
    public PageResult<ClientFuncPermsRespDTO> getFuncPermissionsPage(ClientFuncPermsPageReqDTO pageReqVO) {
        PageResult<ClientFuncPermsDO> page = clientPermsService.getFuncPermissionsPage(pageReqVO);
        return BeanUtils.toBean(page, ClientFuncPermsRespDTO.class);
    }

    @Override
    public List<ClientDataPermsTreeRespDTO> getProjectPermsDataTree(Long projectId) {
        List<ClientEngineeringVO> engineeringList = clientProjectService.getEngineeringList(projectId);
        //TODO 暂时只控制项目权限
        engineeringList = engineeringList.stream().filter(e-> ClientClassIdEnum.PROJECTENG.getCode().equals(e.getAlias())).collect(Collectors.toList());
        return ClientSystemConvert.INSTANCE.convert3List(engineeringList);
    }

    @Override
    public Long addProjectPermsUser(ClientDataPermsPrjReqDTO req) {
        ClientDataPermsDO permsDO = ClientSystemConvert.INSTANCE.convert4(req);
        clientDataPermsMapper.insert(permsDO);
        return permsDO.getId();
    }

    @Override
    public Boolean deleteProjectPermsUser(Long projectId, Long modelId, Long userId) {
        clientDataPermsMapper.delete(new LambdaQueryWrapper<ClientDataPermsDO>()
                .eq(ClientDataPermsDO::getProjectId, projectId)
                .eq(ClientDataPermsDO::getModelId, modelId)
                .eq(ClientDataPermsDO::getUserId, userId));
        return true;
    }

    @Override
    public List<ClientDataPermsRespDTO> getProjectDataPerms(Long projectId, Long modelId) {
        List<ClientDataPermsDO> dataPerms = clientDataPermsMapper.selectList(new LambdaQueryWrapper<ClientDataPermsDO>()
                .eq(ClientDataPermsDO::getProjectId, projectId)
                .eq(ClientDataPermsDO::getModelId, modelId));
        return BeanUtils.toBean(dataPerms, ClientDataPermsRespDTO.class);
    }

    @Override
    public void uploadUpdateScript(String filePath, String fileUrl) {
        clientScriptService.uploadUpdateScript(filePath, fileUrl);
    }

    /**
     * @param userId
     * @return
     */
    @Cacheable(value = RedisKeyConstants.CLIENT_USER_FUNC_PERMS, key = "#userId")
    public List<String> getClientUserFuncPermsCache(Long userId){
        List<ClientFuncPermsDO> userFuncPerms = clientPermsService.getClientUserFuncPerms(userId);
        return userFuncPerms.stream().map(ClientFuncPermsDO::getPermission).collect(Collectors.toList());
    }



    private ClientSystemApiImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}
