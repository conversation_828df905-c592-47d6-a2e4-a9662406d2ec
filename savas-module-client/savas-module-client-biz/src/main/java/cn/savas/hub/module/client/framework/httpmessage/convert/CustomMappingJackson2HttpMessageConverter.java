package cn.savas.hub.module.client.framework.httpmessage.convert;

import cn.savas.hub.module.client.framework.httpmessage.serializer.modifier.CustomBeanSerializerModifier;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

/**
 * <AUTHOR>
 * @date 2024/12/10 14:55
 */
public class CustomMappingJackson2HttpMessageConverter extends MappingJackson2HttpMessageConverter {
    public CustomMappingJackson2HttpMessageConverter() {
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.setSerializerModifier(new CustomBeanSerializerModifier());
        objectMapper.registerModule(module);
        setObjectMapper(objectMapper);
    }
}
