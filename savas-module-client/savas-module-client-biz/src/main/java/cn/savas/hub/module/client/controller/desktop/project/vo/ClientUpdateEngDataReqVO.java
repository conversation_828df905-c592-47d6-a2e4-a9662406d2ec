package cn.savas.hub.module.client.controller.desktop.project.vo;

import cn.savas.hub.module.client.controller.desktop.project.vo.resolver.EngAliasTypeIdResolver;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonTypeIdResolver;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/8 09:36
 */
@Data
public class ClientUpdateEngDataReqVO {
    @Schema(description = "数据")
    @NotNull(message = "数据不能为空")
    private List<UpdateEngBaseVO> items;

    @Data
    @JsonTypeInfo(use = JsonTypeInfo.Id.CUSTOM, property = "alias", visible = true)
    @JsonTypeIdResolver(EngAliasTypeIdResolver.class)
    public static class UpdateEngBaseVO{
        @Schema(description = "数据类型")
        private Long alias;
    }
    @Data
    public static class UpdateProjectVO extends UpdateEngBaseVO{
        private Long id;
        private Long projectid;
        private String edition;
        private Integer compiledversion;
        private Integer submittedversion;
        private Long modelstate;
    }
}
