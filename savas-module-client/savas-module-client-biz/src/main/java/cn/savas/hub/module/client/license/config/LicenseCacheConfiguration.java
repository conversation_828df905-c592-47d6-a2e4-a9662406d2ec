package cn.savas.hub.module.client.license.config;

import cn.savas.hub.module.client.license.constants.LicenseConstants;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * 许可证模块专用缓存配置
 * 使用 Caffeine 本地缓存，独立于全局 Redis 缓存
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = "aicc.license.enabled", havingValue = "true", matchIfMissing = true)
public class LicenseCacheConfiguration {

    /**
     * 许可证模块专用的缓存管理器
     * 使用 Caffeine 本地内存缓存，独立于全局 Redis 缓存
     */
    @Bean("licenseCacheManager")
    public CacheManager licenseCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // 配置默认的 Caffeine 缓存
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(1000)                    // 最大缓存条目数
            .expireAfterWrite(30, TimeUnit.MINUTES)  // 写入后30分钟过期
            .expireAfterAccess(10, TimeUnit.MINUTES) // 访问后10分钟过期
            .recordStats());                      // 启用统计

        // 预定义缓存名称
        cacheManager.setCacheNames(Arrays.asList(
            LicenseConstants.Cache.LICENSE_CONTENT_CACHE,
            LicenseConstants.Cache.LICENSE_VALIDATION_CACHE,
            LicenseConstants.Cache.HARDWARE_ID_CACHE,
            LicenseConstants.Cache.LICENSE_INFO_CACHE  // LicenseService.getLicenseInfo() 使用的缓存
        ));

        return cacheManager;
    }
}
