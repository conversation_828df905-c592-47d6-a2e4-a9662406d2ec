package cn.savas.hub.module.client.license.util;

import cn.savas.hub.module.client.license.constants.LicenseConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;

/**
 * 硬件ID生成器
 * 与C#端的硬件ID生成算法保持一致
 */
@Component
public class HardwareIdGenerator {

    private static final Logger logger = LoggerFactory.getLogger(HardwareIdGenerator.class);

    /**
     * 获取当前机器的硬件ID
     * 使用Spring缓存，避免重复计算
     *
     * @return 硬件ID字符串
     */
    @Cacheable(value = LicenseConstants.Cache.HARDWARE_ID_CACHE, unless = "#result == null", cacheManager = "licenseCacheManager")
    public String getCurrentHardwareId() {
        try {
            // 获取硬件信息
            String motherboardSerial = getMotherboardSerial();
            String cpuId = getCpuId();
            String diskSerial = getDiskSerial();

            // 组合硬件信息
            String hardwareInfo = (motherboardSerial != null ? motherboardSerial : "") +
                                 (cpuId != null ? cpuId : "") +
                                 (diskSerial != null ? diskSerial : "");

            // 如果没有获取到硬件信息，使用计算机名和用户名
            if (hardwareInfo.isEmpty()) {
                String computerName = System.getProperty("user.name");
                String userName = System.getProperty("user.home");
                hardwareInfo = computerName + "_" + userName;
            }

            // 计算MD5哈希
            return calculateMD5(hardwareInfo);

        } catch (Exception e) {
            logger.error("获取硬件ID时发生错误: {}", e.getMessage(), e);
            // 备用方案：使用系统属性
            String fallback = System.getProperty("user.name") + "_" + System.getProperty("os.name");
            logger.warn("使用备用方案生成硬件ID: {}", fallback);
            return calculateMD5(fallback);
        }
    }

    /**
     * 获取主板序列号
     */
    private static String getMotherboardSerial() {
        try {
            String os = System.getProperty("os.name").toLowerCase();

            if (os.contains("windows")) {
                return executeCommand("wmic baseboard get serialnumber /value")
                    .stream()
                    .filter(line -> line.startsWith("SerialNumber="))
                    .map(line -> line.substring("SerialNumber=".length()).trim())
                    .filter(serial -> !serial.isEmpty() && !"None".equals(serial))
                    .findFirst()
                    .orElse(null);
            } else if (os.contains("linux")) {
                return executeCommand("sudo dmidecode -s baseboard-serial-number")
                    .stream()
                    .filter(line -> !line.trim().isEmpty())
                    .findFirst()
                    .orElse(null);
            }
        } catch (Exception e) {
            logger.debug("获取主板序列号失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取CPU ID
     */
    private static String getCpuId() {
        try {
            String os = System.getProperty("os.name").toLowerCase();

            if (os.contains("windows")) {
                return executeCommand("wmic cpu get processorid /value")
                    .stream()
                    .filter(line -> line.startsWith("ProcessorId="))
                    .map(line -> line.substring("ProcessorId=".length()).trim())
                    .filter(id -> !id.isEmpty())
                    .findFirst()
                    .orElse(null);
            } else if (os.contains("linux")) {
                return executeCommand("cat /proc/cpuinfo | grep 'processor' | head -1")
                    .stream()
                    .findFirst()
                    .orElse(null);
            }
        } catch (Exception e) {
            logger.debug("获取CPU ID失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取硬盘序列号
     */
    private static String getDiskSerial() {
        try {
            String os = System.getProperty("os.name").toLowerCase();

            if (os.contains("windows")) {
                return executeCommand("wmic diskdrive where \"MediaType='Fixed hard disk media'\" get serialnumber /value")
                    .stream()
                    .filter(line -> line.startsWith("SerialNumber="))
                    .map(line -> line.substring("SerialNumber=".length()).trim())
                    .filter(serial -> !serial.isEmpty())
                    .findFirst()
                    .orElse(null);
            } else if (os.contains("linux")) {
                return executeCommand("lsblk -d -o name,serial | grep -v loop")
                    .stream()
                    .filter(line -> !line.trim().isEmpty())
                    .findFirst()
                    .orElse(null);
            }
        } catch (Exception e) {
            logger.debug("获取硬盘序列号失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 执行系统命令
     */
    private static List<String> executeCommand(String command) {
        List<String> result = new ArrayList<>();
        try {
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                result.add(line);
            }
            process.waitFor();
        } catch (Exception e) {
            logger.debug("执行命令失败: {}, 错误: {}", command, e.getMessage());
        }
        return result;
    }

    /**
     * 计算MD5哈希值
     */
    private static String calculateMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes("UTF-8"));

            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02X", b));
            }
            return sb.toString();
        } catch (Exception e) {
            logger.error("计算MD5失败: {}", e.getMessage(), e);
            return String.valueOf(input.hashCode());
        }
    }

    /**
     * 获取硬件信息摘要
     */
    public String getHardwareInfoSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("硬件信息摘要:\n");
        summary.append("操作系统: ").append(System.getProperty("os.name")).append("\n");
        summary.append("用户名: ").append(System.getProperty("user.name")).append("\n");
        summary.append("主板序列号: ").append(getMotherboardSerial()).append("\n");
        summary.append("CPU ID: ").append(getCpuId()).append("\n");
        summary.append("硬盘序列号: ").append(getDiskSerial()).append("\n");
        summary.append("硬件ID: ").append(getCurrentHardwareId()).append("\n");
        return summary.toString();
    }
}
