package cn.savas.hub.module.client.license.util;

import cn.savas.hub.module.client.license.constants.LicenseConstants;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 许可证缓存管理工具
 * 提供缓存状态查看和管理功能
 */
@Slf4j
@Component
public class LicenseCacheManagerUtil {

    private final CacheManager licenseCacheManager;

    @Autowired
    public LicenseCacheManagerUtil(@Qualifier("licenseCacheManager") CacheManager licenseCacheManager) {
        this.licenseCacheManager = licenseCacheManager;
    }

    /**
     * 获取所有缓存的统计信息
     */
    public Map<String, CacheStats> getAllCacheStats() {
        Map<String, CacheStats> statsMap = new HashMap<>();

        for (String cacheName : licenseCacheManager.getCacheNames()) {
            org.springframework.cache.Cache cache = licenseCacheManager.getCache(cacheName);
            if (cache instanceof CaffeineCache) {
                CaffeineCache caffeineCache = (CaffeineCache) cache;
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = caffeineCache.getNativeCache();
                CacheStats stats = nativeCache.stats();
                statsMap.put(cacheName, stats);
            }
        }

        return statsMap;
    }

    /**
     * 获取指定缓存的统计信息
     */
    public CacheStats getCacheStats(String cacheName) {
        org.springframework.cache.Cache cache = licenseCacheManager.getCache(cacheName);
        if (cache instanceof CaffeineCache) {
            CaffeineCache caffeineCache = (CaffeineCache) cache;
            com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = caffeineCache.getNativeCache();
            return nativeCache.stats();
        }
        return null;
    }

    /**
     * 清除指定缓存
     */
    public void evictCache(String cacheName) {
        org.springframework.cache.Cache cache = licenseCacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
            log.info("已清除缓存: {}", cacheName);
        }
    }

    /**
     * 清除所有许可证相关缓存
     */
    public void evictAllLicenseCaches() {
        evictCache(LicenseConstants.Cache.LICENSE_CONTENT_CACHE);
        evictCache(LicenseConstants.Cache.LICENSE_VALIDATION_CACHE);
        evictCache(LicenseConstants.Cache.HARDWARE_ID_CACHE);
        evictCache(LicenseConstants.Cache.LICENSE_INFO_CACHE);
        log.info("已清除所有许可证缓存");
    }
    /**
     * 打印缓存统计信息
     */
    public void printCacheStats() {
        log.info("=== 许可证缓存统计信息 ===");

        Map<String, CacheStats> allStats = getAllCacheStats();
        for (Map.Entry<String, CacheStats> entry : allStats.entrySet()) {
            String cacheName = entry.getKey();
            CacheStats stats = entry.getValue();

            log.info("缓存名称: {}", cacheName);
            log.info("  - 请求次数: {}", stats.requestCount());
            log.info("  - 命中次数: {}", stats.hitCount());
            log.info("  - 命中率: {:.2f}%", stats.hitRate() * 100);
            log.info("  - 未命中次数: {}", stats.missCount());
            log.info("  - 加载次数: {}", stats.loadCount());
            log.info("  - 平均加载时间: {:.2f}ms", stats.averageLoadPenalty() / 1_000_000.0);
            log.info("  - 驱逐次数: {}", stats.evictionCount());
        }

        log.info("========================");
    }
}
