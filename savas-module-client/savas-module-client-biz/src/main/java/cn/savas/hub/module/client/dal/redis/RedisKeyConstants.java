package cn.savas.hub.module.client.dal.redis;

/**
 * System Redis Key 枚举类
 *
 * */
public interface RedisKeyConstants {
    /**
     * 客户端拥有指定用户的功能权限
     * KEY 格式：client_user_func_perms:{userId}
     * VALUE 数据类型：String 功能权限标识
     */
    String CLIENT_USER_FUNC_PERMS = "client_user_func_perms";

    /**
     * 客户端字典配置
     * KEY 格式：client_dict_setting
     * VALUE 数据类型：Object
     */
    String CLIENT_DICT_SETTING = "client_dict_setting";

    /**
     * 项目汇总文件
     * KEY 格式：sum_sqlite_url:{projectId}
     * VALUE 数据类型：String
     */
    String PROJECT_SUMMARY_FILE_SQLITE_URL = "sum_sqlite_url";

    /**
     * 流程锁定
     * KEY 格式：client_lock_flow:{projectId}:{hostmodelid}
     * VALUE 数据类型：String
     */
    String CLIENT_LOCK_FLOW = "client_lock_flow";

    /**
     * 客户端更新脚本版本
     */
    String CLIENT_UPDATE_SCRIPT_VERSION = "client_update_script_version";
}
