package cn.savas.hub.module.client.license.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 许可证定额库信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LicenseQuotaLibrary {
    private Integer id;
    private String name;
    private String version;

    @Override
    public String toString() {
        return "LicenseQuotaLibrary{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", version='" + version + '\'' +
                '}';
    }
}
