package cn.savas.hub.module.client.controller.desktop.flow.vo.resolver;

import cn.savas.hub.module.client.controller.desktop.flow.vo.ClientFlowDataNodeDirVO;
import cn.savas.hub.module.client.controller.desktop.flow.vo.ClientFlowDataNodeVO;
import cn.savas.hub.module.client.controller.desktop.flow.vo.ClientFlowDataScheduleVO;
import cn.savas.hub.module.client.controller.desktop.flow.vo.ClientFlowSuggestVO;
import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.DatabindContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.annotation.JsonTypeIdResolver;
import com.fasterxml.jackson.databind.jsontype.impl.TypeIdResolverBase;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:37
 */
public class FlowAliasTypeIdResolver extends TypeIdResolverBase {
    private static final String FLOW_NODE = ClientClassIdEnum.FLOWNODE.getCode().toString();
    private static final String FLOW_NODE_DIRECTION = ClientClassIdEnum.FLOWNODEDIRECTION.getCode().toString();
    private static final String FLOW_SCHEDULE = ClientClassIdEnum.FLOWSCHEDULE.getCode().toString();
    private static final String CLASS_ID_SUGGESTION = ClientClassIdEnum.SUGGESTION.getCode().toString();

    @Override
    public String idFromValue(Object value) {
        if (value instanceof ClientFlowDataNodeVO) return FLOW_NODE;
        if (value instanceof ClientFlowDataNodeDirVO) return FLOW_NODE_DIRECTION;
        if (value instanceof ClientFlowDataScheduleVO) return FLOW_SCHEDULE;
        if (value instanceof ClientFlowSuggestVO) return CLASS_ID_SUGGESTION;
        return null;
    }

    @Override
    public String idFromValueAndType(Object value, Class<?> suggestedType) {
        return idFromValue(value);
    }

    @Override
    public JavaType typeFromId(DatabindContext context, String id) {
        if (FLOW_NODE.equals(id)) {
            return context.constructType(ClientFlowDataNodeVO.class);
        }else if (FLOW_NODE_DIRECTION.equals(id)) {
            return context.constructType(ClientFlowDataNodeDirVO.class);
        }else if (FLOW_SCHEDULE.equals(id)) {
            return context.constructType(ClientFlowDataScheduleVO.class);
        }else if(CLASS_ID_SUGGESTION.equals(id)){
            return context.constructType(ClientFlowSuggestVO.class);
        }else {
            throw new IllegalArgumentException("Unknown alias: " + id);
        }
    }

    @Override
    public JsonTypeInfo.Id getMechanism() {
        return JsonTypeInfo.Id.CUSTOM;
    }
}
