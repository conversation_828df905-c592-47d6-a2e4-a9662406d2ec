package cn.savas.hub.module.client.license.actuator;

import cn.savas.hub.module.client.license.dto.LicenseInfo;
import cn.savas.hub.module.client.license.service.LicenseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.boot.actuate.endpoint.annotation.WriteOperation;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 许可证信息端点
 */
@Component
@Endpoint(id = "license")
@ConditionalOnClass(name = "org.springframework.boot.actuate.endpoint.annotation.Endpoint")
@ConditionalOnProperty(name = "aicc.license.enabled", havingValue = "true", matchIfMissing = true)
public class LicenseInfoEndpoint {

    private final LicenseService licenseService;

    @Autowired
    public LicenseInfoEndpoint(LicenseService licenseService) {
        this.licenseService = licenseService;
    }

    /**
     * 获取许可证信息
     */
    @ReadOperation
    public Map<String, Object> getLicenseInfo() {
        Map<String, Object> result = new HashMap<>();

        try {
            LicenseInfo licenseInfo = licenseService.getLicenseInfo();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 基本信息
            result.put("valid", licenseInfo.isValid());
            result.put("licenseId", licenseInfo.getLicenseId());
            result.put("companyName", licenseInfo.getCompanyName());
            result.put("hardwareId", licenseInfo.getHardwareId());
            result.put("currentHardwareId", licenseService.getCurrentHardwareId());

            // 时间信息
            if (licenseInfo.getIssueDate() != null) {
                result.put("issueDate", licenseInfo.getIssueDate().format(formatter));
            }

            if (licenseInfo.getExpiryDate() != null) {
                result.put("expiryDate", licenseInfo.getExpiryDate().format(formatter));
                result.put("remainingDays", licenseInfo.getRemainingDays());
                result.put("expired", licenseInfo.isExpired());
            } else {
                result.put("expiryDate", null);
                result.put("remainingDays", -1);
                result.put("expired", false);
            }

            // 备注
            result.put("remarks", licenseInfo.getRemarks());

            // 授权产品
            result.put("products", licenseInfo.getProducts().stream()
                    .map(product -> {
                        Map<String, Object> productMap = new HashMap<>();
                        productMap.put("id", product.getId());
                        productMap.put("name", product.getName());
                        productMap.put("points", product.getPoints());
                        return productMap;
                    })
                    .collect(Collectors.toList()));

            // 授权定额库
            result.put("quotaLibraries", licenseInfo.getQuotaLibraries().stream()
                    .map(quota -> {
                        Map<String, Object> quotaMap = new HashMap<>();
                        quotaMap.put("id", quota.getId());
                        quotaMap.put("name", quota.getName());
                        quotaMap.put("version", quota.getVersion());
                        return quotaMap;
                    })
                    .collect(Collectors.toList()));

            // 验证时间
            if (licenseInfo.getLastValidationTime() != null) {
                result.put("lastValidationTime", licenseInfo.getLastValidationTime().format(formatter));
            }

            result.put("status", "SUCCESS");

        } catch (Exception e) {
            result.put("status", "ERROR");
            result.put("error", e.getMessage());
            result.put("valid", false);
        }

        return result;
    }

    /**
     * 重新验证许可证
     */
    @WriteOperation
    public Map<String, Object> revalidateLicense() {
        Map<String, Object> result = new HashMap<>();

        try {
            licenseService.revalidateLicense();
            result.put("status", "SUCCESS");
            result.put("message", "许可证重新验证成功");
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        } catch (Exception e) {
            result.put("status", "ERROR");
            result.put("message", "许可证重新验证失败: " + e.getMessage());
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }

        return result;
    }
}
