package cn.savas.hub.module.client.websocket;

import cn.savas.hub.framework.websocket.core.listener.WebSocketMessageListener;
import cn.savas.hub.module.client.websocket.message.ClientSendMessage;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

/**
 * <AUTHOR>
 * @date 2024/11/8 15:04
 */
@Component
public class ClientWebSocketMessageListener implements WebSocketMessageListener<ClientSendMessage> {
    @Override
    public void onMessage(WebSocketSession session, ClientSendMessage message) {
        System.out.println(session);
        System.out.println(message);
    }

    @Override
    public String getType() {
        return "client-message-send";
    }
}
