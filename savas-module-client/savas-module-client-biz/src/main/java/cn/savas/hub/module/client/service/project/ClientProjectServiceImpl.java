package cn.savas.hub.module.client.service.project;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.client.util.V9PackageUtil;
import cn.savas.hub.framework.common.client.util.VersionInfo;
import cn.savas.hub.framework.common.enums.UserTypeEnum;
import cn.savas.hub.framework.common.util.collection.TreeDataUtil;
import cn.savas.hub.framework.common.util.io.FileTypeUtils;
import cn.savas.hub.framework.common.util.object.BeanUtils;
import cn.savas.hub.framework.mybatis.core.util.SQLiteDataSourceUtil;
import cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils;
import cn.savas.hub.framework.web.core.util.WebFrameworkUtils;
import cn.savas.hub.framework.websocket.core.sender.WebSocketMessageSender;
import cn.savas.hub.module.bpm.api.client.BpmClientApi;
import cn.savas.hub.module.bpm.api.client.dto.ClientFlowDataDTO;
import cn.savas.hub.module.bpm.api.client.dto.ClientFlowStateDTO;
import cn.savas.hub.module.bpm.enums.oa.ClientBpmModelEnum;
import cn.savas.hub.module.client.api.project.ClientProjectFileApi;
import cn.savas.hub.module.client.controller.desktop.project.vo.*;
import cn.savas.hub.module.client.convert.project.ClientProjectConvert;
import cn.savas.hub.module.client.dal.dataobject.*;
import cn.savas.hub.module.client.dal.mapper.*;
import cn.savas.hub.module.client.dal.redis.RedisKeyConstants;
import cn.savas.hub.module.client.enums.project.ProjectSumFileSourceEnum;
import cn.savas.hub.module.client.enums.system.CSocketMessageTypeEnum;
import cn.savas.hub.module.client.service.flow.ClientFlowService;
import cn.savas.hub.module.client.service.system.ClientPermsService;
import cn.savas.hub.module.client.service.system.ClientScriptService;
import cn.savas.hub.module.collect.api.CollectDataApi;
import cn.savas.hub.module.collect.api.dto.*;
import cn.savas.hub.module.infra.api.config.ConfigApi;
import cn.savas.hub.module.infra.api.file.FileApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.client.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:37
 */
@Slf4j
@Service
public class ClientProjectServiceImpl implements ClientProjectService {
    private static final String CLIENT_ENG_VISIBLE = "client.eng-visible";
    @Resource
    private ClientProjectFileMapper clientProjectFileMapper;
    @Resource
    private ClientProjectFileSummaryMapper clientProjectFileSummaryMapper;
    @Resource
    private FileApi fileApi;
    @Resource
    private ClientProjectMapper clientProjectMapper;
    @Resource
    private BpmClientApi bpmClientApi;
    @Resource
    private CollectDataApi collectDataApi;
    @Resource
    private ClientProjectSettingMapper projectSettingMapper;
    @Resource
    private WebSocketMessageSender webSocketMessageSender;
    @Resource
    private ClientEngineeringMapper clientEngineeringMapper;
    @Resource
    private ConfigApi configApi;
    @Resource
    private ClientPermsService clientPermsService;
    @Resource
    @Lazy
    private ClientProjectFileApi clientProjectFileApi;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private ClientFlowService clientFlowService;
    @Resource
    private ClientProjectUserSettingMapper clientProjectUserSettingMapper;
    @Resource
    private SQLiteDataSourceUtil sqliteDataSourceUtil;
    @Resource
    private ClientScriptService clientScriptService;


    private static final class FileInfo {
        private final String name;
        private final String path;
        private final Long projectId;
        private final ClientProjectFileDO projectFile;
        private final ClientProjectFileSummaryDO fileSummary;
        public FileInfo(String name, String path, Long projectId,
                        ClientProjectFileDO projectFile, ClientProjectFileSummaryDO fileSummary) {
            this.name = name;
            this.path = path;
            this.projectId = projectId;
            this.projectFile = projectFile;
            this.fileSummary = fileSummary;
        }
    }

    @Override
    public ClientProjectDO getProjectByPrjId(Long projectId) {
        ClientProjectDO projectDO = clientProjectMapper.selectByPrjId(projectId);
        if (projectDO == null) {
            throw exception(PROJECT_NOT_EXISTS);
        }
        return projectDO;
    }

    @Override
    public ClientProjectDO getProjectByOriginalId(Long originalId) {
        ClientProjectDO projectDO = clientProjectMapper.selectByOriginalId(originalId);
        if (projectDO == null) {
            throw exception(PROJECT_NOT_EXISTS);
        }
        return projectDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadProjectFile(Long prjId, String fileName, byte[] content, Integer compiledversion) {
        ClientProjectFileDO prjFileDO = clientProjectFileMapper.selectIActive(prjId);
        if (prjFileDO != null) {
            clientProjectFileMapper.dull(prjFileDO.getId());
        }
        String fileUrl = fileApi.createFile(fileName, null, content);
        ClientProjectFileDO projectFileDO = new ClientProjectFileDO();
        projectFileDO.setPath(StringUtils.substringAfterLast(fileUrl, "/"));
        projectFileDO.setSize(content.length);
        projectFileDO.setUrl(fileUrl);
        projectFileDO.setName(fileName);
        projectFileDO.setProjectId(prjId);
        projectFileDO.setVersion(compiledversion);
        projectFileDO.setIsActive(Boolean.TRUE);
        clientProjectFileMapper.insert(projectFileDO);
        // 盖汇总文件
        ClientProjectFileSummaryDO prjFileSummaryDO = clientProjectFileSummaryMapper.selectActiveByPrjId(prjId);
        if (prjFileSummaryDO != null) {
            return;
        }
        String summaryFileName = FileUtil.getPrefix(fileName) + "-summary" + "." + FileUtil.getSuffix(fileName);
        String summaryFileUrl = fileApi.createFile(summaryFileName, null, content);
        ClientProjectFileSummaryDO newPrjFileSummaryDO = new ClientProjectFileSummaryDO();
        newPrjFileSummaryDO.setPath(StringUtils.substringAfterLast(summaryFileUrl, "/"));
        newPrjFileSummaryDO.setSize(content.length);
        newPrjFileSummaryDO.setUrl(summaryFileUrl);
        newPrjFileSummaryDO.setName(summaryFileName);
        newPrjFileSummaryDO.setProjectId(prjId);
        newPrjFileSummaryDO.setVersion(1);
        newPrjFileSummaryDO.setIsActive(Boolean.TRUE);
        newPrjFileSummaryDO.setProjectFileId(projectFileDO.getId());
        newPrjFileSummaryDO.setSource(ProjectSumFileSourceEnum.UPLOAD_PROJECT_FILE.name());
        clientProjectFileSummaryMapper.insert(newPrjFileSummaryDO);
    }

    @Override
    public void downloadProjectFile(HttpServletResponse response, ClientDownLoadFileReqVO reqVO) throws Exception {
        // 获取文件信息（汇总文件或个人文件）
        FileInfo fileInfo = getFileInfo(reqVO.getFilePath());
        // 校验下载权限
        if (!hasDownloadPermission(fileInfo.projectFile, fileInfo.fileSummary)) {
            throw exception(PROJECT_FILE_NO_PERMISSION);
        }
        // 处理文件内容，包括版本检查和更新
        byte[] fileContent = processFileContent(fileInfo, reqVO);
        // 将处理后的文件作为附件输出到响应
        FileTypeUtils.writeAttachment(response, fileInfo.name, fileContent);
    }

    /**
     * 获取文件信息，优先检查汇总文件，若不存在则检查个人文件
     * @param filePath 文件路径
     * @return FileInfo 包含文件元数据的记录对象
     */
    private FileInfo getFileInfo(String filePath) {
        // 查询汇总文件
        ClientProjectFileSummaryDO fileSummary = clientProjectFileSummaryMapper.selectByPath(filePath);
        if (fileSummary != null) {
            return new FileInfo(fileSummary.getName(), fileSummary.getPath(), fileSummary.getProjectId(), null, fileSummary);
        }
        // 如果汇总文件不存在，查询个人文件
        ClientProjectFileDO projectFile = clientProjectFileMapper.selectByPath(filePath);
        if (projectFile == null) {
            throw exception(PROJECT_FILE_NOT_EXISTS);
        }
        return new FileInfo(projectFile.getName(), projectFile.getPath(), projectFile.getProjectId(), projectFile, null);
    }

    /**
     * 处理文件内容，包括读取、版本检查、更新和数据处理
     * @param reqVO 下载请求参数
     * @return 处理后的文件内容字节数组
     * @throws IOException 如果文件读取或更新失败，抛出异常
     */
    private byte[] processFileContent(FileInfo fileInfo, ClientDownLoadFileReqVO reqVO) throws Exception {
        // 读取文件内容为字节数组
        byte[] fileContent = fileApi.getFileContent(fileInfo.path);
        // 解析文件版本信息
        VersionInfo versionInfo = V9PackageUtil.getVersionFromFile(fileContent);
        // 如果是汇总文件，检查版本并尝试更新
        if (fileInfo.fileSummary != null && !clientScriptService.checkProjectVersion(versionInfo.getVersion())) {
            // 执行更新脚本
            clientScriptService.executeUpdateScript(fileInfo.projectId, versionInfo.getVersion());
            // 获取更新后的汇总文件
            ClientProjectFileSummaryDO newSummary = clientProjectFileSummaryMapper.selectActiveByPrjId(fileInfo.projectId);
            // 重新读取更新后的文件内容
            fileContent = fileApi.getFileContent(newSummary.getPath());
            versionInfo = V9PackageUtil.getVersionFromFile(fileContent);
            if (!clientScriptService.checkProjectVersion(versionInfo.getVersion())) {
                throw exception(CLIENT_UPDATE_SCRIPT_VERSION_TOO_LOW);
            }
        }
        // 删除指定工程数据并重新压缩
        return delEngDataZip(fileContent, reqVO.getEngIdList(), reqVO.getProduct(), versionInfo);
    }

    /**
     * 删除指定工程数据并重新压缩
     * @param fileContent 文件内容
     * @param engIdList 工程id
     * @return 处理后的压缩文件
     */
    private byte[] delEngDataZip(byte[] fileContent, List<Long> engIdList, String product, VersionInfo versionInfo) {
        if (CollectionUtils.isEmpty(engIdList)) {
            return fileContent;
        }
        String sqliteUrl = V9PackageUtil.unZipFileToDB(fileContent);
        return sqliteDataSourceUtil.executeWithSQLite(sqliteUrl, ()->{
            // 删除指定工程数据
            collectDataApi.deleteDataByEngId(engIdList, product);
            // 重新压缩
            return V9PackageUtil.zipDBToFile(versionInfo, sqliteUrl, null);
        });
    }

    private boolean hasDownloadPermission(ClientProjectFileDO projectFile, ClientProjectFileSummaryDO fileSummary) {
        Set<Long> projectIdList = clientPermsService.getAuthorizedProjectIds();
        // 获取要检查的项目ID
        Long projectId = null;
        if (fileSummary != null) {
            projectId = fileSummary.getProjectId();
        } else if (projectFile != null) {
            projectId = projectFile.getProjectId();
        }
        // 检查权限
        return projectId != null && projectIdList.contains(projectId);
    }

    @Override
    public ClientProjectFileVersionVO getProjectFileVersion(Long engProjectId) {
        ClientProjectFileVersionVO respVO = new ClientProjectFileVersionVO();
        // 个人项目文件版本
        ClientProjectFileDO projectFile = clientProjectFileMapper.selectIActive(engProjectId);
        if (projectFile != null) {
            respVO.setCompiledversion(projectFile.getVersion());
        }
        // 汇总项目文件版本
        ClientProjectFileSummaryDO projectSumFile = clientProjectFileSummaryMapper.selectActiveByPrjId(engProjectId);
        if (projectSumFile != null) {
            respVO.setSubmittedversion(projectSumFile.getVersion());
        }
        respVO.setHostmodelid(engProjectId);
        return respVO;
    }

    @Override
    public List<ClientEngineeringVO> getEngineeringTree(Long engProjectId, Long originalid) {
        // 获取和当前登录人有关的项目
        Set<Long> authorizedProjectIds = clientPermsService.getAuthorizedProjectIds();
        if (!authorizedProjectIds.contains(engProjectId)) {
            throw exception(PROJECT_NO_PERMISSION);
        }
        ClientProjectDO projectDO = clientProjectMapper.selectByPrjId(engProjectId);
        if (projectDO == null) {
            throw exception(PROJECT_NOT_EXISTS);
        }
        // 获取工程数据
        List<ClientEngineeringVO> engineeringList = ClientProjectConvert.INSTANCE.convert7List(clientEngineeringMapper.selectByPrjId(engProjectId));
        // 获取流程相关执行人
        ClientFlowDataDTO.FlowDataContainer flowData = bpmClientApi.getFlowData(originalid);
        Map<Long, List<ClientFlowDataDTO.FlowNode>> flowEngMap = flowData.getNodes().stream().collect(
                Collectors.groupingBy(ClientFlowDataDTO.FlowNode::getNodeHostmodel)
        );
        List<ClientEngineeringVO> resultList = new ArrayList<>(engineeringList);
        resultList.add(ClientProjectConvert.INSTANCE.convert8(projectDO));
        List<ClientEngineeringVO> engineeringTreeList = TreeDataUtil.baseTreeV2(resultList, true, ClientEngineeringVO::getId, ClientEngineeringVO::getPid, ClientEngineeringVO::setItems, ClientEngineeringVO::getItems);
        // 是否可以看到无关的工程，数据权限大于工程流程权限。
        if (!clientPermsService.hasProjectDataPerms(engProjectId) && !configApi.getConfigVisibleByKey(CLIENT_ENG_VISIBLE)) {
            this.filterEngPermissions(engineeringTreeList, flowEngMap, SecurityFrameworkUtils.getLoginUserId());
        }
        return engineeringTreeList;
    }

    @Override
    public List<ClientEngineeringVO> getEngineeringList(Long engProjectId) {
        ClientProjectDO projectDO = clientProjectMapper.selectByPrjId(engProjectId);
        // 获取工程数据
        List<ClientEngineeringVO> engineeringList = ClientProjectConvert.INSTANCE.convert7List(clientEngineeringMapper.selectByPrjId(engProjectId));
        List<ClientEngineeringVO> resultList = new ArrayList<>(engineeringList);
        resultList.add(ClientProjectConvert.INSTANCE.convert8(projectDO));
        return resultList;
    }

    /**
     * 工程权限过滤
     */
    private void filterEngPermissions(List<ClientEngineeringVO> engineeringList, Map<Long, List<ClientFlowDataDTO.FlowNode>> flowEngMap, Long loginUserId) {
        engineeringList.removeIf(eng -> !hasPermission(eng, flowEngMap, loginUserId));
    }

    /**
     * 判断用户是否对指定工程有权限
     */
    private boolean hasPermission(ClientEngineeringVO eng, Map<Long, List<ClientFlowDataDTO.FlowNode>> flowEngMap, Long loginUserId) {
        // 当前工程的流程节点集合
        List<ClientFlowDataDTO.FlowNode> flowEng = flowEngMap.getOrDefault(eng.getId(), new ArrayList<>());

        // 如果是节点负责人，直接有权限 || 如果是节点编制人，直接有权限
        if (isDirector(eng, loginUserId) || isOrganizer(loginUserId, flowEng)) {
            return true;
        }

        // 检查是否分配了节点权限
        boolean hasNodePermission = flowEng.stream().anyMatch(node -> node.getNodeUserId().equals(loginUserId));

        // 单位工程：需有节点权限
        if (ClientClassIdEnum.UNITENG.getCode().equals(eng.getAlias())) {
            return hasNodePermission;
        }

        // 非单位工程：递归处理子节点
        if (!CollectionUtils.isEmpty(eng.getItems())) {
            filterEngPermissions(eng.getItems(), flowEngMap, loginUserId);
        }

        // 如果没有子节点且无节点权限，则无权限
        return !CollectionUtils.isEmpty(eng.getItems()) || hasNodePermission;
    }

    /**
     * 判断是否是编制人
     * @param loginUserId
     * @param flowEng
     * @return
     */
    private boolean isOrganizer(Long loginUserId, List<ClientFlowDataDTO.FlowNode> flowEng) {
        return flowEng.stream().anyMatch(node->ClientBpmModelEnum.BZ.getNodeName().equals(node.getNodeName()) && loginUserId.equals(node.getNodeUserId()));
    }

    /**
     * 判断是否为负责人
     */
    private boolean isDirector(ClientEngineeringVO eng, Long loginUserId) {
        return eng.getDirectorid().equals(loginUserId);
    }

    @Override
    public ClientProjectFileInfoVO getProjectFile(Long engProjectId) {
        ClientProjectFileInfoVO fileInfoVO = new ClientProjectFileInfoVO();
        // 个人文件地址
        ClientProjectFileDO projectFile = clientProjectFileMapper.selectActiveByPrjId(engProjectId, WebFrameworkUtils.getLoginUserId());
        if (projectFile != null) {
            if (!Long.valueOf(projectFile.getCreator()).equals(SecurityFrameworkUtils.getLoginUserId())) {
                throw exception(PROJECT_FILE_NO_PERMISSION);
            }
            fileInfoVO.setFilename(projectFile.getName());
            fileInfoVO.setPrivatefilepath(projectFile.getPath());
        }

        // 汇总文件地址
        ClientProjectFileSummaryDO projectFileSummary = clientProjectFileSummaryMapper.selectActiveByPrjId(engProjectId);
        if (projectFileSummary == null) {
            throw exception(PROJECT_FILE_NOT_EXISTS);
        }
        fileInfoVO.setFilename(projectFileSummary.getName().replace("-summary", ""));
        fileInfoVO.setPublicfilepath(projectFileSummary.getPath());
        return fileInfoVO;
    }

    @Override
    public void bindDefinition(Long projectid, String processDefinitionId) {
        clientProjectMapper.bindDefinition(projectid, processDefinitionId);
    }

    @Override
    public void mergeProjectFile(ClientMergeProjectFileReqVO reqVO, String product) {
        // 个人项目文件
        Long loginUserId = reqVO.getUserid() != null?reqVO.getUserid(): WebFrameworkUtils.getLoginUserId();
        ClientProjectFileDO projectFileDO = clientProjectFileMapper.selectActiveByPrjId(reqVO.getProjectid(), loginUserId);
        if (projectFileDO == null) {
            return;
        }
        // 汇总项目文件
        ClientProjectFileSummaryDO projectFileSummaryDO = clientProjectFileSummaryMapper.selectActiveByPrjId(reqVO.getProjectid());
        ClientMergeProjectFileDTO mergeDTO = new ClientMergeProjectFileDTO();
        mergeDTO.setMainPath(projectFileSummaryDO.getPath());
        mergeDTO.setSlavePath(projectFileDO.getPath());
        mergeDTO.setWbsIds(reqVO.getHostmodelids());
        mergeDTO.setProjectId(reqVO.getProjectid());
        mergeDTO.setProjectFileId(projectFileDO.getId());
        mergeDTO.setIncreaseEngVersion(reqVO.isIncreaseEngVersion());
        mergeDTO.setProduct(product);
        try {
            collectDataApi.mergeProjectFile(mergeDTO);
        } catch (Exception e) {
            log.error("[mergeProjectFile]合并项目文件失败", e);
            throw exception(PROJECT_FILE_MERGE_FAIL);
        }
    }

    @Override
    public void copyPrjUploadEngineering(ClientEngineeringReqVO.EngineeringClass req) {
        if (CollectionUtils.isEmpty(req.getItems())) {
            return;
        }
        clientEngineeringMapper.insertBatch(ClientProjectConvert.INSTANCE.convert9List(req.getItems()));
    }

    @Override
    public void updateDirector(UpdateDirectorIdReqVO req) {
        Long projectid = req.getProjectid();
        List<UpdateDirectorIdReqVO.Items> items = req.getItems();
        // 流程锁检查
        for (UpdateDirectorIdReqVO.Items item : items) {
            String key = RedisKeyConstants.CLIENT_LOCK_FLOW + ":" + projectid + ":" + item.getEngid();
            String value = redisTemplate.opsForValue().get(key);
            if (StringUtils.isNotBlank(value)) {
                throw exception(PROJECT_FLOW_LOCKED);
            }
        }
        // 更新负责人
        for (UpdateDirectorIdReqVO.Items item : items) {
            // 项目层级单独处理
            if (projectid.equals(item.getEngid())) {
                clientProjectMapper.updateDirectorId(projectid, item.getDirectorid(), item.getDirectorname());
                continue;
            }
            clientEngineeringMapper.updateDirectorId(projectid, item.getEngid(), item.getDirectorid(), item.getDirectorname());
        }
        // 更新汇总文件内的负责人
        List<PrjEngineeringDTO> engDTOList = new ArrayList<>();
        items.forEach(e->{
            PrjEngineeringDTO engineeringDTO = new PrjEngineeringDTO();
            engineeringDTO.setEngId(e.getEngid());
            engineeringDTO.setEngDirectorname(e.getDirectorname());
            engineeringDTO.setEngDirectorid(e.getDirectorid());
            engDTOList.add(engineeringDTO);
        });
        clientProjectFileApi.changePrjFileSumDs(projectid, () -> {
            collectDataApi.updateDirectorId(projectid, engDTOList);
            return true;
        }, true, ProjectSumFileSourceEnum.UPDATE_ENG_LEADER);
    }

    @Override
    public void calculateProjectData(ClientMergeProjectFileReqVO reqVO) {
        // 汇总项目文件
        ClientProjectFileSummaryDO projectFileSummaryDO = clientProjectFileSummaryMapper.selectActiveByPrjId(reqVO.getProjectid());
        try {
            CalculateReqDTO calculateReqDTO = new CalculateReqDTO();
            calculateReqDTO.setId(projectFileSummaryDO.getId());
            calculateReqDTO.setProjectId(projectFileSummaryDO.getProjectId());
            calculateReqDTO.setPath(projectFileSummaryDO.getPath());
            calculateReqDTO.setProjectFileId(projectFileSummaryDO.getProjectFileId());
            calculateReqDTO.setHostmodelids(reqVO.getHostmodelids());
//            calculateReqDTO.setProduct(reqVO.getProduct());
            collectDataApi.calculateProjectData(calculateReqDTO);
        }catch (Exception e) {
            log.error("[calculateProjectData]计算项目数据失败", e);
            throw exception(PROJECT_FILE_CALCULATE_FAIL);
        }
    }

    @Override
    public List<ClientEngineeringDO> getEngByIds(Long projectid, List<Long> hostmodelids) {
        if (CollectionUtils.isEmpty(hostmodelids)) {
            return Collections.emptyList();
        }
        return clientEngineeringMapper.selectList(new LambdaQueryWrapper<ClientEngineeringDO>()
                .eq(ClientEngineeringDO::getEngProjectId, projectid)
                .in(ClientEngineeringDO::getEngId, hostmodelids)
        );
    }

    @Override
    public List<ClientProjectFileDO> getPersonalProjectFile(Long projectid, Collection<Long> userids) {
        if (CollectionUtils.isEmpty(userids)) {
            return Collections.emptyList();
        }
        return clientProjectFileMapper.selectList(new LambdaQueryWrapper<ClientProjectFileDO>()
                .in(ClientProjectFileDO::getCreator, userids)
                .eq(ClientProjectFileDO::getProjectId, projectid)
                .eq(ClientProjectFileDO::getIsActive, Boolean.TRUE)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProjectUserSetting(ClientProjectUserSettingReqVO reqVO) {
        List<ClientProjectUserSettingReqVO.Items> items = reqVO.getItems();
        Long projectId = reqVO.getProjectid();
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Set<Long> paraIdSet = items.stream().map(ClientProjectUserSettingReqVO.Items::getId).collect(Collectors.toSet());
        // 删除旧数据
        clientProjectUserSettingMapper.deleteOldData(projectId, paraIdSet, userId);
        // 插入新数据
        List<ClientProjectUserSettingDO> userSettingList = ClientProjectConvert.INSTANCE.convert11List(items);
        if (CollectionUtils.isEmpty(userSettingList)) {
            return;
        }
        // 补充数据
        for (ClientProjectUserSettingDO settingDO : userSettingList) {
            settingDO.setProjectId(projectId);
            settingDO.setUserId(userId);
        }
        clientProjectUserSettingMapper.insertBatch(userSettingList);
    }

    @Override
    public List<ClientProjectUserSettingRespVO> getProjectUserSetting(Long projectId) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        List<ClientProjectUserSettingDO> userSettingList = clientProjectUserSettingMapper.selectList(new LambdaQueryWrapper<ClientProjectUserSettingDO>()
                .eq(ClientProjectUserSettingDO::getProjectId, projectId)
                .eq(ClientProjectUserSettingDO::getUserId, userId)
        );
        if (CollectionUtils.isEmpty(userSettingList)) {
            return Collections.emptyList();
        }
        return ClientProjectConvert.INSTANCE.convert12List(userSettingList);
    }

    @Override
    public String getSumFileByVersion(Long engProjectId, Integer version) {
        ClientProjectFileSummaryDO fileSummaryDO = clientProjectFileSummaryMapper.selectByVersion(engProjectId, version);
        if (fileSummaryDO == null) {
            throw exception(PROJECT_SUMMARY_FILE_NOT_EXISTS);
        }
        if (Objects.equals(SecurityFrameworkUtils.getLoginUserId(), Long.valueOf(fileSummaryDO.getCreator()))) {
            return fileSummaryDO.getPath();
        } else {
            throw exception(PROJECT_SUMMARY_FILE_NO_PERMISSION);
        }
    }

    @Override
    public List<ClientTestEngineeringVO> getTestEngineering(Long projectid) {
        return clientProjectFileApi.changePrjFileSumDs(projectid, () -> {
            List<PrjTestEngineeringDTO> testEngineering = collectDataApi.getTestEngineering(projectid);
            return ClientProjectConvert.INSTANCE.convertList13(testEngineering);
        });
    }

    @Override
    public void updateEngineeringData(ClientUpdateEngDataReqVO reqVO) {
        List<ClientUpdateEngDataReqVO.UpdateEngBaseVO> items = reqVO.getItems();
        items.forEach(item->{
            if(item instanceof ClientUpdateEngDataReqVO.UpdateProjectVO){
                ClientUpdateEngDataReqVO.UpdateProjectVO projectVO = (ClientUpdateEngDataReqVO.UpdateProjectVO) item;
                clientProjectMapper.updateProject(projectVO);
                // 更换汇总文件内的项目数据
                clientProjectFileApi.changePrjFileSumDs(projectVO.getProjectid(), () -> {
                    collectDataApi.updateProjectData(ClientProjectConvert.INSTANCE.convert14(projectVO));
                    return true;
                }, true, ProjectSumFileSourceEnum.UPDATE_ENG_DATA);
            }
        });
    }

    @Override
    public void uploadHostModelTableData(ClientProjectCreateReqVO createReqVO) {
        List<ClientProjectCreateReqVO.ClientProjectCreateReqDetailVO> items = createReqVO.getItems();
        Map<Long, List<ClientProjectCreateReqVO.ClientProjectCreateReqDetailVO>> itemsMap = items.stream().collect(Collectors.groupingBy(ClientProjectCreateReqVO.ClientProjectCreateReqDetailVO::getAlias));
        for (Map.Entry<Long, List<ClientProjectCreateReqVO.ClientProjectCreateReqDetailVO>> itemEntry : itemsMap.entrySet()) {
            ClientClassIdEnum anEnum = ClientClassIdEnum.getByCode(itemEntry.getKey());
            if (anEnum == null) {
                continue;
            }
            switch (anEnum) {
                case PROJECTENG:
                    createProject(itemEntry.getValue().get(0));
                    break;
                default:
            }
        }
    }


    /**
     * 创建项目
     * @param createReqDetailVO
     * @return
     */
    private Integer createProject(ClientProjectCreateReqVO.ClientProjectCreateReqDetailVO createReqDetailVO) {
        return clientProjectMapper.insert(ClientProjectConvert.INSTANCE.convert2(createReqDetailVO));
    }

    @Override
    public List<ClientProjectRespVO> getProjectList() {
        // 1. 获取当前用户有权限的项目 ID
        Set<Long> projectIds = clientPermsService.getAuthorizedProjectIds();
        if (CollectionUtils.isEmpty(projectIds)) {
            return Collections.emptyList();
        }
        // 2. 查询项目列表
        List<ClientProjectDO> projectList = clientProjectMapper.selectProjectListByIds(projectIds);
        // 3. 转换为 VO
        List<ClientProjectRespVO> respVOS = ClientProjectConvert.INSTANCE.convertList1(projectList);
        // 4. 设置流程状态描述
        setFlowStateDescriptions(respVOS);
        // 5. 合并多个版本的项目
        return mergeProjectVersions(respVOS);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProject(Long prjId) {
        ClientProjectDO projectDO = clientProjectMapper.selectByPrjId(prjId);
        if (projectDO == null) {
            throw exception(PROJECT_NOT_EXISTS);
        }
        // 删除锁
        clientFlowService.delFlowLock(prjId);
        // 获取流程数据
        ClientFlowDataDTO.FlowDataContainer flowData = bpmClientApi.getFlowData(projectDO.getProjectId());
        // 删除个人项目文件
        clientProjectFileMapper.deleteByPrjId(projectDO.getProjectId());
        // 删除汇总项目文件
        clientProjectFileSummaryMapper.deleteByPrjId(projectDO.getProjectId());
        // 删除项目配置
        projectSettingMapper.deleteByPrjId(projectDO.getProjectId());
        // 删除流程相关数据
        bpmClientApi.delFlowDataByPrjId(prjId);
        // 删除工程信息
        clientEngineeringMapper.deleteByPrjId(prjId);
        // 删除项目个人配置
        clientProjectUserSettingMapper.deleteByPrjId(prjId);
        // 删除项目
        clientProjectMapper.deleteByPrjId(prjId);
        // 通知相关人
        List<ClientFlowDataDTO.FlowNode> nodes = flowData.getNodes();
        Set<Long> userIdSet = nodes.stream().map(ClientFlowDataDTO.FlowNode::getNodeUserId).collect(Collectors.toSet());
        userIdSet.forEach(userId -> {
            webSocketMessageSender.sendAck(UserTypeEnum.DESKTOP.getValue(), userId, CSocketMessageTypeEnum.DELETE_PROJECT.getCode(), "项目[" + projectDO.getName() + "]已被删除");
        });
    }

    @Override
    public boolean updateProject(ClientProjectCreateReqVO createReqVO) {
        ClientProjectCreateReqVO.ClientProjectCreateReqDetailVO createReqDetailVO =
                createReqVO.getItems().stream()
                        .filter(item -> item.getAlias().equals(ClientClassIdEnum.PROJECTENG.getCode()))
                        .findFirst()
                        .orElseThrow(() -> exception(PROJECT_NOT_EXISTS));
        clientProjectMapper.update(new LambdaUpdateWrapper<ClientProjectDO>()
                .eq(ClientProjectDO::getProjectId, createReqDetailVO.getProjectid())
                .set(ClientProjectDO::getName, createReqDetailVO.getName())
        );
        return true;
    }

    /**
     * 设置流程状态
     * @param projectList
     */
    private void setFlowStateDescriptions(List<ClientProjectRespVO> projectList) {
        List<Long> projectIds = projectList.stream().map(ClientProjectRespVO::getOriginalid).collect(Collectors.toList());
        List<ClientFlowStateDTO> stateList = bpmClientApi.getPrjFlowState(projectIds);
        Map<Long, ClientFlowStateDTO> stateMap = stateList.stream()
                .collect(Collectors.toMap(ClientFlowStateDTO::getNodeProjectId, Function.identity()));

        for (ClientProjectRespVO vo : projectList) {
            ClientFlowStateDTO state = stateMap.get(vo.getOriginalid());
            if (state != null) {
                if (state.getFinishedCount() > 0) {
                    vo.setDescription("已完成");
                } else if (state.getCompilingCount() > 0) {
                    vo.setDescription("编制中");
                }
                vo.setStyle(1); // 非轻协同
            } else {
                vo.setStyle(2); // 轻协同
            }
        }
    }

    /**
     * 合并多个版本的项目
     * @param projects
     * @return
     */
    private List<ClientProjectRespVO> mergeProjectVersions(List<ClientProjectRespVO> projects) {
        Map<Long, List<ClientProjectRespVO>> grouped = projects.stream()
                .collect(Collectors.groupingBy(ClientProjectRespVO::getOriginalid, LinkedHashMap::new, Collectors.toList()));

        List<ClientProjectRespVO> result = new ArrayList<>();
        for (Map.Entry<Long, List<ClientProjectRespVO>> entry : grouped.entrySet()) {
            List<ClientProjectRespVO> versions = entry.getValue();
            if (versions.size() > 1) {
                ClientProjectRespVO latest = versions.get(versions.size() - 1);
                ClientProjectRespVO merged = new ClientProjectRespVO();
                merged.setAlias(ClientClassIdEnum.PROJECT.getCode());
                merged.setOperate(latest.getOperate());
                merged.setCreateusername(latest.getCreateusername());
                merged.setCreatetime(latest.getCreatetime());
                merged.setModelstate(latest.getModelstate());
                merged.setId(latest.getId());
                merged.setProjectid(latest.getOriginalid());
                merged.setName(latest.getName());
                merged.setSortid(latest.getSortid());
                Collections.reverse(versions);
                merged.setItems(versions);
                result.add(merged);
            } else {
                result.addAll(versions);
            }
        }
        return result;
    }
}
