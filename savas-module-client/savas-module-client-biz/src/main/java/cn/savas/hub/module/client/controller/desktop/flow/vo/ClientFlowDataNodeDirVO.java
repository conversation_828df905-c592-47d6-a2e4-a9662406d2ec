package cn.savas.hub.module.client.controller.desktop.flow.vo;

import cn.savas.hub.module.client.controller.desktop.flow.vo.resolver.ClientFlowDataBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:26
 */
@Data
public class ClientFlowDataNodeDirVO extends ClientFlowDataBaseVO {
    @Schema(description = "对应节点ID")
    private Long parentid;

    @Schema(description = "来源节点ID")
    private Long sourceflownodeid;

    @Schema(description = "目标节点ID")
    private Long targetflownodeid;

    @Schema(description = "备注说明")
    private String description;

    @Schema(description = "路径提交次数")
    private Integer submittedcount;

    @Schema(description = "类型（0：流程执行路径---顺序，1：流程执行路径---同时）")
    private Integer style;
}
