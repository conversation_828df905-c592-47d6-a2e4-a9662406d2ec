package cn.savas.hub.module.client.service.flow;

import cn.savas.hub.module.client.controller.desktop.flow.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/22 17:07
 */
public interface ClientFlowService {
    /**
     * 保存流程数据
     * @param saveFlowDataReqVO
     */
    void saveFlowData(ClientFlowDataReqVO saveFlowDataReqVO);

    /**
     * 获取流程数据
     * @param originalid
     * @return
     */
    List<FlowNodeVO> getFlowData(Long originalid);

    /**
     * 获取流程模版
     * @return
     */
    List<ClientFlowTemplateRespVO> getFlowTemplate();

    List<FlowScheduleRespVO> getFlowSchedule(Long originalid);

    /**
     * 获取校审意见
     * @param originalid
     * @return
     */
    List<FlowSuggestionRespVO> getFlowSuggestion(Long originalid);

    /**
     * 锁定流程
     * @param reqVO
     */
    void lockFlow(ClientFlowLockReqVO reqVO);

    /**
     * 解锁流程
     * @param reqVO
     */
    void unlockFlow(ClientFlowUnLockReqVO reqVO);

    /**
     * 删除流程锁
     */
    void delFlowLock(Long projectId);

    /**
     * 强制合稿
     * @param reqVO
     */
    void forceMergeFlow(ClientFlowForceMergeReqVO reqVO, String product);

    /**
     * 绑定汇总文件版本
     * @param reqVO
     */
    void bindSumFileVersion(ClientFlowBindSumFileVersionReqVO reqVO);
}
