package cn.savas.hub.module.client.framework.httpmessage.serializer.modifier;

import cn.savas.hub.module.client.framework.httpmessage.serializer.CTimestampLocalDateTimeSerializer;
import cn.savas.hub.module.client.framework.httpmessage.serializer.NullIntegerSerializer;
import cn.savas.hub.module.client.framework.httpmessage.serializer.NullLongSerializer;
import cn.savas.hub.module.client.framework.httpmessage.serializer.NullStringSerializer;
import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/9 18:07
 */
public class CustomBeanSerializerModifier extends BeanSerializerModifier {
    private static final JsonSerializer<Object> NULL_STRING_SERIALIZER = new NullStringSerializer();
    private static final JsonSerializer<Object> NULL_INTEGER_SERIALIZER = new NullIntegerSerializer();
    private static final JsonSerializer<Object> NULL_LONG_SERIALIZER = new NullLongSerializer();
    private static final JsonSerializer<Object> LOCAL_DATE_TIME_SERIALIZER = new CTimestampLocalDateTimeSerializer();

    @Override
    public List<BeanPropertyWriter> changeProperties(SerializationConfig config,
                                                     BeanDescription beanDesc,
                                                     List<BeanPropertyWriter> beanProperties) {
        List<BeanPropertyWriter> newWriters = new ArrayList<>();

        for (BeanPropertyWriter writer : beanProperties) {
            JsonSerializer<?> serializer = writer.getSerializer();
            if (serializer == null) {
                JavaType writerType = writer.getType();
                if (writerType.isTypeOrSubTypeOf(String.class)) {
                    writer.assignNullSerializer(NULL_STRING_SERIALIZER);
                } else if (writerType.isTypeOrSubTypeOf(Integer.class)) {
                    writer.assignNullSerializer(NULL_INTEGER_SERIALIZER);
                } else if(writerType.isTypeOrSubTypeOf(Long.class)) {
                    writer.assignNullSerializer(NULL_LONG_SERIALIZER);
                }else if(writerType.isTypeOrSubTypeOf(LocalDateTime.class)) {
                    writer.assignSerializer(LOCAL_DATE_TIME_SERIALIZER);
                }
            }
            newWriters.add(writer);
        }
        return newWriters;
    }
}
