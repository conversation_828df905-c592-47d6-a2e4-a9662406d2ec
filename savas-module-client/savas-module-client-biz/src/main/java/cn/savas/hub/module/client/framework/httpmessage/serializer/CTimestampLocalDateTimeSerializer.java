package cn.savas.hub.module.client.framework.httpmessage.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR>
 * @date 2024/12/9 19:00
 */
public class CTimestampLocalDateTimeSerializer extends JsonSerializer<Object> {
    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            gen.writeString(((LocalDateTime) value).atZone(ZoneId.of("UTC")).toInstant().toString());
        }
    }
}
