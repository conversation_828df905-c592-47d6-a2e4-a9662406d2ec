package cn.savas.hub.module.client.service.system;

import cn.savas.hub.framework.common.enums.UserTypeEnum;
import cn.savas.hub.framework.websocket.core.sender.WebSocketMessageSender;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeReqVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeRespVO;
import cn.savas.hub.module.client.convert.system.ClientSystemConvert;
import cn.savas.hub.module.client.dal.dataobject.ClientNoticeDO;
import cn.savas.hub.module.client.dal.mapper.ClientNoticeMapper;
import cn.savas.hub.module.client.enums.system.CSocketMessageTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * <AUTHOR>
 * @date 2025/4/22 15:28
 */
@Service
public class ClientSystemServiceImpl implements ClientSystemService{
    @Resource
    private ClientNoticeMapper clientNoticeMapper;
    @Resource
    private WebSocketMessageSender webSocketMessageSender;

    @Override
    public List<ClientNoticeRespVO> getNotice() {
        List<ClientNoticeDO> noticeList = clientNoticeMapper.selectList(new LambdaQueryWrapper<ClientNoticeDO>()
                .eq(ClientNoticeDO::getNoticeTargetUserId, getLoginUserId())
                .orderByDesc(ClientNoticeDO::getNoticeDate));
        return ClientSystemConvert.INSTANCE.convert2List(noticeList);
    }

    @Override
    public void sendNotice(ClientNoticeReqVO notice) {
        clientNoticeMapper.insertBatch(ClientSystemConvert.INSTANCE.convertList(notice.getItems()));
        notice.getItems().parallelStream().forEach(item -> webSocketMessageSender.send(UserTypeEnum.DESKTOP.getValue(), item.getTargetuser(), CSocketMessageTypeEnum.TODO_TASK_CHANGE.getCode(), item.getName()));
    }
}
