package cn.savas.hub.module.client.job.project;

import cn.hutool.core.io.FileUtil;
import cn.savas.hub.framework.quartz.core.handler.JobHandler;
import cn.savas.hub.framework.tenant.core.aop.TenantIgnore;
import cn.savas.hub.module.client.api.project.ClientProjectFileApiImpl;
import cn.savas.hub.module.client.dal.redis.RedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;

/**
 * 定时清理项目汇总文件的 Job
 * <AUTHOR>
 * @date 2025/5/8 17:44
 */
@Slf4j
@Component
public class PrjSumFileCleanJob implements JobHandler {
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    @TenantIgnore
    public String execute(String param) {
        Integer count = 0;
        File dir = new File(ClientProjectFileApiImpl.DB_STORAGE_PATH);
        if (dir.exists()) {
            for (File file : dir.listFiles()) {
                String cacheKey = RedisKeyConstants.PROJECT_SUMMARY_FILE_SQLITE_URL + ":" + extractProjectIdFromFile(file.getName());
                if (!redisTemplate.hasKey(cacheKey)) {
                    FileUtil.del(file);
                    count++;
                    log.info("[cleanExpiredDbFiles] 删除过期 .db 文件: {}", file.getPath());
                }
            }
        }
        return String.format("定时清理过期的项目汇总文件数量 %s 个", count);
    }

    /**
     * 提取项目 ID:project_{projectId}.db
     * @param name
     * @return
     */
    private String extractProjectIdFromFile(String name) {
        String[] parts = name.split("_");
        if (parts.length > 1) {
            String projectId = parts[1].split("\\.")[0];
            return projectId;
        }
        return null;
    }
}
