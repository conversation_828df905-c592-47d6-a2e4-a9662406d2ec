package cn.savas.hub.module.client.service.system;

import cn.savas.hub.framework.common.enums.UserTypeEnum;
import cn.savas.hub.framework.mybatis.core.util.SQLiteDataSourceUtil;
import cn.savas.hub.framework.websocket.core.sender.WebSocketMessageSender;
import cn.savas.hub.module.client.api.project.ClientProjectFileApi;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeReqVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeRespVO;
import cn.savas.hub.module.client.convert.system.ClientSystemConvert;
import cn.savas.hub.module.client.dal.dataobject.ClientNoticeDO;
import cn.savas.hub.module.client.dal.dataobject.ClientScriptDO;
import cn.savas.hub.module.client.dal.dataobject.ClientScriptUpgradeDO;
import cn.savas.hub.module.client.dal.mapper.ClientNoticeMapper;
import cn.savas.hub.module.client.dal.mapper.ClientScriptMapper;
import cn.savas.hub.module.client.dal.redis.RedisKeyConstants;
import cn.savas.hub.module.client.enums.project.ProjectSumFileSourceEnum;
import cn.savas.hub.module.client.enums.system.CSocketMessageTypeEnum;
import cn.savas.hub.module.client.service.project.ClientProjectService;
import cn.savas.hub.module.collect.api.CollectDataApi;
import cn.savas.hub.module.infra.api.file.FileApi;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.savas.hub.module.client.enums.ErrorCodeConstants.CLIENT_UPDATE_SCRIPT_NOT_FOUND;

/**
 * <AUTHOR>
 * @date 2025/4/22 15:28
 */
@Slf4j
@Service
public class ClientScriptServiceImpl implements ClientScriptService{
    @Resource
    private ClientScriptMapper clientScriptMapper;
    @Resource
    private SQLiteDataSourceUtil sqliteDataSourceUtil;
    @Resource
    private FileApi fileApi;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private ClientProjectFileApi projectFileApi;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public boolean checkProjectVersion(String version) {
        // 先从redis获取最新版本
        String lastVersion = redisTemplate.opsForValue().get(RedisKeyConstants.CLIENT_UPDATE_SCRIPT_VERSION);
        // 如果没有，则从数据库获取
        if(lastVersion == null){
            ClientScriptDO clientScriptDO = clientScriptMapper.getLastUpdateFile();
            if(clientScriptDO != null){
                lastVersion = clientScriptDO.getVersion();
                redisTemplate.opsForValue().set(RedisKeyConstants.CLIENT_UPDATE_SCRIPT_VERSION, lastVersion);
            }else {
                return true;
            }
        }
        return comparisonVersion(version, lastVersion);
    }

    @Override
    public void uploadUpdateScript(String filePath, String fileUrl) {
        String lastVersion = sqliteDataSourceUtil.executeWithSQLite(fileApi.getFileLocalByPath(filePath), ()-> clientScriptMapper.getLastVersion());
        redisTemplate.opsForValue().set(RedisKeyConstants.CLIENT_UPDATE_SCRIPT_VERSION, lastVersion);
        ClientScriptDO clientScriptDO = ClientScriptDO.builder().path(filePath).url(fileUrl).version(lastVersion).build();
        clientScriptMapper.insert(clientScriptDO);
    }

    @Override
    public void executeUpdateScript(Long projectId, String version) {
        ClientScriptDO clientScriptDO = clientScriptMapper.selectOneByMaxDate();
        if (clientScriptDO == null) {
            throw exception(CLIENT_UPDATE_SCRIPT_NOT_FOUND);
        }
        List<String> sqlScriptList = sqliteDataSourceUtil.executeWithSQLite(fileApi.getFileLocalByPath(clientScriptDO.getPath()), () -> {
            List<ClientScriptUpgradeDO> upgradeDOList = clientScriptMapper.selectUpgradeAll();
            for (ClientScriptUpgradeDO upgradeDO : upgradeDOList) {
                if (!comparisonVersion(version, upgradeDO.getVersion())) {
                    // 获取需要执行的sql脚本
                    return clientScriptMapper.selectUpdateScript(upgradeDO.getId());
                }
            }
            return Collections.emptyList();
        });
        if (sqlScriptList.isEmpty()) {
            log.warn("检查到更新,但没有需要执行的更新脚本 projectId={}, version={}", projectId, version);
            return;
        }
        // 执行sql脚本
        executeSqlScript(projectId, sqlScriptList, clientScriptDO.getVersion());
    }

    /**
     * 版本号比较,true 是最新版本，false 不是最新版本
     * @param version 当前版本
     * @param lastVersion 最新版本
     * @return true 当前版本>=最新版本，false 当前版本<最新版本
     */
    private static boolean comparisonVersion(String version, String lastVersion) {
        String[] versions = version.split("\\.");
        String[] lastVersions = lastVersion.split("\\.");
        int length = Math.max(versions.length, lastVersions.length);
        for (int i = 0; i < length; i++) {
            int v1 = i < versions.length ? Integer.parseInt(versions[i]) : 0;
            int v2 = i < lastVersions.length ? Integer.parseInt(lastVersions[i]) : 0;
            if (v1 < v2) {
                return false;
            } else if (v1 > v2) {
                return true;
            }
        }
        return true;
    }

    /**
     * 执行sql脚本
     */
    private void executeSqlScript(Long projectId, List<String> sqlScriptList, String version) {
        projectFileApi.changePrjFileSumDs(projectId, () -> transactionTemplate.execute(status -> {
            try {
                for (String sql : sqlScriptList) {
                    if (StringUtils.hasText(sql)) {
                        jdbcTemplate.execute(sql);
                    }
                }
                return true;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("执行SQL脚本失败 projectId={}, error={}", projectId, e.getMessage(), e);
                throw e;
            }
        }), true, version, ProjectSumFileSourceEnum.PROJECT_SCRIPT_UPGRADE);
    }
}
