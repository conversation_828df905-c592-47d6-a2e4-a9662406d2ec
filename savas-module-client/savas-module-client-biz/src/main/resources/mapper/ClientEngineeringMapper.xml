<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.savas.hub.module.client.dal.mapper.ClientEngineeringMapper">

    <select id="selectByPrjIdTree" resultType="cn.savas.hub.module.client.dal.dataobject.ClientEngineeringDO">
        SELECT
            eng_id,
            eng_pid,
            eng_director_id
        FROM client_engineering
        WHERE eng_project_id = #{prjId}

        union all

        SELECT
            project_id as eng_id,
            0 as eng_pid,
            director_id as eng_director_id
        FROM client_project
        WHERE project_id = #{prjId}
    </select>
</mapper>
