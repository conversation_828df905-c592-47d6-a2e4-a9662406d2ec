# AICC 许可证验证器配置
aicc:
  license:
    enabled: true                           # 启用许可证验证
    fail-fast: false                        # 验证失败时是否阻止应用启动
    show-details-on-startup: true          # 启动时显示许可证详细信息
    enable-periodic-validation: true       # 启用定期验证
    periodic-validation-interval: 3600     # 定期验证间隔（秒）

# Spring Boot Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,license
  endpoint:
    health:
      show-details: when-authorized
    license:
      enabled: true
