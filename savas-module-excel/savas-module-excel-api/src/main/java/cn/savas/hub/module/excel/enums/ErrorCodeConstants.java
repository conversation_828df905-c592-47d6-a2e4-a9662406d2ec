package cn.savas.hub.module.excel.enums;

import cn.savas.hub.framework.common.exception.ErrorCode;

/**
 * excel 错误码枚举类
 *
 * excel 系统，使用 1-023-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 参数配置 1-023-000-000 ==========
    /**
     * 模板不存在
     */
    ErrorCode EXCEL_TEMPLATE_NOT_EXISTS = new ErrorCode(1_023_000_001, "模板不存在");

    ErrorCode EXCEL_TEMPLATE_DOWNLOAD_FAILED = new ErrorCode(1_023_000_002, "模板下载失败");
}
