package cn.savas.hub.module.excel.service.template;

import cn.savas.hub.framework.common.util.object.BeanUtils;
import cn.savas.hub.framework.excel.core.util.ExcelUtils;
import cn.savas.hub.module.excel.controller.admin.template.vo.ExcelTemplateRespVO;
import cn.savas.hub.module.excel.dal.dataobject.ExcelColumnsDO;
import cn.savas.hub.module.excel.dal.dataobject.ExcelTemplatesDO;
import cn.savas.hub.module.excel.dal.mapper.ExcelColumnsMapper;
import cn.savas.hub.module.excel.dal.mapper.ExcelTemplatesMapper;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.excel.enums.ErrorCodeConstants.EXCEL_TEMPLATE_DOWNLOAD_FAILED;
import static cn.savas.hub.module.excel.enums.ErrorCodeConstants.EXCEL_TEMPLATE_NOT_EXISTS;

/**
 * <AUTHOR>
 * @date 2025/4/11 16:23
 */
@Service
@Slf4j
public class ExcelTemplateServiceImpl implements ExcelTemplateService{
    @Resource
    private ExcelTemplatesMapper templateRepo;
    @Resource
    private ExcelColumnsMapper columnRepo;
    @Override
    public List<Map<String, Object>> parseExcel(MultipartFile file, Long templateId) throws IOException {
        // 1. 加载模板
        ExcelTemplatesDO template = templateRepo.selectById(templateId);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        List<ExcelColumnsDO> columns = columnRepo.selectByTemplateId(templateId);
        List<Map<String, Object>> dataList = new ArrayList<>();
        // 2. 配置 EasyExcel 解析
        EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                    @Override
                    public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                        // 跳过表头
                        if (context.readRowHolder().getRowIndex() < template.getDataStartRow() - 1) {
                            return;
                        }
                        // 映射列到字段
                        Map<String, Object> mappedRow = new HashMap<>();
                        for (ExcelColumnsDO col : columns) {
                            // 如果有默认值
                            if (col.getColumnDefaultValue() != null && !col.getColumnDefaultValue().isEmpty()) {
                                mappedRow.put(col.getColumnField(), convertValue(col.getColumnDefaultValue(), col.getColumnFieldType()));
                                continue;
                            }
                            // 获取列索引对应的值
                            String value = rowData.get(col.getColumnIndex());
                            if (col.getColumnField() != null) {
                                mappedRow.put(col.getColumnField(), convertValue(value, col.getColumnFieldType()));
                            }
                        }
                        dataList.add(mappedRow);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        // 3. 处理解析后的数据
                    }
                })
                .headRowNumber(template.getHeaderRows()) // 设置表头行数
                .sheet()
                .doRead();
        return dataList;
    }

    @Override
    public List<ExcelTemplateRespVO> getTemplateList() {
        List<ExcelTemplatesDO> source = templateRepo.selectList(new LambdaQueryWrapper<ExcelTemplatesDO>()
                .eq(ExcelTemplatesDO::getTemplateType, 1)); // 1：标准价格库，2：电缆价格库
        return BeanUtils.toBean(source, ExcelTemplateRespVO.class);
    }

    @Override
    public List<ExcelColumnsDO> getTemplateColumns(Long templateId) {
        return columnRepo.selectByTemplateId(templateId);
    }

    @Override
    public void downloadTemplate(Long templateId, HttpServletResponse response) {
        // 1. 加载模板和列信息
        ExcelTemplatesDO template = templateRepo.selectById(templateId);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        List<ExcelColumnsDO> columns = columnRepo.selectByTemplateId(templateId);

        // 2. 准备表头数据
        Map<Long, ExcelColumnsDO> columnIdMap = new HashMap<>();
        for (ExcelColumnsDO column : columns) {
            columnIdMap.put(column.getColumnId(), column);
        }
        // 构建表头
        List<List<String>> head = new LinkedList<>();
        // 获取所有叶子节点
        List<ExcelColumnsDO> leafColumns = new ArrayList<>();
        for (ExcelColumnsDO column : columns) {
            if (column.getColumnSpan() == 1) {
                leafColumns.add(column);
            }
        }
        // 遍历叶子节点，构建表头
        for (ExcelColumnsDO column : leafColumns) {
            List<String> rowHead = new LinkedList<>();
            buildHeader(column, columnIdMap, rowHead);
            head.add(column.getColumnIndex(), rowHead);
        }
        // 遍历叶子节点, 填充默认值
        List<List<Object>> defaultRowList = new ArrayList<>(1);
        List<Object> defaultRow = new ArrayList<>(head.size());
        defaultRowList.add(defaultRow);
        for (ExcelColumnsDO column : leafColumns) {
            Object defaultValue = convertValue(column.getColumnDefaultValue(), column.getColumnFieldType());
            defaultRow.add(defaultValue == null ? "" : defaultValue);
        }
        // 3. 导出Excel
        try {
            ExcelUtils.write(response, template.getTemplateName() + ".xlsx", template.getTemplateName(), head, defaultRowList);
        } catch (IOException e) {
            log.error("下载模板失败，templateId: {}, error: {}", templateId, e.getMessage(), e);
            throw exception(EXCEL_TEMPLATE_DOWNLOAD_FAILED);
        }
    }

    @Override
    public List<ExcelColumnsDO> getTemplateColumnsByName(String templateName) {
        ExcelTemplatesDO template = templateRepo.selectOne(
                new LambdaQueryWrapper<ExcelTemplatesDO>()
                        .select(ExcelTemplatesDO::getTemplateId)
                        .eq(ExcelTemplatesDO::getTemplateName, templateName)
        );
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        return columnRepo.selectByTemplateId(template.getTemplateId());
    }

    @Override
    public ExcelTemplatesDO getTemplateByName(String templateName) {
        ExcelTemplatesDO template = templateRepo.selectOne(ExcelTemplatesDO::getTemplateName, templateName);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        return template;
    }

    @Override
    public List<ExcelTemplatesDO> getTemplateByType(Integer templateType) {
        List<ExcelTemplatesDO> templates = templateRepo.selectList(
                new LambdaQueryWrapper<ExcelTemplatesDO>()
                        .eq(ExcelTemplatesDO::getTemplateType, templateType)
        );
        if (templates.isEmpty()) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        return templates;
    }

    @Override
    public ExcelTemplatesDO getTemplateById(Long templateId) {
        ExcelTemplatesDO template = templateRepo.selectById(templateId);
        if (template == null) {
            throw exception(EXCEL_TEMPLATE_NOT_EXISTS);
        }
        return template;
    }

    /**
     * 递归构建表头
     *
     * @param column      当前列
     * @param columnIdMap 列ID映射
     *    * @param rowHead     表头列表
     */
    private void buildHeader(ExcelColumnsDO column, Map<Long, ExcelColumnsDO> columnIdMap, List<String> rowHead) {
        rowHead.add(0, column.getColumnName());
        ExcelColumnsDO parent = columnIdMap.get(column.getParentColumnId());
        // 如果有父级列，则递归构建父级列的表头
        if (parent != null) {
            buildHeader(parent, columnIdMap, rowHead);
        }
    }

    private Object convertValue(String value, String fieldType) {
        // 根据 field_type 转换数据类型
        // 示例：string、int、date 等
        if (value == null || value.isEmpty()) {
            return null;
        }
        if ("decimal".equalsIgnoreCase(fieldType)) {
            return new BigDecimal(value);
        } else if ("string".equalsIgnoreCase(fieldType)) {
            return value;
        }else if ("long".equalsIgnoreCase(fieldType)) {
            return Long.parseLong(value);
        }else if ("integer".equalsIgnoreCase(fieldType)) {
            return Integer.parseInt(value);
        }
        return value;
    }
}
