package cn.savas.hub.module.excel.api.template;

import cn.savas.hub.framework.common.util.object.BeanUtils;
import cn.savas.hub.module.excel.api.template.dto.ExcelColumnDTO;
import cn.savas.hub.module.excel.api.template.dto.ExcelTemplateDTO;
import cn.savas.hub.module.excel.dal.dataobject.ExcelColumnsDO;
import cn.savas.hub.module.excel.dal.dataobject.ExcelTemplatesDO;
import cn.savas.hub.module.excel.service.template.ExcelTemplateService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/23 18:35
 */
@Service
public class ExcelTemplatesApiImpl implements ExcelTemplatesApi{
    @Resource
    private ExcelTemplateService excelTemplateService;
    @Override
    public List<Map<String, Object>> parseExcel(MultipartFile file, Long templateId) throws IOException {
        return excelTemplateService.parseExcel(file, templateId);
    }

    @Override
    public List<ExcelColumnDTO> getTemplateColumns(Long templateId) {
        List<ExcelColumnsDO> templateColumns = excelTemplateService.getTemplateColumns(templateId);
        return BeanUtils.toBean(templateColumns, ExcelColumnDTO.class);
    }

    @Override
    public List<ExcelColumnDTO> getTemplateColumnsByName(String templateName) {
        List<ExcelColumnsDO> templateColumns = excelTemplateService.getTemplateColumnsByName(templateName);
        return BeanUtils.toBean(templateColumns, ExcelColumnDTO.class);
    }

    @Override
    public ExcelTemplateDTO getTemplateByName(String templateName) {
        ExcelTemplatesDO templateByName = excelTemplateService.getTemplateByName(templateName);
        return BeanUtils.toBean(templateByName, ExcelTemplateDTO.class);
    }

    @Override
    public List<ExcelTemplateDTO> getTemplateByType(Integer templateType) {
        List<ExcelTemplatesDO> templatesByType = excelTemplateService.getTemplateByType(templateType);
        return BeanUtils.toBean(templatesByType, ExcelTemplateDTO.class);
    }

    @Override
    public ExcelTemplateDTO getTemplateById(Long templateId) {
        ExcelTemplatesDO templateById = excelTemplateService.getTemplateById(templateId);
        return BeanUtils.toBean(templateById, ExcelTemplateDTO.class);
    }
}
