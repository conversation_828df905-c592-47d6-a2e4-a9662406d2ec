package cn.savas.hub.module.excel.controller.admin.template;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.module.excel.controller.admin.template.vo.ExcelTemplateRespVO;
import cn.savas.hub.module.excel.service.template.ExcelTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2025/4/11 16:16
 */
@Tag(name = "Excel动态模板")
@RestController
@RequestMapping("/excel/template")
@Validated
public class ExcelTemplateController {
    @Resource
    private ExcelTemplateService excelTemplateService;

    @GetMapping("/list")
    @Operation(summary = "获取模板列表")
    @PreAuthorize("@ss.hasPermission('excel:template:list')")
    public CommonResult<List<ExcelTemplateRespVO>> getTemplateList() {
        return success(excelTemplateService.getTemplateList());
    }

    // 下载模板
    @GetMapping("/downloadTemplate")
    @Operation(summary = "下载模板")
    @PreAuthorize("@ss.hasPermission('excel:template:download')")
    public void downloadTemplate(@RequestParam("templateId") Long templateId, HttpServletResponse response) {
        excelTemplateService.downloadTemplate(templateId, response);
    }
}
