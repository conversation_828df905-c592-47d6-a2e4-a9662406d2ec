package cn.savas.hub.module.excel.service.template;

import cn.savas.hub.module.excel.controller.admin.template.vo.ExcelTemplateRespVO;
import cn.savas.hub.module.excel.dal.dataobject.ExcelColumnsDO;
import cn.savas.hub.module.excel.dal.dataobject.ExcelTemplatesDO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/11 16:23
 */
public interface ExcelTemplateService {
    List<Map<String, Object>> parseExcel(MultipartFile file, Long templateId) throws IOException;

    List<ExcelTemplateRespVO> getTemplateList();

    List<ExcelColumnsDO> getTemplateColumns(Long templateId);

    void downloadTemplate(Long templateId, HttpServletResponse response);

    List<ExcelColumnsDO> getTemplateColumnsByName(String templateName);

    ExcelTemplatesDO getTemplateByName(String templateName);

    List<ExcelTemplatesDO> getTemplateByType(Integer templateType);

    ExcelTemplatesDO getTemplateById(Long templateId);
}
