package cn.savas.hub.module.bpm.api.client;

import cn.savas.hub.framework.common.client.enmus.ClientOperateEnum;
import cn.savas.hub.framework.common.util.object.BeanUtils;
import cn.savas.hub.module.bpm.api.client.dto.*;
import cn.savas.hub.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceCancelReqVO;
import cn.savas.hub.module.bpm.convert.oa.BpmClientConvert;
import cn.savas.hub.module.bpm.dal.dataobject.client.*;
import cn.savas.hub.module.bpm.dal.dataobject.oa.BpmOaProofreadingDO;
import cn.savas.hub.module.bpm.dal.mysql.client.*;
import cn.savas.hub.module.bpm.dal.mysql.oa.BpmOaProofreadingMapper;
import cn.savas.hub.module.bpm.service.task.BpmProcessInstanceService;
import cn.savas.hub.module.bpm.service.task.BpmTaskService;
import cn.savas.hub.module.client.api.project.ClientProjectApi;
import cn.savas.hub.module.client.api.project.dto.ClientEngineeringRespDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.bpm.enums.ErrorCodeConstants.CLIENT_NODE_PROGRESS_NOT_EXISTS;

/**
 * <AUTHOR>
 * @date 2024/10/23 16:35
 */
@Service
@Validated
public class BpmClientApiImpl implements BpmClientApi {
    @Resource
    private BpmOaProofreadingMapper proofreadingMapper;
    @Resource
    private BpmTaskService bpmTaskService;
    @Resource
    private BpmProcessInstanceService processInstanceService;
    @Resource
    private ClientFlowModelMapper flowModelMapper;
    @Resource
    private ClientFlowModelNDMapper flowModelNDMapper;
    @Resource
    private ClientFlowModelNMapper flowModelNMapper;
    @Resource
    private ClientFlowNodeDirMapper flowNodeDirMapper;
    @Resource
    private ClientFlowNodeMapper flowNodeMapper;
    @Resource
    private ClientFlowScheduleMapper flowScheduleMapper;
    @Resource
    private ClientSuggestionMapper suggestionMapper;
    @Resource
    private ClientProjectApi clientProjectApi;

    @Override
    public List<BpmClientHistoricReqDTO> getTaskListByProcessInstanceIds(List<String> processInstanceIdList) {
        if (CollectionUtils.isEmpty(processInstanceIdList)) {
            return Collections.emptyList();
        }
        List<BpmClientHistoricReqDTO> resList = new ArrayList<>();
        // 获取历史流程节点
        List<HistoricTaskInstance> instanceTaskList = bpmTaskService.getTaskListByProcessInstanceIds(processInstanceIdList);
        List<BpmClientHistoricReqDTO> historicReqDTOS = BpmClientConvert.INSTANCE.convertList1(instanceTaskList);
        resList.addAll(historicReqDTOS);
        // 获取子流程节点
        return resList;
    }

    @Override
    public List<BpmOaProofreadingDTO> getProofreadingList(Long originalId) {
        return BpmClientConvert.INSTANCE.convertList2(proofreadingMapper.selectOneByPrjIdOnStatus(originalId));
    }


    @Override
    public Set<Long> getTaskProjectIds(Long loginUserId) {
        List<ClientFlowNodeDO> flowNodeDOS = flowNodeMapper.getTaskProjectIds(loginUserId);
        Set<Long> hostModelIdSet = flowNodeDOS.stream()
                .map(ClientFlowNodeDO::getNodeHostmodel)
                .collect(Collectors.toSet());
        List<ClientEngineeringRespDTO> byEngId = clientProjectApi.getBpmProjectByEngId(hostModelIdSet);
        Set<Long> resultIdSet = new HashSet<>();
        // 根据工程获取项目ID
        Set<Long> engProjectId = byEngId.stream()
                .map(ClientEngineeringRespDTO::getEngProjectId)
                .collect(Collectors.toSet());
        resultIdSet.addAll(engProjectId);
        List<ClientProjectRespDTO> projectByIdList = clientProjectApi.getProjectByIdList(hostModelIdSet);
        Set<Long> prjProjectIds = projectByIdList.stream()
                .map(ClientProjectRespDTO::getProjectId)
                .collect(Collectors.toSet());
        resultIdSet.addAll(prjProjectIds);
        return resultIdSet;
    }

    @Override
    public Set<Long> getFlowUserIdByPrjId(Long prjId) {
        List<ClientFlowNodeDO> flowNodeDOS = flowNodeMapper.selectByPrjId(prjId);
        return flowNodeDOS.stream()
                .map(ClientFlowNodeDO::getNodeUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    @Override
    public List<BpmOaProofreadingDTO> delPrjProofreading(Long prjId) {
        List<BpmOaProofreadingDO> proofreadingList =proofreadingMapper.selectByPrjId(prjId);
        for (BpmOaProofreadingDO proofreadingDO : proofreadingList) {
            if (proofreadingDO.getProcessInstanceId() != null) {
                BpmProcessInstanceCancelReqVO cancelReqVO = new BpmProcessInstanceCancelReqVO();
                cancelReqVO.setReason("删除项目");
                cancelReqVO.setId(proofreadingDO.getProcessInstanceId());
                processInstanceService.cancelProcessInstanceByStartUser(Long.valueOf(proofreadingDO.getCreator()), cancelReqVO);
            }
        }
        proofreadingMapper.delete(new LambdaQueryWrapper<BpmOaProofreadingDO>().eq(BpmOaProofreadingDO::getOriginalId, prjId));
        return BpmClientConvert.INSTANCE.convertList2(proofreadingList);
    }

    @Override
    public ClientFlowTemplateDTO getFlowTemplate() {
        List<ClientFlowTemplateDTO.FlowTemplate> workList = BeanUtils.toBean(flowModelMapper.selectList(new LambdaQueryWrapper<ClientWorkflowDO>().orderByAsc(ClientWorkflowDO::getWorkDate)), ClientFlowTemplateDTO.FlowTemplate.class);
        List<ClientFlowTemplateDTO.FlowNode> workNodeList = BeanUtils.toBean(flowModelNMapper.selectList(new LambdaQueryWrapper<ClientWorkflowNodeDO>().orderByAsc(ClientWorkflowNodeDO::getNodeSortId)), ClientFlowTemplateDTO.FlowNode.class);
        List<ClientFlowTemplateDTO.FlowDirection> workNodeDirectionList = BeanUtils.toBean(flowModelNDMapper.selectList(), ClientFlowTemplateDTO.FlowDirection.class);
        return new ClientFlowTemplateDTO(workList, workNodeList, workNodeDirectionList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFlowData(ClientFlowDataDTO flowDataDTO) {
        Map<ClientOperateEnum, ClientFlowDataDTO.FlowDataContainer> flowData = flowDataDTO.getFlowData();
        flowData.forEach((key, value) -> {
            List<ClientFlowNodeDO> flowNodeList = BeanUtils.toBean(value.getNodes(), ClientFlowNodeDO.class);
            List<ClientFlowNodeDirectionDO> flowNodeDirList = BeanUtils.toBean(value.getDirections(), ClientFlowNodeDirectionDO.class);
            List<ClientFlowScheduleDO> flowScheduleList = BeanUtils.toBean(value.getSchedules(), ClientFlowScheduleDO.class);
            List<ClientSuggestionDO> suggestionList = BeanUtils.toBean(value.getSuggestions(), ClientSuggestionDO.class);
            switch (key) {
                case INSERT:
                    insertFlowData(flowNodeList, flowNodeDirList, flowScheduleList, suggestionList);
                    break;
                case UPDATE:
                    updateFlowData(flowNodeList, flowNodeDirList, flowScheduleList, suggestionList);
                    break;
                case DELETE:
                    deleteFlowData(flowNodeList, flowNodeDirList, flowScheduleList, suggestionList);
                    break;
                default:
                    break;
            }
        });
    }

    @Override
    public ClientFlowDataDTO.FlowDataContainer getFlowData(Long originalid) {
        // 获取工程数据
        List<ClientEngineeringRespDTO> engineeringList = clientProjectApi.getEngineeringIdList(originalid);
        Set<Long> engineeringIdSet = engineeringList.stream().map(ClientEngineeringRespDTO::getEngId).collect(Collectors.toSet());
        engineeringIdSet.add(originalid);
        // 获取流程数据
        ClientFlowDataDTO.FlowDataContainer container = new ClientFlowDataDTO.FlowDataContainer();
        List<ClientFlowNodeDO> clientFlowNodeDOList = flowNodeMapper.selectByPrjId(originalid);
        List<ClientFlowNodeDirectionDO> flowNodeDirectionList = flowNodeDirMapper.selectByPrjIdAndSort(originalid);
        List<ClientFlowScheduleDO> scheduleDOList = flowScheduleMapper.selectByPrjId(originalid);
        List<ClientSuggestionDO> suggestionDOList = suggestionMapper.selectByPrjId(originalid);
        // 排除 已删除工程流程
        clientFlowNodeDOList = clientFlowNodeDOList.stream().filter(e -> engineeringIdSet.contains(e.getNodeHostmodel())).collect(Collectors.toList());
        flowNodeDirectionList = flowNodeDirectionList.stream().filter(e -> engineeringIdSet.contains(e.getDirHostModel())).collect(Collectors.toList());
        scheduleDOList = scheduleDOList.stream().filter(e -> engineeringIdSet.contains(e.getSchHostModel())).collect(Collectors.toList());
        suggestionDOList = suggestionDOList.stream().filter(e -> engineeringIdSet.contains(e.getSugHostmodel())).collect(Collectors.toList());

        container.setNodes(BeanUtils.toBean(clientFlowNodeDOList, ClientFlowDataDTO.FlowNode.class));
        container.setDirections(BeanUtils.toBean(flowNodeDirectionList, ClientFlowDataDTO.FlowNodeDirection.class));
        container.setSchedules(BeanUtils.toBean(scheduleDOList, ClientFlowDataDTO.FlowSchedule.class));
        container.setSuggestions(BeanUtils.toBean(suggestionDOList, ClientFlowDataDTO.FlowSuggestion.class));
        return container;
    }

    @Override
    public void delFlowDataByPrjId(Long prjId) {
        flowNodeMapper.delete(new LambdaQueryWrapper<ClientFlowNodeDO>().eq(ClientFlowNodeDO::getNodeProjectId, prjId));
        flowNodeDirMapper.delete(new LambdaQueryWrapper<ClientFlowNodeDirectionDO>().eq(ClientFlowNodeDirectionDO::getDirProjectId, prjId));
        flowScheduleMapper.delete(new LambdaQueryWrapper<ClientFlowScheduleDO>().eq(ClientFlowScheduleDO::getSchProjectId, prjId));
        suggestionMapper.delete(new LambdaQueryWrapper<ClientSuggestionDO>().eq(ClientSuggestionDO::getSugProjectId, prjId));
    }

    @Override
    public List<ClientFlowNodeDTO> getFlowNodeById(Collection<Long> flownodeids) {
        if (CollectionUtils.isEmpty(flownodeids)) {
            return Collections.emptyList();
        }
        return BpmClientConvert.INSTANCE.convertList5(flowNodeMapper.selectBatchIds(flownodeids));
    }

    @Override
    public void bindSumFileVersion(Long flowScheduleId, Integer sumFileVersion) {
        ClientFlowScheduleDO flowScheduleDO = flowScheduleMapper.selectById(flowScheduleId);
        if (flowScheduleDO == null) {
            throw exception(CLIENT_NODE_PROGRESS_NOT_EXISTS);
        }
        flowScheduleDO.setSchSumFileVersion(sumFileVersion);
        flowScheduleMapper.updateById(flowScheduleDO);
    }

    @Override
    public List<ClientFlowStateDTO> getPrjFlowState(List<Long> originalid) {
        if (CollectionUtils.isEmpty(originalid)) {
            return Collections.emptyList();
        }
        return flowNodeMapper.selectPrjFlowState(originalid);
    }

    private void deleteFlowData(List<ClientFlowNodeDO> flowNodeList,
                                List<ClientFlowNodeDirectionDO> flowNodeDirList,
                                List<ClientFlowScheduleDO> flowScheduleList,
                                List<ClientSuggestionDO> suggestionList
    ) {
        if (!CollectionUtils.isEmpty(flowNodeList)) {
            flowNodeMapper.deleteByIds(flowNodeList.stream().map(ClientFlowNodeDO::getNodeId).collect(Collectors.toSet()));
        }
        if (!CollectionUtils.isEmpty(flowNodeDirList)) {
            flowNodeDirMapper.deleteByIds(flowNodeDirList.stream().map(ClientFlowNodeDirectionDO::getDirId).collect(Collectors.toSet()));
        }
        if (!CollectionUtils.isEmpty(flowScheduleList)) {
            flowScheduleMapper.deleteByIds(flowScheduleList.stream().map(ClientFlowScheduleDO::getSchId).collect(Collectors.toSet()));
        }
        if (!CollectionUtils.isEmpty(suggestionList)) {
            suggestionMapper.deleteByIds(suggestionList.stream().map(ClientSuggestionDO::getSugId).collect(Collectors.toSet()));
        }
    }
    private void updateFlowData(List<ClientFlowNodeDO> flowNodeList,
                                List<ClientFlowNodeDirectionDO> flowNodeDirList,
                                List<ClientFlowScheduleDO> flowScheduleList,
                                List<ClientSuggestionDO> suggestionList
    ) {
        if (!CollectionUtils.isEmpty(flowNodeList)) {
            flowNodeMapper.updateBatch(flowNodeList);
        }
        if (!CollectionUtils.isEmpty(flowNodeDirList)) {
            flowNodeDirMapper.updateBatch(flowNodeDirList);
        }
        if (!CollectionUtils.isEmpty(flowScheduleList)) {
            flowScheduleMapper.updateBatch(flowScheduleList);
        }
        if (!CollectionUtils.isEmpty(suggestionList)) {
            suggestionMapper.updateBatch(suggestionList);
        }
    }

    private void insertFlowData(List<ClientFlowNodeDO> flowNodeList,
                                List<ClientFlowNodeDirectionDO> flowNodeDirList,
                                List<ClientFlowScheduleDO> flowScheduleList,
                                List<ClientSuggestionDO> suggestionList
    ) {
        if (!CollectionUtils.isEmpty(flowNodeList)) {
            flowNodeMapper.insert(flowNodeList);
        }
        if (!CollectionUtils.isEmpty(flowNodeDirList)) {
            flowNodeDirMapper.insertBatch(flowNodeDirList);
        }
        if (!CollectionUtils.isEmpty(flowScheduleList)) {
            flowScheduleMapper.insertBatch(flowScheduleList);
        }
        if (!CollectionUtils.isEmpty(suggestionList)){
            suggestionMapper.insertBatch(suggestionList);
        }
    }



}
