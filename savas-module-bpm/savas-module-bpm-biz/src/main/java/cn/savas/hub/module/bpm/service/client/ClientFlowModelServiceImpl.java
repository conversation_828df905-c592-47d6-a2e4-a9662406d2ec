package cn.savas.hub.module.bpm.service.client;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelDetailRespVO;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelPageReqVO;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelReqVO;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelUpdateReqVO;
import cn.savas.hub.module.bpm.convert.client.ClientFlowModelConvert;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientWorkflowDO;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientWorkflowDirectionDO;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientWorkflowNodeDO;
import cn.savas.hub.module.bpm.dal.mysql.client.ClientFlowModelMapper;
import cn.savas.hub.module.bpm.dal.mysql.client.ClientFlowModelNDMapper;
import cn.savas.hub.module.bpm.dal.mysql.client.ClientFlowModelNMapper;
import cn.savas.hub.module.bpm.utils.BpmnXmlParserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/7 13:59
 */
@Service
@Slf4j
public class ClientFlowModelServiceImpl implements ClientFlowModelService{
    @Resource
    private ClientFlowModelMapper clientFlowModelMapper;
    @Resource
    private ClientFlowModelNMapper clientFlowModelNMapper;
    @Resource
    private ClientFlowModelNDMapper clientFlowModelNdMapper;
    @Resource
    private BpmnXmlParserUtil bpmnXmlParserUtil;
    @Override
    public void createFlowModel(ClientFlowModelReqVO vo) {
        ClientWorkflowDO convert = ClientFlowModelConvert.INSTANCE.convert(vo);
        convert.setWorkUserId(SecurityFrameworkUtils.getLoginUserId());
        convert.setWorkUserName(SecurityFrameworkUtils.getLoginUserNickname());
        convert.setWorkDate(new Date());
        convert.setWorkKey(vo.getKey());
        clientFlowModelMapper.insert(convert);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFlowModel(ClientFlowModelUpdateReqVO vo) {
        // 处理BPMN XML解析与数据更新
        if (vo.getBpmnXml() != null) {
            try {
                processBpmnXml(vo);
            } catch (Exception e) {
                log.error("流程模型更新失败: {}", e.getMessage());
                throw e;
            }
        }
        // 更新主表信息
        ClientWorkflowDO workflowDO = ClientFlowModelConvert.INSTANCE.convert6(vo);
        if (vo.getBpmnXml() != null) {
            workflowDO.setWorkByte(StrUtil.utf8Bytes(vo.getBpmnXml()));
        }
        if (vo.getBpmnSvg() != null){
            workflowDO.setWorkFlowChart(ZipUtil.zlib(vo.getBpmnSvg(), "UTF-8", 3));
        }
        clientFlowModelMapper.updateById(workflowDO);
    }

    @Override
    public PageResult<ClientWorkflowDO> getFlowModelPage(ClientFlowModelPageReqVO reqVO) {
        return clientFlowModelMapper.selectPage(reqVO);
    }

    @Override
    public ClientFlowModelDetailRespVO getFlowModelDetail(Long workId) {
        ClientWorkflowDO workflowDO = clientFlowModelMapper.selectById(workId);
        ClientFlowModelDetailRespVO respVO = new ClientFlowModelDetailRespVO();
        respVO.setId(workflowDO.getWorkId());
        respVO.setName(workflowDO.getWorkName());
        respVO.setBpmnXml(StrUtil.str(workflowDO.getWorkByte(), "UTF-8"));
        respVO.setEnable(workflowDO.getWorkState().intValue());
        respVO.setKey(workflowDO.getWorkKey());
        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delFlowModel(Long id) {
        clientFlowModelMapper.deleteById(id);
        clientFlowModelNMapper.deleteByWorkflowId(id);
        clientFlowModelNdMapper.deleteByWorkflowId(id);
    }

    private void processBpmnXml(ClientFlowModelUpdateReqVO vo) {
        BpmnXmlParserUtil.BpmnParseResult parseResult = bpmnXmlParserUtil.parseBpmnXml(vo.getBpmnXml());
        // 转换节点和连接，并设置workflowId
        List<ClientWorkflowNodeDO> nodes = convertNodes(parseResult.getNodes(), vo.getId());
        List<ClientWorkflowDirectionDO> connections = convertConnections(parseResult.getConnections(), vo.getId());
        // 替换旧数据
        replaceOldData(vo.getId(), nodes, connections);
    }

    private List<ClientWorkflowNodeDO> convertNodes(List<BpmnXmlParserUtil.ClientWorkFlowNode> nodes, Long workflowId) {
        return nodes.stream()
                .map(node -> {
                    ClientWorkflowNodeDO nodeDO = ClientFlowModelConvert.INSTANCE.convert1(node);
                    nodeDO.setNodeWorkFlow(workflowId);
                    nodeDO.setNodePid(nodeDO.getNodePid() == null ? workflowId : nodeDO.getNodePid());
                    nodeDO.setNodeType(node.getFlowNodeType());
                    return nodeDO;
                })
                .collect(Collectors.toList());
    }

    private List<ClientWorkflowDirectionDO> convertConnections(List<BpmnXmlParserUtil.ClientWorkFlowDirection> connections, Long workflowId) {
        return connections.stream()
                .map(connection -> {
                    ClientWorkflowDirectionDO connectionDO = ClientFlowModelConvert.INSTANCE.convert2(connection);
                    connectionDO.setDirWorkFlow(workflowId);
                    return connectionDO;
                })
                .collect(Collectors.toList());
    }

    private void replaceOldData(Long workflowId, List<ClientWorkflowNodeDO> nodes, List<ClientWorkflowDirectionDO> connections) {
        clientFlowModelNMapper.deleteByWorkflowId(workflowId);
        clientFlowModelNdMapper.deleteByWorkflowId(workflowId);

        if (!nodes.isEmpty()) {
            clientFlowModelNMapper.insertBatch(nodes);
        }
        if (!connections.isEmpty()) {
            clientFlowModelNdMapper.insertBatch(connections);
        }
    }
}
