package cn.savas.hub.module.system.api.auth;

import cn.savas.hub.module.system.api.auth.dto.AuthLoginRespDTO;
import cn.savas.hub.module.system.api.auth.dto.ClientAuthLoginRespDTO;
import cn.savas.hub.module.system.api.auth.dto.OAuth2AccessTokenDTO;
import cn.savas.hub.module.system.enums.logger.LoginLogTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/24 16:25
 */
public interface AdminAuthApi {
    /**
     * 客户端 验证账号 + 密码。如果通过，则返回用户
     */
    ClientAuthLoginRespDTO clientAuthenticate(String username, String password, LoginLogTypeEnum logType);

    /**
     * 获取客户端在线用户
     */
    List<OAuth2AccessTokenDTO> getClientOnlineUserList(Integer userType);

    /**
     * 创建新的客户端用户访问令牌
     * @return
     */
    AuthLoginRespDTO clientCreateNewUserToken(Long userId, String username);

    /**
     * 刷新客户端访问令牌
     */
    OAuth2AccessTokenDTO clientRefreshAccessToken(String refreshToken);

    /**
     * 客户端登出
     */
    void clientLogout(String accessToken);

    /**
     * 客户端强制登出
     */
    OAuth2AccessTokenDTO clientForceLogout(String accessToken);
    /**
     * 获取指定的访问令牌
     */
    OAuth2AccessTokenDTO getAccessToken(String accessToken);
}
