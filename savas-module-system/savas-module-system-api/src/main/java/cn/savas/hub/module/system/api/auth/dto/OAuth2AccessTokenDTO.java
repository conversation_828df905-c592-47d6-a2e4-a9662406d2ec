package cn.savas.hub.module.system.api.auth.dto;

import cn.savas.hub.framework.common.enums.UserTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/13 15:17
 */
@Data
public class OAuth2AccessTokenDTO {
    /**
     * 编号，数据库递增
     */
    private Long id;
    /**
     * 访问令牌
     */
    private String accessToken;
    /**
     * 刷新令牌
     */
    private String refreshToken;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 用户类型
     *
     */
    private Integer userType;
    /**
     * 用户信息
     */
    private Map<String, String> userInfo;
    /**
     * 客户端编号
     *
     */
    private String clientId;
    /**
     * 授权范围
     */
    private List<String> scopes;
    /**
     * 过期时间
     */
    private LocalDateTime expiresTime;

    /**
     * 用户 IP
     */
    private String userIp;
}
