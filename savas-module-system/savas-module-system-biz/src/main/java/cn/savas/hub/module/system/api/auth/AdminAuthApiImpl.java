package cn.savas.hub.module.system.api.auth;

import cn.savas.hub.framework.common.enums.UserTypeEnum;
import cn.savas.hub.framework.common.util.object.BeanUtils;
import cn.savas.hub.framework.common.util.servlet.ServletUtils;
import cn.savas.hub.framework.websocket.core.sender.WebSocketMessageSender;
import cn.savas.hub.module.client.enums.system.AuthUserErrorEnum;
import cn.savas.hub.module.client.enums.system.CSocketMessageTypeEnum;
import cn.savas.hub.module.system.api.auth.dto.AuthLoginRespDTO;
import cn.savas.hub.module.system.api.auth.dto.ClientAuthLoginRespDTO;
import cn.savas.hub.module.system.api.auth.dto.OAuth2AccessTokenDTO;
import cn.savas.hub.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import cn.savas.hub.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.savas.hub.module.system.enums.logger.LoginLogTypeEnum;
import cn.savas.hub.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.savas.hub.module.system.service.auth.AdminAuthService;
import cn.savas.hub.module.system.service.oauth2.OAuth2TokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.system.enums.ErrorCodeConstants.AUTH_TOKEN_NOT_EXISTS;

/**
 * <AUTHOR>
 * @date 2024/10/24 16:25
 */
@Slf4j
@Service
public class AdminAuthApiImpl implements AdminAuthApi{
    @Resource
    private AdminAuthService adminAuthService;
    @Resource
    private OAuth2TokenService oauth2TokenService;

    @Override
    public void clientLogout(String token) {
        adminAuthService.logout(token, LoginLogTypeEnum.LOGOUT_CLIENT_SELF.getType());
    }

    @Override
    public OAuth2AccessTokenDTO getAccessToken(String accessToken) {
        OAuth2AccessTokenDO tokenDO = oauth2TokenService.getAccessToken(accessToken);
        return BeanUtils.toBean(tokenDO, OAuth2AccessTokenDTO.class);
    }

    @Override
    public OAuth2AccessTokenDTO clientForceLogout(String accessToken) {
        return BeanUtils.toBean(adminAuthService.logout(accessToken, LoginLogTypeEnum.LOGOUT_CLIENT_DELETE.getType()), OAuth2AccessTokenDTO.class);
    }

    @Override
    public ClientAuthLoginRespDTO clientAuthenticate(String username, String password, LoginLogTypeEnum logType) {
        return adminAuthService.clientAuthenticate(username, password, logType);
    }

    @Override
    public List<OAuth2AccessTokenDTO> getClientOnlineUserList(Integer userType) {
        List<OAuth2AccessTokenDO> onlineUserList = adminAuthService.getClientOnlineUserList(userType);
        return BeanUtils.toBean(onlineUserList, OAuth2AccessTokenDTO.class);
    }

    @Override
    public AuthLoginRespDTO clientCreateNewUserToken(Long userId, String username) {
        AuthLoginRespVO tokenResult = adminAuthService.createTokenAfterLoginSuccess(
                userId,
                username,
                LoginLogTypeEnum.LOGIN_CLIENT,
                OAuth2ClientConstants.CLIENT_CLIENT_ID,
                UserTypeEnum.DESKTOP.getValue()
        );
        return BeanUtils.toBean(tokenResult, AuthLoginRespDTO.class);
    }

    @Override
    public OAuth2AccessTokenDTO clientRefreshAccessToken(String refreshToken) {
        OAuth2AccessTokenDO newToken = oauth2TokenService.refreshAccessToken(refreshToken, OAuth2ClientConstants.CLIENT_CLIENT_ID);
        return BeanUtils.toBean(newToken, OAuth2AccessTokenDTO.class);
    }

}
