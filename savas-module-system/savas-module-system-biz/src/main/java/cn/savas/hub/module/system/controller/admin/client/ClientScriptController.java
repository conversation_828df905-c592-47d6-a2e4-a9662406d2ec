package cn.savas.hub.module.system.controller.admin.client;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.module.system.controller.admin.client.vo.ClientAssignRoleReqVO;
import cn.savas.hub.module.system.controller.admin.client.vo.ClientUpdateScriptReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2025/2/6 15:18
 */
@Tag(name = "管理后台 - 客户端拓展功能")
@RestController
@RequestMapping("/system/client/script")
@Validated
@Slf4j
public class ClientScriptController {
    // 上传客户端升级脚本
    @PostMapping("/upload-update-script")
    @Operation(summary = "上传客户端升级脚本")
    public CommonResult<Boolean> uploadUpdateScript(@RequestBody ClientUpdateScriptReqVO req) {

        return success(true);
    }

    // 赋予角色客户端权限
    @PostMapping("/assign-role-client")
    @Operation(summary = "赋予角色客户端权限")
    @Parameters({
            @Parameter(name = "roleId", description = "角色编号", required = true, example = "1024"),
            @Parameter(name = "menuIds", description = "客户端权限编号", required = true, example = "1024")
    })
    public CommonResult<Boolean> assignRoleClient(@RequestBody ClientAssignRoleReqVO req) {

        return success(true);
    }

}
