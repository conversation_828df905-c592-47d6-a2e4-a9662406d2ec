package cn.savas.hub.module.system.service.auth;

import cn.savas.hub.module.system.api.auth.dto.ClientAuthLoginRespDTO;
import cn.savas.hub.module.system.api.auth.dto.OAuth2AccessTokenDTO;
import cn.savas.hub.module.system.controller.admin.auth.vo.*;
import cn.savas.hub.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.savas.hub.module.system.dal.dataobject.user.AdminUserDO;
import cn.savas.hub.module.system.enums.logger.LoginLogTypeEnum;

import javax.validation.Valid;
import java.util.List;

/**
 * 管理后台的认证 Service 接口
 *
 * 提供用户的登录、登出的能力
 *
 * <AUTHOR>
 */
public interface AdminAuthService {

    /**
     * 验证账号 + 密码。如果通过，则返回用户
     *
     * @param username 账号
     * @param password 密码
     * @return 用户
     */
    AdminUserDO authenticate(String username, String password, LoginLogTypeEnum logType);

    /**
     * 客户端 验证账号 + 密码。如果通过，则返回用户
     */
    ClientAuthLoginRespDTO clientAuthenticate(String username, String password, LoginLogTypeEnum logType);


    /**
     * 账号登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO login(@Valid AuthLoginReqVO reqVO);

    /**
     * 基于 token 退出登录
     *
     * @param token token
     * @param logType 登出类型
     */
    OAuth2AccessTokenDO logout(String token, Integer logType);

    /**
     * 短信验证码发送
     *
     * @param reqVO 发送请求
     */
    void sendSmsCode(AuthSmsSendReqVO reqVO);

    /**
     * 短信登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO smsLogin(AuthSmsLoginReqVO reqVO) ;

    /**
     * 社交快捷登录，使用 code 授权码
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO socialLogin(@Valid AuthSocialLoginReqVO reqVO);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    AuthLoginRespVO refreshToken(String refreshToken);

    /**
     * 创建登录日志
     * @param userId
     * @param username
     * @param logType
     * @return
     */
    AuthLoginRespVO createTokenAfterLoginSuccess(Long userId, String username, LoginLogTypeEnum logType);

    /**
     * 创建登录日志
     * @param userId
     * @param username
     * @param logType
     * @return
     */
    AuthLoginRespVO createTokenAfterLoginSuccess(Long userId, String username, LoginLogTypeEnum logType, String clientId, Integer userType);


    /**
     * 用户注册
     *
     * @param createReqVO 注册用户
     * @return 注册结果
     */
    AuthLoginRespVO register(AuthRegisterReqVO createReqVO);

    /**
     * 验证用户是否在线
     */
    List<OAuth2AccessTokenDO> getAccessTokenListByUser(Long userId, Integer userType);

    /**
     * 获取客户端在线用户
     */
    List<OAuth2AccessTokenDO> getClientOnlineUserList(Integer userType);
}
