package cn.savas.hub.framework.mybatis.core.util;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;
import java.util.UUID;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2025/9/10 17:53
 */
public class SQLiteDataSourceUtil {

    private final DataSource dataSource;
    public SQLiteDataSourceUtil(DataSource dataSource){
        this.dataSource = dataSource;
    }

    /**
     * @param sqliteFilePath sqlite 文件路径
     */
    public <T> T executeWithSQLite(String sqliteFilePath, Supplier<T> supplier) {
        String dsName = "sqlite-" + UUID.randomUUID();
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        try {
            DriverManagerDataSource sqliteDS = new DriverManagerDataSource();
            sqliteDS.setDriverClassName("org.sqlite.JDBC");
            sqliteDS.setUrl("jdbc:sqlite:" + sqliteFilePath);
            ds.addDataSource(dsName, sqliteDS);
            DynamicDataSourceContextHolder.push(dsName);
            return supplier.get();
        }finally {
            // 恢复数据源上下文,移除数据源
            DynamicDataSourceContextHolder.poll();
            ds.removeDataSource(dsName);
        }
    }
}
