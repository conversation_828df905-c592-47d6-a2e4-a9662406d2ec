package cn.savas.hub.framework.common.client.util;

import cn.hutool.core.util.ZipUtil;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 1.4个字节存贮个数
 * 2.4个字节存储key 长度
 * 3.key 长度*2 key值
 * 4.添加4个空白类型
 * 5.10个字节存储value 长度
 * 6.value 转成byte[]
 */

public class V9MapToBinaryUtil {

    public static byte[] convertToBinary(Map<String, BigDecimal> feeMap){
        if (feeMap == null || feeMap.isEmpty()) {
            return new byte[0];
        }
        List<byte[]> list = new ArrayList<>();
        //个数
        byte[] lengthByte = ByteConvertUtil.intToByte(feeMap.size());
        list.add(lengthByte);
        for (Map.Entry<String, BigDecimal> entry : feeMap.entrySet()) {
            //key 长度
            byte[] keyByte =ByteConvertUtil. intToByte(entry.getKey().length());
            list.add(keyByte);
            //key
            byte[] key = ByteConvertUtil.stringToByteAddWhitespace(entry.getKey());
            list.add(key);
            //数据类型//添加4位占位符
            list.add(new byte[]{0, 0, 0, 0});
            //value
            byte[] value = decimalToBytes(entry.getValue());
            list.add(value);
        }
        //合并
        byte[] all = ByteConvertUtil.combineArrays(list);
        //转码//根据实际情况处理
        byte[] bytes = ByteConvertUtil.convertToClient(all);
        //lib 压缩
        return ZipUtil.zlib(bytes, 3);
    }


    /**
     * 总长度10个字节
     *
     * @param x
     * @return
     */
    public static byte[] decimalToBytes(BigDecimal x) {
        byte[] out = new byte[10];
        if (x == null || x.signum() == 0) {
            return out; // 全 0 表示 0.0
        }

        boolean neg = x.signum() < 0;
        BigDecimal v = x.abs();

        MathContext mc = new MathContext(80, java.math.RoundingMode.HALF_EVEN);
        BigDecimal TWO = BigDecimal.valueOf(2);

        // 1. 初始指数估计（double近似）
        int e = Math.getExponent(v.doubleValue());
        BigDecimal pow2abs = TWO.pow(Math.abs(e), mc);
        BigDecimal pow2 = (e >= 0) ? pow2abs : BigDecimal.ONE.divide(pow2abs, mc);
        BigDecimal m = v.divide(pow2, mc);

        // 2. 调整到 [1,2)
        while (m.compareTo(BigDecimal.ONE) < 0) {
            e--;
            m = m.multiply(TWO, mc);
        }
        while (m.compareTo(BigDecimal.valueOf(2)) >= 0) {
            e++;
            m = m.divide(TWO, mc);
        }

        // 3. 计算 64 位尾数（含显式整数位 1）
        BigInteger two63 = BigInteger.ONE.shiftLeft(63);
        BigInteger mant = m.multiply(new BigDecimal(two63, mc), mc)
                .setScale(0, java.math.RoundingMode.HALF_EVEN)
                .toBigInteger();

        // 溢出修正
        if (mant.bitLength() > 64) {
            mant = mant.shiftRight(1);
            e++;
        }

        // 4. 偏移指数
        int biased = e + 16383;
        if (biased <= 0 || biased >= 0x7FFF) {
            throw new ArithmeticException("Exponent out of range for x87 Extended: " + e);
        }

        // 5. 尾数写入低 8 字节（Little-endian）
        for (int i = 0; i < 8; i++) {
            out[i] = (byte) (mant.shiftRight(8 * i).intValue() & 0xFF);
        }

        // 6. 指数与符号写入高 2 字节
        out[8] = (byte) (biased & 0xFF);
        out[9] = (byte) ((biased >> 8) & 0x7F);
        if (neg) {
            out[9] |= (byte) 0x80;
        }

        return out;
    }

}

